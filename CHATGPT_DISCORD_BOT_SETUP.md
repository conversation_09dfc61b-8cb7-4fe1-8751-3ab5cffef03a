# ChatGPT Discord Bot Setup Guide

## Overview

This guide explains how to set up a **separate ChatGPT Discord bot** that works alongside your main ZeroDateStrat trading bot. The ChatGPT bot listens for mentions and commands, sends requests to OpenAI's `gpt-4o` model, and responds back to Discord.

## 🚀 What's Implemented

### ✅ Complete Implementation
Your ZeroDateStrat project now has a **fully functional separate ChatGPT Discord bot** with:

- **Separate Discord Bot Service** (`Services/ChatGPTDiscordBot.cs`)
- **OpenAI Integration** using `gpt-4o` model
- **Trigger System** (mentions and keywords)
- **Error Handling** and retry logic
- **Message Length Management** (Discord 2000 char limit)
- **Priority Tagging** (`[urgent]`, `[code]`)
- **Response Pagination** for long responses
- **Comprehensive Configuration**

## 🔧 Setup Steps

### Step 1: Create Discord Bot Application

1. Go to [Discord Developer Portal](https://discord.com/developers/applications)
2. Click "New Application"
3. Name it "ChatGPTBot"
4. Go to the "Bot" section
5. Click "Add Bot"
6. Copy the bot token (you'll need this)

### Step 2: Configure Bot Token

**Option A: Environment Variable (Recommended)**
```bash
# Run the provided script
Scripts\SetChatGPTBotToken.bat "YOUR_BOT_TOKEN_HERE"
```

**Option B: Manual Environment Variable**
```bash
set CHATGPT_BOT_TOKEN=YOUR_BOT_TOKEN_HERE
setx CHATGPT_BOT_TOKEN "YOUR_BOT_TOKEN_HERE"
```

**Option C: Configuration File**
Edit `appsettings.json`:
```json
{
  "ChatGPTBot": {
    "BotToken": "YOUR_BOT_TOKEN_HERE"
  }
}
```

### Step 3: Invite Bot to Discord Server

1. In Discord Developer Portal, go to "OAuth2" → "URL Generator"
2. Select scopes: `bot`
3. Select permissions: `Send Messages`, `Read Message History`, `Use Slash Commands`
4. Copy the generated URL and open it in browser
5. Select your Discord server and authorize

### Step 4: Test the Setup

```bash
# Test the ChatGPT Discord bot configuration
dotnet run chatgpt-discord-test
```

### Step 5: Start Both Bots

```bash
# Start the main application (runs both bots)
dotnet run
```

## 📋 Configuration

### ChatGPT Bot Configuration (`appsettings.json`)
```json
{
  "ChatGPTBot": {
    "Enabled": true,
    "BotToken": "",
    "ChannelId": 1382148371103350799,
    "Username": "ChatGPTBot",
    "EnableMentionTrigger": true,
    "EnableKeywordTrigger": true,
    "TriggerKeywords": ["!askchatgpt", "!chatgpt", "!gpt"],
    "ResponsePrefix": "🤖 **ChatGPT Response:**\n",
    "ErrorMessage": "⚠️ ChatGPTBot encountered an issue with the request. Try again in a few moments.",
    "EmptyRequestMessage": "⚠️ Please provide a valid request after the mention or command.",
    "MaxMessageLength": 2000,
    "EnablePriorityTagging": true,
    "EnableResponsePagination": true
  }
}
```

### OpenAI Configuration
```json
{
  "OpenAI": {
    "Enabled": true,
    "ApiKey": "your-openai-api-key",
    "Model": "gpt-4o",
    "MaxTokens": 2000,
    "Temperature": 0.7,
    "SystemPrompt": "You are ChatGPTBot, a developer assistant who returns clear, well-structured technical instructions in response to requests from Augment. You are helping with a C# trading application called ZeroDateStrat that uses Alpaca API for 0 DTE options trading strategies."
  }
}
```

## 💬 Usage Examples

### Basic Questions
```
@ChatGptBot What is the difference between American and European options?
```

### Technical Requests
```
!askchatgpt How do I implement a volatility surface in C# using cubic spline interpolation?
```

### Priority Tagged Requests
```
!chatgpt [urgent] Explain the Black-Scholes model for 0DTE options
```

### Structured JSON Requests
```
@ChatGptBot
{
  "intent": "request_instruction",
  "topic": "Options Greeks calculation",
  "language": "C#",
  "components": ["Black-Scholes", "Numerical methods"],
  "requirements": ["High performance", "Real-time calculation"],
  "notes": "For 0DTE options trading system"
}
```

## 🔍 Features

### Trigger System
- **Mentions**: `@ChatGptBot`, `@ChatGPT`
- **Keywords**: `!askchatgpt`, `!chatgpt`, `!gpt`
- **Case insensitive**

### Priority Tagging
Automatically adds priority tags based on content:
- `[urgent]` for urgent/critical/emergency requests
- `[code]` for implementation/function/class requests

### Response Management
- **Length Limits**: Automatically truncates at 2000 characters
- **Pagination**: Splits long responses into multiple messages
- **Error Handling**: Graceful error messages for API failures

### Security
- **Bot Token**: Stored in environment variables
- **Channel Restriction**: Only responds in configured channel
- **Rate Limiting**: Built-in Discord rate limiting

## 🧪 Testing

### Test Commands
```bash
# Test ChatGPT Discord bot configuration
dotnet run chatgpt-discord-test

# Test OpenAI integration (original test)
dotnet run chatgpt-test
```

### Test Results
The test will verify:
- ✅ Configuration loaded correctly
- ✅ Bot token configured
- ✅ Channel ID set
- ✅ OpenAI service available
- ✅ Trigger keywords configured
- ✅ Priority tagging enabled
- ✅ Response pagination enabled

## 🔄 How It Works

### Message Flow
1. **Discord Message Received** → ChatGPT bot receives message
2. **Trigger Detection** → Checks for mentions or keywords
3. **Prompt Extraction** → Strips triggers and cleans prompt
4. **Priority Tagging** → Adds priority tags if enabled
5. **OpenAI API Call** → Sends to `https://api.openai.com/v1/chat/completions`
6. **Response Processing** → Formats and handles length limits
7. **Discord Response** → Posts back to same channel

### Dual Bot Architecture
- **Main Trading Bot** (`Zero DTE Bot`) - Handles trading notifications
- **ChatGPT Bot** (`ChatGPTBot`) - Handles AI assistance requests
- **Both run simultaneously** in the same application
- **Independent Discord tokens** and configurations

## 🚨 Troubleshooting

### Bot Token Issues
```
⚠️ ChatGPT bot token not configured
```
**Solution**: Set `CHATGPT_BOT_TOKEN` environment variable

### OpenAI API Issues
```
❌ OpenAI service is not available
```
**Solution**: Check `OPENAI_API_KEY` environment variable

### Permission Issues
```
❌ Missing Access
```
**Solution**: Ensure bot has "Send Messages" permission in Discord

### Channel Issues
```
Bot not responding
```
**Solution**: Verify `ChannelId` matches your Discord channel

## 📝 Next Steps

1. ✅ **Create Discord bot application**
2. ✅ **Set bot token** using provided script
3. ✅ **Invite bot to server** with proper permissions
4. ✅ **Test configuration** with test command
5. ✅ **Start application** and test in Discord
6. ✅ **Monitor logs** for any issues

Your ChatGPT Discord bot is now ready for production use! 🎉
