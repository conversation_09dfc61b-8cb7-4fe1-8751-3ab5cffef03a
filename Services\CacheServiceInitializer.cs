using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;

namespace ZeroDateStrat.Services;

// Service to initialize cache dependencies after DI container is built
public class CacheServiceInitializer : IHostedService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<CacheServiceInitializer> _logger;

    public CacheServiceInitializer(IServiceProvider serviceProvider, ILogger<CacheServiceInitializer> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    public Task StartAsync(CancellationToken cancellationToken)
    {
        try
        {
            // Wire up the cache service dependencies
            var cacheService = _serviceProvider.GetRequiredService<IHighPerformanceCacheService>();
            var analyticsService = _serviceProvider.GetRequiredService<ICacheAnalyticsService>();
            var predictiveService = _serviceProvider.GetRequiredService<IPredictiveCachingService>();

            if (cacheService is HighPerformanceCacheService concreteCache)
            {
                concreteCache.SetAnalyticsService(analyticsService);
                concreteCache.SetPredictiveService(predictiveService);
                _logger.LogInformation("✅ Cache service dependencies wired up successfully");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize cache service dependencies");
        }

        return Task.CompletedTask;
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }
}
