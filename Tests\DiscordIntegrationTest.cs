using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using ZeroDateStrat.Models;
using ZeroDateStrat.Services;

namespace ZeroDateStrat.Tests;

public static class DiscordIntegrationTest
{
    public static async Task RunDiscordIntegrationTest()
    {
        Console.WriteLine("🤖 Starting Discord Integration Test...");
        Console.WriteLine(new string('=', 50));

        try
        {
            // Setup configuration
            var configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json", optional: false)
                .Build();

            // Setup logging
            var logger = new LoggerConfiguration()
                .WriteTo.Console()
                .CreateLogger();

            // Setup services
            var services = new ServiceCollection();
            services.AddLogging(builder => builder.AddSerilog(logger));
            services.AddSingleton<IConfiguration>(configuration);
            services.AddSingleton<INotificationService, NotificationService>();
            services.AddSingleton<IDiscordService, DiscordService>();
            services.AddSingleton<IDiscordCommandHandler, DiscordCommandHandler>();

            var serviceProvider = services.BuildServiceProvider();

            // Get services
            var notificationService = serviceProvider.GetRequiredService<INotificationService>();
            var discordService = serviceProvider.GetRequiredService<IDiscordService>();

            Console.WriteLine("✅ Services initialized successfully");

            // Test 1: Configuration Validation
            Console.WriteLine("\n📋 Test 1: Discord Configuration Validation");
            var channels = await notificationService.GetAvailableChannelsAsync();
            var discordChannel = channels.FirstOrDefault(c => c.Type == "Discord");
            
            if (discordChannel != null)
            {
                Console.WriteLine($"✅ Discord channel found - Enabled: {discordChannel.IsEnabled}");
                Console.WriteLine($"   Priority: {discordChannel.Priority}");
                
                foreach (var config in discordChannel.Configuration)
                {
                    var value = string.IsNullOrEmpty(config.Value) ? "[Not Set]" : 
                               config.Key.Contains("Token") ? "[Hidden]" : config.Value;
                    Console.WriteLine($"   {config.Key}: {value}");
                }
            }
            else
            {
                Console.WriteLine("❌ Discord channel not found in configuration");
            }

            // Test 2: Discord Configuration Test
            Console.WriteLine("\n🔧 Test 2: Discord Configuration Test");
            try
            {
                var configTest = await notificationService.TestDiscordConfigurationAsync();
                Console.WriteLine($"Discord configuration test: {(configTest ? "✅ PASSED" : "❌ FAILED")}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Discord configuration test failed: {ex.Message}");
            }

            // Test 3: Send Test Alert via Discord
            Console.WriteLine("\n📨 Test 3: Send Test Alert via Discord");
            try
            {
                var testAlert = new RiskAlert
                {
                    Id = "DISCORD_TEST_001",
                    Type = "DiscordIntegrationTest",
                    Severity = RiskLevel.Low,
                    Message = "This is a test Discord alert from the Zero DTE Trading System integration test.",
                    Timestamp = DateTime.UtcNow,
                    Value = 1234.56m,
                    Threshold = 1000.00m,
                    CreatedTime = DateTime.UtcNow,
                    ExpiryTime = DateTime.UtcNow.AddHours(1)
                };

                var alertSent = await notificationService.SendDiscordAlertAsync(testAlert);
                Console.WriteLine($"Discord alert test: {(alertSent ? "✅ SENT" : "❌ FAILED")}");
                
                if (alertSent)
                {
                    Console.WriteLine("   Alert details:");
                    Console.WriteLine($"   - ID: {testAlert.Id}");
                    Console.WriteLine($"   - Type: {testAlert.Type}");
                    Console.WriteLine($"   - Severity: {testAlert.Severity}");
                    Console.WriteLine($"   - Message: {testAlert.Message}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Discord alert test failed: {ex.Message}");
            }

            // Test 4: Discord Service Connection Test
            Console.WriteLine("\n🔗 Test 4: Discord Service Connection Test");
            try
            {
                var isConnected = await discordService.IsConnectedAsync();
                Console.WriteLine($"Discord service connection: {(isConnected ? "✅ CONNECTED" : "⚠️ NOT CONNECTED")}");
                
                if (!isConnected)
                {
                    Console.WriteLine("   Note: Discord bot service may not be enabled or configured.");
                    Console.WriteLine("   This is normal if you haven't set up a Discord bot token and channel ID.");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Discord service connection test failed: {ex.Message}");
            }

            // Test 5: Available Notification Channels
            Console.WriteLine("\n📢 Test 5: Available Notification Channels");
            try
            {
                var allChannels = await notificationService.GetAvailableChannelsAsync();
                Console.WriteLine($"Total notification channels: {allChannels.Count}");
                
                foreach (var channel in allChannels.OrderBy(c => c.Priority))
                {
                    var status = channel.IsEnabled ? "🟢 Enabled" : "🔴 Disabled";
                    Console.WriteLine($"   {channel.Type} (Priority {channel.Priority}): {status}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Failed to get notification channels: {ex.Message}");
            }

            // Test 6: Notification Configuration Validation
            Console.WriteLine("\n✅ Test 6: Notification Configuration Validation");
            try
            {
                var configValid = await notificationService.ValidateNotificationConfigurationAsync();
                Console.WriteLine($"Notification configuration validation: {(configValid ? "✅ VALID" : "❌ INVALID")}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Notification configuration validation failed: {ex.Message}");
            }

            Console.WriteLine("\n" + new string('=', 50));
            Console.WriteLine("🎉 Discord Integration Test Completed!");
            Console.WriteLine("\n📝 Setup Instructions:");
            Console.WriteLine("To enable Discord notifications:");
            Console.WriteLine("1. Create a Discord bot at https://discord.com/developers/applications");
            Console.WriteLine("2. Get the bot token and add it to appsettings.json");
            Console.WriteLine("3. Invite the bot to your Discord server");
            Console.WriteLine("4. Get the channel ID and add it to appsettings.json");
            Console.WriteLine("5. Set 'Enabled': true in the Discord configuration");
            Console.WriteLine("\nAlternatively, you can use a Discord webhook URL instead of a bot token.");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Discord Integration Test failed with exception: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
    }
}
