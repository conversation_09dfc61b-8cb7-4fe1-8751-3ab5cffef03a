using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Text.Json;
using Discord.WebSocket;
using ZeroDateStrat.Models;

namespace ZeroDateStrat.Services;

/// <summary>
/// Service for requesting external guidance from @ChatGptBot in Discord
/// </summary>
public class GuidanceRequestService : IGuidanceRequestService
{
    private readonly IDiscordService _discordService;
    private readonly ILogger<GuidanceRequestService> _logger;
    private readonly GuidanceRequestConfiguration _config;
    
    // Thread-safe collections for managing requests and responses
    private readonly ConcurrentDictionary<string, GuidanceRequest> _pendingRequests = new();
    private readonly ConcurrentDictionary<string, GuidanceResponse> _storedResponses = new();
    private readonly ConcurrentDictionary<string, TaskCompletionSource<GuidanceResponse?>> _responseWaiters = new();

    public event Func<GuidanceResponse, Task>? OnGuidanceResponseReceived;
    public event Func<GuidanceRequest, Task>? OnGuidanceRequestTimeout;
    public event Func<GuidanceRequest, Exception, Task>? OnGuidanceRequestFailed;

    public GuidanceRequestService(
        IDiscordService discordService,
        IConfiguration configuration,
        ILogger<GuidanceRequestService> logger)
    {
        _discordService = discordService;
        _logger = logger;
        
        // Load configuration
        _config = new GuidanceRequestConfiguration();
        configuration.GetSection("GuidanceRequest").Bind(_config);
        
        // Subscribe to Discord message events to capture responses from ChatGptBot
        _discordService.OnChatGptBotMessageReceived += HandleChatGptBotMessageAsync;
        
        _logger.LogInformation("GuidanceRequestService initialized with ChatGptBot mention: {Mention}", 
            _config.ChatGptBotMention);
    }

    public async Task<GuidanceResponse?> RequestGuidanceAsync(
        string taskStatement,
        string language = "C#",
        List<string>? components = null,
        string notes = "",
        CancellationToken cancellationToken = default)
    {
        var request = new GuidanceRequest
        {
            TaskStatement = taskStatement,
            Language = language,
            Components = components ?? new List<string>(),
            Notes = notes,
            Format = GuidanceRequestFormat.Plaintext
        };

        return await SendGuidanceRequestAsync(request, cancellationToken);
    }

    public async Task<GuidanceResponse?> RequestStructuredGuidanceAsync(
        StructuredGuidanceRequest request,
        CancellationToken cancellationToken = default)
    {
        var guidanceRequest = new GuidanceRequest
        {
            TaskStatement = request.Topic,
            Language = request.Language,
            Components = request.Components,
            Notes = request.Notes,
            TechnicalRequirements = request.Requirements,
            Format = GuidanceRequestFormat.StructuredJson
        };

        return await SendGuidanceRequestAsync(guidanceRequest, cancellationToken);
    }

    public async Task<GuidanceResponse?> SendGuidanceRequestAsync(
        GuidanceRequest guidanceRequest,
        CancellationToken cancellationToken = default)
    {
        if (!_config.Enabled)
        {
            _logger.LogWarning("Guidance request service is disabled");
            return null;
        }

        try
        {
            _logger.LogInformation("Sending guidance request: {RequestId} - {Task}", 
                guidanceRequest.Id, guidanceRequest.TaskStatement);

            // Store the pending request
            _pendingRequests[guidanceRequest.Id] = guidanceRequest;
            
            // Create a task completion source to wait for the response
            var responseWaiter = new TaskCompletionSource<GuidanceResponse?>();
            _responseWaiters[guidanceRequest.Id] = responseWaiter;

            // Format and send the message
            var message = FormatGuidanceRequestMessage(guidanceRequest);
            await _discordService.SendGuidanceRequestAsync(message);
            
            guidanceRequest.SentAt = DateTime.UtcNow;
            guidanceRequest.Status = GuidanceRequestStatus.Sent;

            // Wait for response with timeout
            using var timeoutCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
            timeoutCts.CancelAfter(guidanceRequest.ResponseTimeout);

            try
            {
                var response = await responseWaiter.Task.WaitAsync(timeoutCts.Token);
                
                if (response != null)
                {
                    guidanceRequest.Status = GuidanceRequestStatus.ResponseReceived;
                    guidanceRequest.ResponseReceivedAt = DateTime.UtcNow;
                    guidanceRequest.Response = response.Content;
                    
                    // Store response if configured
                    if (_config.StoreResponses)
                    {
                        _storedResponses[guidanceRequest.Id] = response;
                        await SaveResponseToDiskAsync(response);
                    }
                    
                    _logger.LogInformation("Received guidance response for request: {RequestId}", guidanceRequest.Id);
                    return response;
                }
            }
            catch (OperationCanceledException) when (timeoutCts.Token.IsCancellationRequested)
            {
                // Handle timeout
                guidanceRequest.Status = GuidanceRequestStatus.Timeout;
                guidanceRequest.ErrorMessage = "Request timed out waiting for response";
                
                _logger.LogWarning("Guidance request timed out: {RequestId}", guidanceRequest.Id);
                
                if (OnGuidanceRequestTimeout != null)
                    await OnGuidanceRequestTimeout(guidanceRequest);
                
                // Retry logic
                if (_config.EnableRetryLogic && guidanceRequest.RetryCount < guidanceRequest.MaxRetries)
                {
                    guidanceRequest.RetryCount++;
                    _logger.LogInformation("Retrying guidance request: {RequestId} (Attempt {Retry}/{Max})", 
                        guidanceRequest.Id, guidanceRequest.RetryCount, guidanceRequest.MaxRetries);
                    
                    return await SendGuidanceRequestAsync(guidanceRequest, cancellationToken);
                }
            }

            return null;
        }
        catch (Exception ex)
        {
            guidanceRequest.Status = GuidanceRequestStatus.Failed;
            guidanceRequest.ErrorMessage = ex.Message;
            
            _logger.LogError(ex, "Failed to send guidance request: {RequestId}", guidanceRequest.Id);
            
            if (OnGuidanceRequestFailed != null)
                await OnGuidanceRequestFailed(guidanceRequest, ex);
            
            return null;
        }
        finally
        {
            // Cleanup
            _pendingRequests.TryRemove(guidanceRequest.Id, out _);
            _responseWaiters.TryRemove(guidanceRequest.Id, out _);
        }
    }

    private string FormatGuidanceRequestMessage(GuidanceRequest request)
    {
        if (request.Format == GuidanceRequestFormat.StructuredJson)
        {
            var structuredRequest = new StructuredGuidanceRequest
            {
                Topic = request.TaskStatement,
                Language = request.Language,
                Components = request.Components,
                Notes = request.Notes,
                Requirements = request.TechnicalRequirements
            };

            var json = JsonSerializer.Serialize(structuredRequest, new JsonSerializerOptions 
            { 
                WriteIndented = true 
            });

            return $"{_config.ChatGptBotMention}\n```json\n{json}\n```";
        }
        else
        {
            var message = $"{_config.ChatGptBotMention} {request.TaskStatement}";
            
            if (request.Components.Any())
            {
                message += $" Components: {string.Join(", ", request.Components)}.";
            }
            
            if (!string.IsNullOrEmpty(request.Notes))
            {
                message += $" {request.Notes}";
            }
            
            return message;
        }
    }

    private async Task HandleChatGptBotMessageAsync(Discord.WebSocket.SocketMessage message)
    {
        try
        {
            var messageContent = message.Content?.Trim();
            if (string.IsNullOrWhiteSpace(messageContent))
                return;

            _logger.LogInformation("Received message from ChatGptBot: {Content}",
                messageContent.Substring(0, Math.Min(100, messageContent.Length)));

            var pendingRequestIds = _pendingRequests.Keys.ToList();
            if (!pendingRequestIds.Any())
            {
                _logger.LogDebug("No pending guidance requests to match response");
                return;
            }

            // Match the response to the oldest pending request
            // In a more sophisticated implementation, you could use message context,
            // timing, or other factors to better match responses to requests
            var oldestRequestId = pendingRequestIds
                .Select(id => new { Id = id, Request = _pendingRequests[id] })
                .OrderBy(x => x.Request.SentAt)
                .FirstOrDefault()?.Id;

            if (oldestRequestId != null && _responseWaiters.TryGetValue(oldestRequestId, out var waiter))
            {
                var response = new GuidanceResponse
                {
                    RequestId = oldestRequestId,
                    Content = messageContent,
                    ReceivedAt = DateTime.UtcNow,
                    AuthorId = message.Author.Id.ToString(),
                    AuthorUsername = message.Author.Username,
                    MessageId = message.Id,
                    IsFromChatGptBot = true
                };

                waiter.SetResult(response);

                _logger.LogInformation("Matched guidance response to request: {RequestId}", oldestRequestId);

                if (OnGuidanceResponseReceived != null)
                    await OnGuidanceResponseReceived(response);
            }
            else
            {
                _logger.LogWarning("Could not match ChatGptBot response to any pending request");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling ChatGptBot message for guidance response");
        }
    }

    private async Task SaveResponseToDiskAsync(GuidanceResponse response)
    {
        try
        {
            if (!Directory.Exists(_config.ResponseStoragePath))
                Directory.CreateDirectory(_config.ResponseStoragePath);

            var fileName = $"guidance_response_{response.RequestId}_{DateTime.UtcNow:yyyyMMdd_HHmmss}.json";
            var filePath = Path.Combine(_config.ResponseStoragePath, fileName);
            
            var json = JsonSerializer.Serialize(response, new JsonSerializerOptions { WriteIndented = true });
            await File.WriteAllTextAsync(filePath, json);
            
            _logger.LogDebug("Saved guidance response to disk: {FilePath}", filePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save guidance response to disk");
        }
    }

    public Task<GuidanceRequestStatus> GetRequestStatusAsync(string requestId)
    {
        if (_pendingRequests.TryGetValue(requestId, out var request))
            return Task.FromResult(request.Status);
        
        if (_storedResponses.ContainsKey(requestId))
            return Task.FromResult(GuidanceRequestStatus.ResponseReceived);
        
        return Task.FromResult(GuidanceRequestStatus.Failed);
    }

    public Task<GuidanceResponse?> GetStoredResponseAsync(string requestId)
    {
        _storedResponses.TryGetValue(requestId, out var response);
        return Task.FromResult(response);
    }

    public Task<List<GuidanceResponse>> GetAllStoredResponsesAsync()
    {
        return Task.FromResult(_storedResponses.Values.ToList());
    }

    public Task<bool> CancelRequestAsync(string requestId)
    {
        if (_pendingRequests.TryRemove(requestId, out var request))
        {
            request.Status = GuidanceRequestStatus.Cancelled;
            
            if (_responseWaiters.TryRemove(requestId, out var waiter))
            {
                waiter.SetCanceled();
            }
            
            _logger.LogInformation("Cancelled guidance request: {RequestId}", requestId);
            return Task.FromResult(true);
        }
        
        return Task.FromResult(false);
    }

    public Task<int> CleanupOldResponsesAsync(TimeSpan olderThan)
    {
        var cutoffTime = DateTime.UtcNow - olderThan;
        var toRemove = _storedResponses
            .Where(kvp => kvp.Value.ReceivedAt < cutoffTime)
            .Select(kvp => kvp.Key)
            .ToList();

        var removedCount = 0;
        foreach (var key in toRemove)
        {
            if (_storedResponses.TryRemove(key, out _))
                removedCount++;
        }

        _logger.LogInformation("Cleaned up {Count} old guidance responses", removedCount);
        return Task.FromResult(removedCount);
    }
}
