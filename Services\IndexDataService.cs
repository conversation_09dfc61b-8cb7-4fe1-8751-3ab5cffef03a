using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using ZeroDateStrat.Models;

namespace ZeroDateStrat.Services;

/// <summary>
/// Comprehensive index data service that implements the three-tier hierarchy for all major indices:
/// Primary: Polygon Indices → Secondary: Polygon Markets → Tertiary: Alpaca/ETF Proxies
/// </summary>
public interface IIndexDataService
{
    Task<decimal> GetCurrentIndexValueAsync(string indexSymbol);
    Task<IndexQuote> GetIndexQuoteAsync(string indexSymbol);
    Task<List<IndexDataPoint>> GetIndexHistoryAsync(string indexSymbol, DateTime startDate, DateTime endDate, string timespan = "day");
    Task<bool> TestConnectionAsync();
    Task<Dictionary<string, decimal>> GetAllMajorIndicesAsync();
    Task<IndexDataSourceStatus> GetDataSourceStatusAsync(string indexSymbol);
}

public class IndexDataService : IIndexDataService
{
    private readonly IPolygonDataService _polygonDataService;
    private readonly IAlpacaService _alpacaService;
    private readonly ISyntheticVixService _syntheticVixService;
    private readonly IConfiguration _configuration;
    private readonly ILogger<IndexDataService> _logger;

    // Cache for index values
    private readonly Dictionary<string, decimal> _cachedValues = new();
    private readonly Dictionary<string, DateTime> _lastUpdates = new();
    private readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(1);

    // Index mappings from configuration
    private readonly Dictionary<string, IndexMapping> _indexMappings;

    public IndexDataService(
        IPolygonDataService polygonDataService,
        IAlpacaService alpacaService,
        ISyntheticVixService syntheticVixService,
        IConfiguration configuration,
        ILogger<IndexDataService> logger)
    {
        _polygonDataService = polygonDataService;
        _alpacaService = alpacaService;
        _syntheticVixService = syntheticVixService;
        _configuration = configuration;
        _logger = logger;

        // Load index mappings from configuration
        _indexMappings = LoadIndexMappings();
    }

    public async Task<decimal> GetCurrentIndexValueAsync(string indexSymbol)
    {
        try
        {
            // Check cache first
            if (_cachedValues.ContainsKey(indexSymbol) && 
                _lastUpdates.ContainsKey(indexSymbol) &&
                DateTime.UtcNow - _lastUpdates[indexSymbol] < _cacheExpiry)
            {
                _logger.LogDebug($"Returning cached {indexSymbol}: {_cachedValues[indexSymbol]:F2}");
                return _cachedValues[indexSymbol];
            }

            _logger.LogDebug($"Getting {indexSymbol} data - Primary: Polygon, Secondary: Polygon Markets, Tertiary: Alpaca/ETF");

            if (!_indexMappings.ContainsKey(indexSymbol))
            {
                _logger.LogWarning($"No mapping found for index {indexSymbol}, using direct symbol");
                return await GetDirectSymbolValueAsync(indexSymbol);
            }

            var mapping = _indexMappings[indexSymbol];

            // Primary: Use Polygon Indices
            var primaryValue = await GetPolygonIndexValueAsync(mapping.Primary);
            if (primaryValue > 0)
            {
                _cachedValues[indexSymbol] = primaryValue;
                _lastUpdates[indexSymbol] = DateTime.UtcNow;
                _logger.LogInformation($"Primary {indexSymbol} from Polygon: {primaryValue:F2}");
                return primaryValue;
            }

            // Secondary: Try Polygon Markets alternative endpoints
            var secondaryValue = await GetPolygonMarketsValueAsync(mapping.Secondary);
            if (secondaryValue > 0)
            {
                _cachedValues[indexSymbol] = secondaryValue;
                _lastUpdates[indexSymbol] = DateTime.UtcNow;
                _logger.LogInformation($"Secondary {indexSymbol} from Polygon Markets: {secondaryValue:F2}");
                return secondaryValue;
            }

            // Tertiary: Use Alpaca/ETF proxy
            var tertiaryValue = await GetTertiaryValueAsync(indexSymbol, mapping.Tertiary);
            if (tertiaryValue > 0)
            {
                _cachedValues[indexSymbol] = tertiaryValue;
                _lastUpdates[indexSymbol] = DateTime.UtcNow;
                _logger.LogInformation($"Tertiary {indexSymbol} from {mapping.Tertiary}: {tertiaryValue:F2}");
                return tertiaryValue;
            }

            // Emergency fallback
            var fallbackValue = GetEmergencyFallbackValue(indexSymbol);
            _logger.LogWarning($"All {indexSymbol} sources failed, using emergency fallback: {fallbackValue:F2}");
            return fallbackValue;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error getting current {indexSymbol} value");
            return GetEmergencyFallbackValue(indexSymbol);
        }
    }

    public async Task<IndexQuote> GetIndexQuoteAsync(string indexSymbol)
    {
        try
        {
            var value = await GetCurrentIndexValueAsync(indexSymbol);
            return new IndexQuote
            {
                Symbol = indexSymbol,
                Value = value,
                Bid = value,
                Ask = value,
                Timestamp = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error getting {indexSymbol} quote");
            return new IndexQuote { Symbol = indexSymbol };
        }
    }

    public async Task<List<IndexDataPoint>> GetIndexHistoryAsync(string indexSymbol, DateTime startDate, DateTime endDate, string timespan = "day")
    {
        try
        {
            if (!_indexMappings.ContainsKey(indexSymbol))
            {
                _logger.LogWarning($"No mapping found for index {indexSymbol} history");
                return new List<IndexDataPoint>();
            }

            var mapping = _indexMappings[indexSymbol];

            // Try Polygon first
            if (_polygonDataService != null)
            {
                try
                {
                    if (indexSymbol == "VIX")
                    {
                        var vixHistory = await _polygonDataService.GetVixHistoryAsync(startDate, endDate, timespan);
                        return vixHistory.Select(v => new IndexDataPoint
                        {
                            Symbol = indexSymbol,
                            Date = v.Date,
                            Value = v.Value,
                            Open = v.Open,
                            High = v.High,
                            Low = v.Low,
                            Volume = v.Volume
                        }).ToList();
                    }
                    else
                    {
                        var indexAggregates = await _polygonDataService.GetIndexAggregatesAsync(mapping.Primary, startDate, endDate, timespan);
                        return indexAggregates.Results.Select(r => new IndexDataPoint
                        {
                            Symbol = indexSymbol,
                            Date = r.Date,
                            Value = r.Close,
                            Open = r.Open,
                            High = r.High,
                            Low = r.Low,
                            Volume = r.Volume
                        }).ToList();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogDebug(ex, $"Polygon history failed for {indexSymbol}");
                }
            }

            // Fallback to empty list
            _logger.LogWarning($"No historical data available for {indexSymbol}");
            return new List<IndexDataPoint>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error getting {indexSymbol} history");
            return new List<IndexDataPoint>();
        }
    }

    public async Task<bool> TestConnectionAsync()
    {
        try
        {
            _logger.LogInformation("Testing index data service connections for all major indices...");

            var testResults = new Dictionary<string, bool>();

            foreach (var mapping in _indexMappings)
            {
                try
                {
                    var value = await GetCurrentIndexValueAsync(mapping.Key);
                    testResults[mapping.Key] = value > 0;
                    _logger.LogInformation($"✅ {mapping.Key}: {value:F2}");
                }
                catch (Exception ex)
                {
                    testResults[mapping.Key] = false;
                    _logger.LogWarning($"❌ {mapping.Key}: {ex.Message}");
                }
            }

            var successCount = testResults.Values.Count(r => r);
            var totalCount = testResults.Count;

            _logger.LogInformation($"Index data service test: {successCount}/{totalCount} indices successful");
            return successCount > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Index data service connection test failed");
            return false;
        }
    }

    public async Task<Dictionary<string, decimal>> GetAllMajorIndicesAsync()
    {
        var results = new Dictionary<string, decimal>();

        foreach (var mapping in _indexMappings)
        {
            try
            {
                var value = await GetCurrentIndexValueAsync(mapping.Key);
                results[mapping.Key] = value;
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, $"Failed to get {mapping.Key} value");
                results[mapping.Key] = 0;
            }
        }

        return results;
    }

    public async Task<IndexDataSourceStatus> GetDataSourceStatusAsync(string indexSymbol)
    {
        var status = new IndexDataSourceStatus { Symbol = indexSymbol };

        if (!_indexMappings.ContainsKey(indexSymbol))
        {
            status.Status = "Unknown symbol";
            return status;
        }

        var mapping = _indexMappings[indexSymbol];

        // Test primary source
        try
        {
            var primaryValue = await GetPolygonIndexValueAsync(mapping.Primary);
            status.PrimarySourceStatus = primaryValue > 0 ? "✅ Available" : "❌ No data";
            status.PrimarySourceValue = primaryValue;
        }
        catch (Exception ex)
        {
            status.PrimarySourceStatus = $"❌ Error: {ex.Message}";
        }

        // Test secondary source
        try
        {
            var secondaryValue = await GetPolygonMarketsValueAsync(mapping.Secondary);
            status.SecondarySourceStatus = secondaryValue > 0 ? "✅ Available" : "❌ No data";
            status.SecondarySourceValue = secondaryValue;
        }
        catch (Exception ex)
        {
            status.SecondarySourceStatus = $"❌ Error: {ex.Message}";
        }

        // Test tertiary source
        try
        {
            var tertiaryValue = await GetTertiaryValueAsync(indexSymbol, mapping.Tertiary);
            status.TertiarySourceStatus = tertiaryValue > 0 ? "✅ Available" : "❌ No data";
            status.TertiarySourceValue = tertiaryValue;
        }
        catch (Exception ex)
        {
            status.TertiarySourceStatus = $"❌ Error: {ex.Message}";
        }

        status.Status = "Complete";
        return status;
    }

    private async Task<decimal> GetPolygonIndexValueAsync(string polygonSymbol)
    {
        if (_polygonDataService == null) return 0;

        try
        {
            if (polygonSymbol == "I:VIX")
            {
                return await _polygonDataService.GetCurrentVixAsync();
            }
            else
            {
                var quote = await _polygonDataService.GetCurrentQuoteAsync(polygonSymbol);
                return quote?.Bid ?? 0;
            }
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, $"Polygon index value failed for {polygonSymbol}");
            return 0;
        }
    }

    private async Task<decimal> GetPolygonMarketsValueAsync(string polygonSymbol)
    {
        if (_polygonDataService == null) return 0;

        try
        {
            // Try alternative Polygon methods
            var yesterday = DateTime.UtcNow.AddDays(-1);
            
            if (polygonSymbol == "I:VIX")
            {
                var vixHistory = await _polygonDataService.GetVixHistoryAsync(yesterday, DateTime.UtcNow);
                if (vixHistory.Any())
                {
                    return vixHistory.OrderByDescending(v => v.Date).First().Value;
                }
            }
            else
            {
                var indexAggregates = await _polygonDataService.GetIndexAggregatesAsync(polygonSymbol, yesterday, DateTime.UtcNow, "minute");
                if (indexAggregates.Results.Any())
                {
                    return indexAggregates.Results.OrderByDescending(r => r.Date).First().Close;
                }
            }

            return 0;
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, $"Polygon markets value failed for {polygonSymbol}");
            return 0;
        }
    }

    private async Task<decimal> GetTertiaryValueAsync(string indexSymbol, string tertiarySymbol)
    {
        try
        {
            if (indexSymbol == "VIX" && tertiarySymbol == "SyntheticVIX")
            {
                return await _syntheticVixService.GetCurrentSyntheticVixAsync();
            }
            else if (_alpacaService != null)
            {
                return await _alpacaService.GetCurrentPriceAsync(tertiarySymbol);
            }

            return 0;
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, $"Tertiary value failed for {indexSymbol} -> {tertiarySymbol}");
            return 0;
        }
    }

    private async Task<decimal> GetDirectSymbolValueAsync(string symbol)
    {
        if (_alpacaService != null)
        {
            return await _alpacaService.GetCurrentPriceAsync(symbol);
        }
        return 0;
    }

    private decimal GetEmergencyFallbackValue(string indexSymbol)
    {
        return indexSymbol switch
        {
            "VIX" => 20.0m,
            "SPX" => 6000m,
            "NDX" => 21000m,
            "RUT" => 2400m,
            "DJI" => 45000m,
            _ => 100m
        };
    }

    private Dictionary<string, IndexMapping> LoadIndexMappings()
    {
        var mappings = new Dictionary<string, IndexMapping>();

        try
        {
            var indexMappingsConfig = _configuration.GetSection("DataSources:IndexMappings");
            foreach (var section in indexMappingsConfig.GetChildren())
            {
                var mapping = new IndexMapping
                {
                    Primary = section["Primary"] ?? "",
                    Secondary = section["Secondary"] ?? "",
                    Tertiary = section["Tertiary"] ?? "",
                    Description = section["Description"] ?? ""
                };
                mappings[section.Key] = mapping;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading index mappings from configuration");
        }

        return mappings;
    }
}

// Supporting models
public class IndexMapping
{
    public string Primary { get; set; } = "";
    public string Secondary { get; set; } = "";
    public string Tertiary { get; set; } = "";
    public string Description { get; set; } = "";
}

public class IndexQuote
{
    public string Symbol { get; set; } = "";
    public decimal Value { get; set; }
    public decimal Bid { get; set; }
    public decimal Ask { get; set; }
    public DateTime Timestamp { get; set; }
}

public class IndexDataPoint
{
    public string Symbol { get; set; } = "";
    public DateTime Date { get; set; }
    public decimal Value { get; set; }
    public decimal Open { get; set; }
    public decimal High { get; set; }
    public decimal Low { get; set; }
    public long Volume { get; set; }
}

public class IndexDataSourceStatus
{
    public string Symbol { get; set; } = "";
    public string Status { get; set; } = "";
    public string PrimarySourceStatus { get; set; } = "";
    public decimal PrimarySourceValue { get; set; }
    public string SecondarySourceStatus { get; set; } = "";
    public decimal SecondarySourceValue { get; set; }
    public string TertiarySourceStatus { get; set; } = "";
    public decimal TertiarySourceValue { get; set; }
}
