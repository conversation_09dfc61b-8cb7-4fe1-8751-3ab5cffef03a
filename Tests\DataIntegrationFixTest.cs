using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Serilog;
using ZeroDateStrat.Services;

namespace ZeroDateStrat.Tests;

public static class DataIntegrationFixTest
{
    private static ServiceProvider? _serviceProvider;
    private static Microsoft.Extensions.Logging.ILogger? _logger;

    public static async Task RunDataIntegrationFixTest()
    {
        Console.WriteLine("=== Data Integration Fix Test ===\n");

        // Setup
        SetupServices();
        _logger = _serviceProvider!.GetRequiredService<ILoggerFactory>().CreateLogger("DataIntegrationFixTest");

        try
        {
            // Test 1: Historical Data Service Improvements
            await TestHistoricalDataService();

            // Test 2: Market Regime Analyzer Data Handling
            await TestMarketRegimeAnalyzerDataHandling();

            // Test 3: Volatility Calculations with Fallbacks
            await TestVolatilityCalculationsWithFallbacks();

            // Test 4: Synthetic Data Generation Quality
            await TestSyntheticDataQuality();

            Console.WriteLine("\n=== Data Integration Fix Test Completed Successfully ===");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"\n❌ Test failed with error: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
        finally
        {
            _serviceProvider?.Dispose();
        }
    }

    private static void SetupServices()
    {
        // Configure Serilog
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Debug()
            .WriteTo.Console()
            .WriteTo.File($"logs/data-integration-fix-test-{DateTime.Now:yyyyMMdd}.txt")
            .CreateLogger();

        // Build configuration
        var configuration = new ConfigurationBuilder()
            .AddJsonFile("appsettings.json", optional: true)
            .AddEnvironmentVariables()
            .Build();

        // Setup DI container
        var services = new ServiceCollection();
        services.AddSingleton<IConfiguration>(configuration);
        services.AddLogging(builder => builder.AddSerilog());
        services.AddSingleton<ISecurityService, SecurityService>();
        services.AddSingleton<IConfigurationValidator, ConfigurationValidator>();
        services.AddSingleton<IGlobalExceptionHandler, GlobalExceptionHandler>();
        services.AddSingleton<IAlpacaService, AlpacaService>();
        services.AddSingleton<IPolygonDataService, PolygonDataService>();
        services.AddSingleton<IAlpacaVixService, AlpacaVixService>();
        services.AddSingleton<IHistoricalDataService, HistoricalDataService>();
        services.AddSingleton<IHighPerformanceCacheService, HighPerformanceCacheService>();
        services.AddSingleton<IMarketRegimeAnalyzer>(provider => new MarketRegimeAnalyzer(
            provider.GetRequiredService<ILogger<MarketRegimeAnalyzer>>(),
            provider.GetRequiredService<IConfiguration>(),
            provider.GetRequiredService<IAlpacaService>(),
            provider.GetRequiredService<IPolygonDataService>(),
            provider.GetRequiredService<IAlpacaVixService>(),
            provider.GetRequiredService<IHighPerformanceCacheService>()));
        services.AddHttpClient<IPolygonDataService, PolygonDataService>();

        _serviceProvider = services.BuildServiceProvider();
    }

    private static async Task TestHistoricalDataService()
    {
        Console.WriteLine("1. Testing Historical Data Service Improvements...");

        var historicalDataService = _serviceProvider!.GetRequiredService<IHistoricalDataService>();

        // Test with various date ranges to ensure sufficient data
        var testCases = new[]
        {
            new { Symbol = "SPY", Days = 30, Description = "30-day SPY data" },
            new { Symbol = "QQQ", Days = 60, Description = "60-day QQQ data" },
            new { Symbol = "IWM", Days = 5, Description = "5-day IWM data (minimal)" }
        };

        foreach (var testCase in testCases)
        {
            var endDate = DateTime.UtcNow;
            var startDate = endDate.AddDays(-testCase.Days);

            var bars = await historicalDataService.GetHistoricalBarsAsync(
                testCase.Symbol, startDate, endDate);

            Console.WriteLine($"   {testCase.Description}: {bars.Count} bars retrieved");
            
            if (bars.Count > 0)
            {
                var firstBar = bars.First();
                var lastBar = bars.Last();
                Console.WriteLine($"     Date range: {firstBar.Date:yyyy-MM-dd} to {lastBar.Date:yyyy-MM-dd}");
                Console.WriteLine($"     Price range: {bars.Min(b => b.Low):F2} to {bars.Max(b => b.High):F2}");
                Console.WriteLine($"     ✓ Data quality check passed");
            }
            else
            {
                Console.WriteLine($"     ⚠️ No data retrieved for {testCase.Symbol}");
            }
        }

        Console.WriteLine("   ✓ Historical Data Service Test Completed\n");
    }

    private static async Task TestMarketRegimeAnalyzerDataHandling()
    {
        Console.WriteLine("2. Testing Market Regime Analyzer Data Handling...");

        var marketAnalyzer = _serviceProvider!.GetRequiredService<IMarketRegimeAnalyzer>();

        // Test VIX calculation (should not fail with insufficient data warnings)
        Console.WriteLine("   Testing VIX calculation...");
        var vix = await marketAnalyzer.GetVixAsync();
        Console.WriteLine($"   VIX-equivalent: {vix:F2}");

        // Test market regime analysis
        Console.WriteLine("   Testing market regime analysis...");
        var regime = await marketAnalyzer.GetCurrentRegimeAsync();
        Console.WriteLine($"   Market Regime: {regime.OverallRegime}");
        Console.WriteLine($"   Volatility Regime: {regime.VolatilityRegime}");
        Console.WriteLine($"   Trend: {regime.Trend}");
        Console.WriteLine($"   Confidence: {regime.Confidence:P1}");

        // Test market trend analysis
        Console.WriteLine("   Testing market trend analysis...");
        var trend = await marketAnalyzer.GetMarketTrendAsync("SPY");
        Console.WriteLine($"   SPY Trend: {trend}");

        Console.WriteLine("   ✓ Market Regime Analyzer Test Completed\n");
    }

    private static async Task TestVolatilityCalculationsWithFallbacks()
    {
        Console.WriteLine("3. Testing Volatility Calculations with Fallbacks...");

        var marketAnalyzer = _serviceProvider!.GetRequiredService<IMarketRegimeAnalyzer>();

        // Test volatility forecast
        Console.WriteLine("   Testing volatility forecast...");
        var forecast = await marketAnalyzer.GetVolatilityForecastAsync("SPY", 5);
        Console.WriteLine($"   Current Volatility: {forecast.CurrentVolatility:F2}%");
        Console.WriteLine($"   Forecasted Volatility: {forecast.ForecastedVolatility:F2}%");
        Console.WriteLine($"   Confidence: {forecast.Confidence:P1}");
        Console.WriteLine($"   Model: {forecast.Model}");

        // Test market microstructure
        Console.WriteLine("   Testing market microstructure analysis...");
        var microstructure = await marketAnalyzer.GetMarketMicrostructureAsync("SPY");
        Console.WriteLine($"   VWAP: {microstructure.VolumeWeightedAveragePrice:F2}");
        Console.WriteLine($"   Liquidity Score: {microstructure.LiquidityScore:F1}");
        Console.WriteLine($"   Momentum Score: {microstructure.MomentumScore:F2}%");

        Console.WriteLine("   ✓ Volatility Calculations Test Completed\n");
    }

    private static async Task TestSyntheticDataQuality()
    {
        Console.WriteLine("4. Testing Synthetic Data Generation Quality...");

        var alpacaService = _serviceProvider!.GetRequiredService<IAlpacaService>();

        // Test synthetic data for different symbols and periods
        var testSymbols = new[] { "SPY", "QQQ", "IWM" };

        foreach (var symbol in testSymbols)
        {
            var endDate = DateTime.UtcNow;
            var startDate = endDate.AddDays(-100); // Request 100 days

            var data = await alpacaService.GetHistoricalDataAsync(symbol, startDate, endDate);

            Console.WriteLine($"   {symbol} synthetic data:");
            Console.WriteLine($"     Bars generated: {data.Count}");
            
            if (data.Count > 0)
            {
                var returns = new List<decimal>();
                for (int i = 1; i < data.Count; i++)
                {
                    var dailyReturn = (data[i].Close - data[i - 1].Close) / data[i - 1].Close;
                    returns.Add(Math.Abs(dailyReturn));
                }

                var avgVolatility = returns.Average() * (decimal)Math.Sqrt(252) * 100;
                Console.WriteLine($"     Average volatility: {avgVolatility:F2}%");
                Console.WriteLine($"     Price range: {data.Min(d => d.Low):F2} to {data.Max(d => d.High):F2}");
                Console.WriteLine($"     Volume range: {data.Min(d => d.Volume):N0} to {data.Max(d => d.Volume):N0}");
            }
        }

        Console.WriteLine("   ✓ Synthetic Data Quality Test Completed\n");
    }
}
