using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.ComponentModel.DataAnnotations;
using ZeroDateStrat.Models;

namespace ZeroDateStrat.Services;

public interface IConfigurationValidator
{
    Task<ValidationResult> ValidateConfigurationAsync();
    Task<ValidationResult> ValidateTradingTimesAsync();
    Task<ValidationResult> ValidateApiCredentialsAsync();
    Task<ValidationResult> ValidateRiskParametersAsync();
    Task<ValidationResult> ValidateStrategyConfigurationAsync();
    Task<bool> IsConfigurationValidAsync();
}

public class ConfigurationValidator : IConfigurationValidator
{
    private readonly ILogger<ConfigurationValidator> _logger;
    private readonly IConfiguration _configuration;
    private readonly IOptionsMonitor<TradingConfiguration> _tradingConfig;
    private readonly IOptionsMonitor<AlpacaConfiguration> _alpacaConfig;
    private readonly IOptionsMonitor<RiskConfiguration> _riskConfig;

    public ConfigurationValidator(
        ILogger<ConfigurationValidator> logger,
        IConfiguration configuration,
        IOptionsMonitor<TradingConfiguration> tradingConfig,
        IOptionsMonitor<AlpacaConfiguration> alpacaConfig,
        IOptionsMonitor<RiskConfiguration> riskConfig)
    {
        _logger = logger;
        _configuration = configuration;
        _tradingConfig = tradingConfig;
        _alpacaConfig = alpacaConfig;
        _riskConfig = riskConfig;
    }

    public async Task<ValidationResult> ValidateConfigurationAsync()
    {
        var result = new ValidationResult();
        
        try
        {
            _logger.LogInformation("Starting comprehensive configuration validation");

            // Validate all configuration sections
            var tradingValidation = await ValidateTradingTimesAsync();
            var apiValidation = await ValidateApiCredentialsAsync();
            var riskValidation = await ValidateRiskParametersAsync();
            var strategyValidation = await ValidateStrategyConfigurationAsync();

            // Combine results
            result.IsValid = tradingValidation.IsValid && apiValidation.IsValid && 
                           riskValidation.IsValid && strategyValidation.IsValid;

            result.Errors.AddRange(tradingValidation.Errors);
            result.Errors.AddRange(apiValidation.Errors);
            result.Errors.AddRange(riskValidation.Errors);
            result.Errors.AddRange(strategyValidation.Errors);

            result.Warnings.AddRange(tradingValidation.Warnings);
            result.Warnings.AddRange(apiValidation.Warnings);
            result.Warnings.AddRange(riskValidation.Warnings);
            result.Warnings.AddRange(strategyValidation.Warnings);

            if (result.IsValid)
            {
                _logger.LogInformation("Configuration validation passed successfully");
            }
            else
            {
                _logger.LogError($"Configuration validation failed with {result.Errors.Count} errors");
                foreach (var error in result.Errors)
                {
                    _logger.LogError($"Configuration Error: {error}");
                }
            }

            if (result.Warnings.Any())
            {
                _logger.LogWarning($"Configuration validation completed with {result.Warnings.Count} warnings");
                foreach (var warning in result.Warnings)
                {
                    _logger.LogWarning($"Configuration Warning: {warning}");
                }
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during configuration validation");
            result.IsValid = false;
            result.Errors.Add($"Configuration validation failed: {ex.Message}");
            return result;
        }
    }

    public async Task<ValidationResult> ValidateTradingTimesAsync()
    {
        var result = new ValidationResult();
        
        try
        {
            var config = _tradingConfig.CurrentValue;

            // Parse trading times
            if (!TimeSpan.TryParse(config.EntryTimeStart, out var entryStart))
            {
                result.Errors.Add($"Invalid EntryTimeStart format: {config.EntryTimeStart}");
            }

            if (!TimeSpan.TryParse(config.EntryTimeEnd, out var entryEnd))
            {
                result.Errors.Add($"Invalid EntryTimeEnd format: {config.EntryTimeEnd}");
            }

            if (!TimeSpan.TryParse(config.ManagementTime, out var managementTime))
            {
                result.Errors.Add($"Invalid ManagementTime format: {config.ManagementTime}");
            }

            if (!TimeSpan.TryParse(config.ForceCloseTime, out var forceCloseTime))
            {
                result.Errors.Add($"Invalid ForceCloseTime format: {config.ForceCloseTime}");
            }

            if (!TimeSpan.TryParse(config.TradingEndTime, out var tradingEndTime))
            {
                result.Errors.Add($"Invalid TradingEndTime format: {config.TradingEndTime}");
            }

            // Validate time sequence
            if (result.Errors.Count == 0)
            {
                if (entryStart >= entryEnd)
                {
                    result.Errors.Add("EntryTimeStart must be before EntryTimeEnd");
                }

                if (entryEnd >= managementTime)
                {
                    result.Errors.Add("EntryTimeEnd must be before ManagementTime");
                }

                if (managementTime >= forceCloseTime)
                {
                    result.Errors.Add("ManagementTime must be before ForceCloseTime");
                }

                if (forceCloseTime >= tradingEndTime)
                {
                    result.Errors.Add("ForceCloseTime must be before TradingEndTime");
                }

                // Check for reasonable trading windows
                var entryWindow = entryEnd - entryStart;
                if (entryWindow.TotalMinutes < 15)
                {
                    result.Warnings.Add($"Entry window is very short: {entryWindow.TotalMinutes} minutes");
                }

                var managementWindow = forceCloseTime - managementTime;
                if (managementWindow.TotalHours < 1)
                {
                    result.Warnings.Add($"Management window is very short: {managementWindow.TotalHours:F1} hours");
                }
            }

            result.IsValid = result.Errors.Count == 0;
            return result;
        }
        catch (Exception ex)
        {
            result.IsValid = false;
            result.Errors.Add($"Error validating trading times: {ex.Message}");
            return result;
        }
    }

    public async Task<ValidationResult> ValidateApiCredentialsAsync()
    {
        var result = new ValidationResult();
        
        try
        {
            var config = _alpacaConfig.CurrentValue;

            // Check for required fields
            if (string.IsNullOrWhiteSpace(config.ApiKey))
            {
                result.Errors.Add("Alpaca API Key is required");
            }
            else if (config.ApiKey.Length < 10)
            {
                result.Errors.Add("Alpaca API Key appears to be invalid (too short)");
            }

            if (string.IsNullOrWhiteSpace(config.SecretKey))
            {
                result.Errors.Add("Alpaca Secret Key is required");
            }
            else if (config.SecretKey.Length < 20)
            {
                result.Errors.Add("Alpaca Secret Key appears to be invalid (too short)");
            }

            // Validate URLs
            if (!Uri.TryCreate(config.BaseUrl, UriKind.Absolute, out var baseUri))
            {
                result.Errors.Add($"Invalid Alpaca Base URL: {config.BaseUrl}");
            }
            else if (baseUri.Scheme != "https")
            {
                result.Warnings.Add("Alpaca Base URL should use HTTPS for security");
            }

            if (!Uri.TryCreate(config.DataUrl, UriKind.Absolute, out var dataUri))
            {
                result.Errors.Add($"Invalid Alpaca Data URL: {config.DataUrl}");
            }
            else if (dataUri.Scheme != "https")
            {
                result.Warnings.Add("Alpaca Data URL should use HTTPS for security");
            }

            // Check for paper trading vs live trading
            if (config.BaseUrl.Contains("paper"))
            {
                result.Warnings.Add("Using paper trading environment");
            }
            else
            {
                result.Warnings.Add("Using LIVE trading environment - ensure this is intentional");
            }

            result.IsValid = result.Errors.Count == 0;
            return result;
        }
        catch (Exception ex)
        {
            result.IsValid = false;
            result.Errors.Add($"Error validating API credentials: {ex.Message}");
            return result;
        }
    }

    public async Task<ValidationResult> ValidateRiskParametersAsync()
    {
        var result = new ValidationResult();
        
        try
        {
            var tradingConfig = _tradingConfig.CurrentValue;
            var riskConfig = _riskConfig.CurrentValue;

            // Validate risk/reward ratios
            if (tradingConfig.RiskRewardThreshold > tradingConfig.ProfitTargetPercent)
            {
                result.Errors.Add("Risk/Reward threshold cannot be greater than profit target");
            }

            // Validate position sizing
            if (tradingConfig.MaxPositionSize < tradingConfig.MinAccountEquity * 0.1m)
            {
                result.Warnings.Add("Maximum position size is very small relative to minimum account equity");
            }

            // Validate daily limits
            if (tradingConfig.MaxDailyLoss > tradingConfig.MinAccountEquity * 0.5m)
            {
                result.Warnings.Add("Maximum daily loss is high relative to minimum account equity");
            }

            // Validate concentration limits
            if (riskConfig.MaxConcentration > 0.8m)
            {
                result.Warnings.Add("Maximum concentration limit is very high (>80%)");
            }

            // Validate drawdown limits
            if (riskConfig.MaxDrawdown > 0.15m)
            {
                result.Warnings.Add("Maximum drawdown limit is high (>15%)");
            }

            result.IsValid = result.Errors.Count == 0;
            return result;
        }
        catch (Exception ex)
        {
            result.IsValid = false;
            result.Errors.Add($"Error validating risk parameters: {ex.Message}");
            return result;
        }
    }

    public async Task<ValidationResult> ValidateStrategyConfigurationAsync()
    {
        var result = new ValidationResult();
        
        try
        {
            // Validate strategy configurations
            var strategiesSection = _configuration.GetSection("Strategies");
            if (!strategiesSection.Exists())
            {
                result.Errors.Add("Strategies configuration section is missing");
                result.IsValid = false;
                return result;
            }

            var strategies = new[] { "PutCreditSpread", "IronButterfly", "CallCreditSpread" };
            
            foreach (var strategy in strategies)
            {
                var strategySection = strategiesSection.GetSection(strategy);
                if (!strategySection.Exists())
                {
                    result.Warnings.Add($"Strategy configuration missing for: {strategy}");
                    continue;
                }

                // Validate common strategy parameters
                var enabled = strategySection.GetValue<bool>("Enabled");
                var profitTarget = strategySection.GetValue<decimal>("ProfitTarget");
                var stopLoss = strategySection.GetValue<decimal>("StopLoss");

                if (profitTarget <= 0 || profitTarget > 1)
                {
                    result.Errors.Add($"{strategy}: ProfitTarget must be between 0 and 1");
                }

                if (stopLoss <= 0 || stopLoss > 5)
                {
                    result.Errors.Add($"{strategy}: StopLoss must be between 0 and 5");
                }

                if (stopLoss <= profitTarget)
                {
                    result.Warnings.Add($"{strategy}: StopLoss should typically be greater than ProfitTarget");
                }
            }

            result.IsValid = result.Errors.Count == 0;
            return result;
        }
        catch (Exception ex)
        {
            result.IsValid = false;
            result.Errors.Add($"Error validating strategy configuration: {ex.Message}");
            return result;
        }
    }

    public async Task<bool> IsConfigurationValidAsync()
    {
        var result = await ValidateConfigurationAsync();
        return result.IsValid;
    }
}

public class ValidationResult
{
    public bool IsValid { get; set; } = true;
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
}
