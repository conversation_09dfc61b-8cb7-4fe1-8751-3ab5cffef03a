using ZeroDateStrat.Tests;

namespace ZeroDateStrat;

/// <summary>
/// Simple runner for the SyntheticVixService test
/// </summary>
public class RunSyntheticVixTest
{
    public static async Task Main(string[] args)
    {
        Console.WriteLine("🧪 SyntheticVIX Service Test Runner");
        Console.WriteLine(new string('=', 50));
        
        try
        {
            await SyntheticVixServiceTest.RunTestAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"\n❌ Test execution failed: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
        
        Console.WriteLine("\nPress any key to exit...");
        Console.ReadKey();
    }
}
