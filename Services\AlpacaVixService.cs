using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using ZeroDateStrat.Models;

namespace ZeroDateStrat.Services;

/// <summary>
/// Enhanced VIX service that uses Alpaca's Algo Trader Plus subscription
/// to get VIX-equivalent data from VIX proxy ETFs via SyntheticVixService
/// Now includes VIX term structure analysis with VIX9D and VIX3M
/// </summary>
public interface IAlpacaVixService
{
    Task<decimal> GetCurrentVixAsync();
    Task<decimal> GetVixChangeAsync(TimeSpan period);
    Task<VixAnalysis> GetVixAnalysisAsync();
    Task<VixTermStructureAnalysis> GetVixTermStructureAnalysisAsync();
    Task<bool> IsVolatilityRegimeStressedAsync();
    Task<decimal> GetVolatilityRiskPremiumAsync();
    Task<bool> TestConnectionAsync();
}

public class AlpacaVixService : IAlpacaVixService
{
    private readonly ILogger<AlpacaVixService> _logger;
    private readonly ISyntheticVixService _syntheticVixService;
    private readonly IPolygonDataService _polygonDataService;
    private readonly IVixTermStructureService _vixTermStructureService;
    private readonly IConfiguration _configuration;
    private readonly IVixCalibrationService? _vixCalibrationService;

    // Cache for VIX data
    private decimal _cachedVix = 0;
    private DateTime _lastVixUpdate = DateTime.MinValue;
    private readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(1);

    // Calibration tracking
    private DateTime _lastCalibrationCheck = DateTime.MinValue;
    private readonly TimeSpan _calibrationInterval = TimeSpan.FromMinutes(15);

    public AlpacaVixService(
        ILogger<AlpacaVixService> logger,
        ISyntheticVixService syntheticVixService,
        IPolygonDataService polygonDataService,
        IVixTermStructureService vixTermStructureService,
        IConfiguration configuration,
        IVixCalibrationService? vixCalibrationService = null)
    {
        _logger = logger;
        _syntheticVixService = syntheticVixService;
        _polygonDataService = polygonDataService;
        _vixTermStructureService = vixTermStructureService;
        _configuration = configuration;
        _vixCalibrationService = vixCalibrationService;

        _logger.LogInformation("AlpacaVixService initialized with Real VIX primary, Synthetic VIX fallback, VIX term structure analysis, and calibration support");
    }

    public async Task<decimal> GetCurrentVixAsync()
    {
        try
        {
            // Check cache first
            if (_cachedVix > 0 && DateTime.UtcNow - _lastVixUpdate < _cacheExpiry)
            {
                _logger.LogDebug($"Returning cached VIX: {_cachedVix:F2}");
                return _cachedVix;
            }

            _logger.LogDebug("Getting VIX data - Primary: Polygon, Secondary: Polygon Markets, Tertiary: Synthetic VIX");

            // Primary: Use real Polygon VIX data (Indices Starter subscription)
            if (_polygonDataService != null)
            {
                try
                {
                    var polygonVix = await _polygonDataService.GetCurrentVixAsync();
                    if (polygonVix > 0 && polygonVix < 100) // Sanity check
                    {
                        _cachedVix = polygonVix;
                        _lastVixUpdate = DateTime.UtcNow;

                        _logger.LogInformation($"Primary VIX from Polygon: {polygonVix:F2}");

                        // Trigger calibration check in background (fire and forget)
                        _ = Task.Run(async () => await CheckCalibrationAsync(polygonVix));

                        return polygonVix;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Polygon VIX primary source failed, trying secondary source");
                }
            }

            // Secondary: Try Polygon Markets alternative endpoints
            if (_polygonDataService != null)
            {
                try
                {
                    // Try alternative Polygon endpoints for VIX data
                    var polygonMarketsVix = await GetPolygonMarketsVixAsync();
                    if (polygonMarketsVix > 0 && polygonMarketsVix < 100) // Sanity check
                    {
                        _cachedVix = polygonMarketsVix;
                        _lastVixUpdate = DateTime.UtcNow;

                        _logger.LogInformation($"Secondary VIX from Polygon Markets: {polygonMarketsVix:F2}");

                        // Trigger calibration check in background (fire and forget)
                        _ = Task.Run(async () => await CheckCalibrationAsync(polygonMarketsVix));

                        return polygonMarketsVix;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Polygon Markets VIX secondary source failed, falling back to synthetic");
                }
            }

            // Tertiary: Use SyntheticVixService with z-score normalization
            var syntheticVix = await _syntheticVixService.GetCurrentSyntheticVixAsync();
            if (syntheticVix > 0 && syntheticVix < 100) // Sanity check
            {
                _cachedVix = syntheticVix;
                _lastVixUpdate = DateTime.UtcNow;

                _logger.LogInformation($"Tertiary SyntheticVIX fallback: {syntheticVix:F2}");
                return syntheticVix;
            }

            // Final fallback
            _logger.LogWarning("All VIX sources failed, using conservative fallback");
            return 20.0m;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting current VIX");
            return 20.0m;
        }
    }

    public async Task<decimal> GetVixChangeAsync(TimeSpan period)
    {
        try
        {
            // Use SyntheticVixService for change calculation with historical data
            var change = await _syntheticVixService.GetSyntheticVixChangeAsync(period);
            _logger.LogDebug($"SyntheticVIX change over {period}: {change:F2}");
            return change;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error calculating SyntheticVIX change for period {period}");
            return 0m;
        }
    }

    public async Task<VixAnalysis> GetVixAnalysisAsync()
    {
        try
        {
            // Get enhanced analysis from SyntheticVixService
            var syntheticAnalysis = await _syntheticVixService.GetSyntheticVixAnalysisAsync();

            // Convert to legacy VixAnalysis format for backward compatibility
            return new VixAnalysis
            {
                CurrentLevel = syntheticAnalysis.CurrentLevel,
                Interpretation = syntheticAnalysis.Interpretation,
                RiskLevel = syntheticAnalysis.RiskLevel,
                TradingRecommendation = syntheticAnalysis.TradingRecommendation,
                PositionSizeMultiplier = syntheticAnalysis.PositionSizeMultiplier,
                Timestamp = syntheticAnalysis.Timestamp
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing SyntheticVIX analysis");
            return new VixAnalysis
            {
                CurrentLevel = 20.0m,
                Interpretation = "Error - Using Conservative Default",
                RiskLevel = "Medium",
                TradingRecommendation = "Proceed with caution",
                PositionSizeMultiplier = 0.8m,
                Timestamp = DateTime.UtcNow
            };
        }
    }

    public async Task<VixTermStructureAnalysis> GetVixTermStructureAnalysisAsync()
    {
        try
        {
            _logger.LogDebug("Getting VIX term structure analysis");
            return await _vixTermStructureService.GetTermStructureAnalysisAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting VIX term structure analysis");
            return new VixTermStructureAnalysis
            {
                Timestamp = DateTime.UtcNow,
                CurrentStructure = new VixTermStructure { IsDataComplete = false },
                Confidence = 0,
                MarketImplication = "Error retrieving term structure data",
                TradingSignal = "NEUTRAL"
            };
        }
    }

    public async Task<bool> IsVolatilityRegimeStressedAsync()
    {
        try
        {
            var termStructureAnalysis = await _vixTermStructureService.GetTermStructureAnalysisAsync();

            // Consider regime stressed if:
            // 1. Term structure is inverted (backwardation)
            // 2. VIX is above 25
            // 3. Short-term stress is high (VIX - VIX9D > 2)
            var isInverted = termStructureAnalysis.CurrentStructure.RegimeType == VolatilityRegimeType.Backwardation ||
                           termStructureAnalysis.CurrentStructure.RegimeType == VolatilityRegimeType.InvertedStructure;

            var isHighVix = termStructureAnalysis.CurrentStructure.Vix > 25;
            var isShortTermStress = termStructureAnalysis.ShortTermStress > 2;

            var isStressed = isInverted || isHighVix || isShortTermStress;

            _logger.LogDebug($"Volatility regime stressed: {isStressed} " +
                           $"(Inverted: {isInverted}, High VIX: {isHighVix}, Short-term stress: {isShortTermStress})");

            return isStressed;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking volatility regime stress");
            return false; // Conservative default
        }
    }

    public async Task<decimal> GetVolatilityRiskPremiumAsync()
    {
        try
        {
            return await _vixTermStructureService.GetVolatilityRiskPremiumAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting volatility risk premium");
            return 0; // Conservative default
        }
    }

    public async Task<bool> TestConnectionAsync()
    {
        try
        {
            _logger.LogInformation("Testing VIX service connections in priority order...");

            // Test Polygon VIX connection first (primary)
            if (_polygonDataService != null)
            {
                try
                {
                    var polygonVix = await _polygonDataService.GetCurrentVixAsync();
                    if (polygonVix > 0)
                    {
                        _logger.LogInformation("✅ Primary: Polygon VIX service test successful");
                        return true;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogDebug(ex, "Primary Polygon VIX test failed, testing secondary");
                }
            }

            // Test Polygon Markets alternative endpoints (secondary)
            if (_polygonDataService != null)
            {
                try
                {
                    var polygonMarketsVix = await GetPolygonMarketsVixAsync();
                    if (polygonMarketsVix > 0)
                    {
                        _logger.LogInformation("✅ Secondary: Polygon Markets VIX service test successful");
                        return true;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogDebug(ex, "Secondary Polygon Markets test failed, testing tertiary");
                }
            }

            // Test SyntheticVixService connection (tertiary)
            var syntheticVixTest = await _syntheticVixService.TestConnectionAsync();
            if (syntheticVixTest)
            {
                _logger.LogInformation("✅ Tertiary: SyntheticVIX service test successful");

                // Test VIX term structure service
                var termStructureTest = await _vixTermStructureService.TestConnectionAsync();
                if (termStructureTest)
                {
                    _logger.LogInformation("✅ VIX Term Structure service test successful");
                }
                else
                {
                    _logger.LogWarning("⚠️ VIX Term Structure service test failed - basic VIX functionality available");
                }

                return true;
            }

            _logger.LogWarning("❌ All VIX service tests failed (Primary: Polygon, Secondary: Polygon Markets, Tertiary: Synthetic)");
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "VIX service connection test failed");
            return false;
        }
    }

    private async Task CheckCalibrationAsync(decimal realVix)
    {
        try
        {
            // Only check calibration every 15 minutes to avoid excessive processing
            if (DateTime.UtcNow - _lastCalibrationCheck < _calibrationInterval)
                return;

            _lastCalibrationCheck = DateTime.UtcNow;

            // Get synthetic VIX for comparison
            var syntheticVix = await _syntheticVixService.GetCurrentSyntheticVixAsync();
            if (syntheticVix <= 0) return;

            _logger.LogDebug($"VIX Calibration Check - Real: {realVix:F2}, Synthetic: {syntheticVix:F2}");

            // Use VixCalibrationService if available
            if (_vixCalibrationService != null)
            {
                try
                {
                    var calibrationResult = await _vixCalibrationService.CalibrateAsync(realVix, syntheticVix);

                    if (calibrationResult.IsSignificantDrift)
                    {
                        _logger.LogWarning($"Significant VIX calibration drift detected. " +
                                         $"Real: {realVix:F2}, Synthetic: {syntheticVix:F2}, " +
                                         $"Bias: {calibrationResult.Bias:F3}, " +
                                         $"Correlation: {calibrationResult.Correlation:F3}");

                        // Check if we should trigger automatic recalibration
                        var shouldRecalibrate = await _vixCalibrationService.ShouldRecalibrateAsync();
                        if (shouldRecalibrate)
                        {
                            _logger.LogInformation("Applying automatic calibration to synthetic VIX");
                            await _syntheticVixService.ApplyCalibrationAsync(
                                calibrationResult.Bias,
                                calibrationResult.ScalingFactor);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogDebug(ex, "Error during advanced VIX calibration");
                }
            }
            else
            {
                // Fallback to simple logging if calibration service not available
                var difference = Math.Abs(realVix - syntheticVix);
                var percentDifference = (difference / realVix) * 100;

                if (percentDifference > 15)
                {
                    _logger.LogWarning($"Significant VIX calibration drift detected: {percentDifference:F1}% difference. " +
                                     $"Consider reviewing synthetic VIX parameters.");
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Error during VIX calibration check");
        }
    }

    /// <summary>
    /// Gets VIX data from alternative Polygon Markets endpoints as secondary source
    /// </summary>
    private async Task<decimal> GetPolygonMarketsVixAsync()
    {
        try
        {
            _logger.LogDebug("Attempting to get VIX from Polygon Markets alternative endpoints");

            // Try different Polygon endpoints that might be available
            // This could include different market data feeds or alternative VIX symbols

            // Method 1: Try getting VIX from aggregates endpoint with different parameters
            if (_polygonDataService != null)
            {
                try
                {
                    var yesterday = DateTime.UtcNow.AddDays(-1);
                    var vixHistory = await _polygonDataService.GetVixHistoryAsync(yesterday, DateTime.UtcNow, "minute");

                    if (vixHistory.Any())
                    {
                        var latestVix = vixHistory.OrderByDescending(v => v.Date).First();
                        _logger.LogDebug($"Got VIX from aggregates endpoint: {latestVix.Value:F2}");
                        return latestVix.Value;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogDebug(ex, "Polygon Markets aggregates method failed");
                }
            }

            // Method 2: Try alternative VIX-related symbols if available
            // This could include VIX futures or other volatility indices
            // For now, return 0 to indicate failure
            _logger.LogDebug("All Polygon Markets alternative methods failed");
            return 0m;
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Error in GetPolygonMarketsVixAsync");
            return 0m;
        }
    }

}

// Legacy VixAnalysis class for backward compatibility
public class VixAnalysis
{
    public decimal CurrentLevel { get; set; }
    public string Interpretation { get; set; } = string.Empty;
    public string RiskLevel { get; set; } = string.Empty;
    public string TradingRecommendation { get; set; } = string.Empty;
    public decimal PositionSizeMultiplier { get; set; }
    public DateTime Timestamp { get; set; }
}
