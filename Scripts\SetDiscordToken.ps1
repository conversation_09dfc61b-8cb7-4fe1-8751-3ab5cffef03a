# PowerShell script to set Discord bot token as environment variable
# Usage: .\Scripts\SetDiscordToken.ps1 "YOUR_BOT_TOKEN_HERE"

param(
    [Parameter(Mandatory=$true)]
    [string]$BotToken,
    
    [Parameter(Mandatory=$false)]
    [string]$WebhookUrl = "",
    
    [Parameter(Mandatory=$false)]
    [ValidateSet("User", "Machine")]
    [string]$Scope = "User"
)

Write-Host "🔐 Setting Discord Environment Variables..." -ForegroundColor Cyan

try {
    # Set Discord Bot Token
    if ($BotToken) {
        [Environment]::SetEnvironmentVariable("DISCORD_BOT_TOKEN", $BotToken, $Scope)
        Write-Host "✅ DISCORD_BOT_TOKEN set successfully (Scope: $Scope)" -ForegroundColor Green
    }
    
    # Set Discord Webhook URL if provided
    if ($WebhookUrl) {
        [Environment]::SetEnvironmentVariable("DISCORD_WEBHOOK_URL", $WebhookUrl, $Scope)
        Write-Host "✅ DISCORD_WEBHOOK_URL set successfully (Scope: $Scope)" -ForegroundColor Green
    }
    
    Write-Host ""
    Write-Host "🔍 Current Environment Variables:" -ForegroundColor Yellow
    
    $currentBotToken = [Environment]::GetEnvironmentVariable("DISCORD_BOT_TOKEN", $Scope)
    $currentWebhookUrl = [Environment]::GetEnvironmentVariable("DISCORD_WEBHOOK_URL", $Scope)
    
    if ($currentBotToken) {
        $maskedToken = $currentBotToken.Substring(0, [Math]::Min(10, $currentBotToken.Length)) + "..."
        Write-Host "   DISCORD_BOT_TOKEN: $maskedToken" -ForegroundColor Gray
    } else {
        Write-Host "   DISCORD_BOT_TOKEN: [Not Set]" -ForegroundColor Red
    }
    
    if ($currentWebhookUrl) {
        Write-Host "   DISCORD_WEBHOOK_URL: [Set]" -ForegroundColor Gray
    } else {
        Write-Host "   DISCORD_WEBHOOK_URL: [Not Set]" -ForegroundColor Red
    }
    
    Write-Host ""
    Write-Host "⚠️  Important Notes:" -ForegroundColor Yellow
    Write-Host "   • Restart your IDE/terminal to pick up new environment variables" -ForegroundColor White
    Write-Host "   • User scope: Available only to current user" -ForegroundColor White
    Write-Host "   • Machine scope: Available to all users (requires admin)" -ForegroundColor White
    Write-Host "   • Environment variables take precedence over appsettings.json" -ForegroundColor White
    
    Write-Host ""
    Write-Host "🧪 Test the configuration with:" -ForegroundColor Cyan
    Write-Host "   dotnet run discord" -ForegroundColor White
    
} catch {
    Write-Host "❌ Error setting environment variables: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
