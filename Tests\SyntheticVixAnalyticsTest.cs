using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Serilog;
using ZeroDateStrat.Models;
using ZeroDateStrat.Services;
using ILogger = Microsoft.Extensions.Logging.ILogger;

namespace ZeroDateStrat.Tests;

/// <summary>
/// Comprehensive test for the SyntheticVixAnalyticsService
/// Tests monitoring, alerting, performance analysis, and health reporting
/// </summary>
public static class SyntheticVixAnalyticsTest
{
    private static ServiceProvider? _serviceProvider;
    private static ILogger? _logger;

    public static async Task RunTestAsync()
    {
        // Configure Serilog for detailed logging
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Debug()
            .WriteTo.Console(outputTemplate: "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}")
            .WriteTo.File($"logs/synthetic-vix-analytics-test-{DateTime.Now:yyyyMMdd-HHmmss}.txt")
            .CreateLogger();

        try
        {
            Console.WriteLine("🔬 Starting SyntheticVIX Analytics Service Test");
            Console.WriteLine(new string('=', 60));

            await SetupServicesAsync();
            await TestSyntheticVixAnalyticsAsync();

            Console.WriteLine("\n✅ SyntheticVIX Analytics test completed successfully!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"\n❌ SyntheticVIX Analytics test failed: {ex.Message}");
            _logger?.LogError(ex, "SyntheticVIX Analytics test failed");
        }
        finally
        {
            _serviceProvider?.Dispose();
            Log.CloseAndFlush();
        }
    }

    private static async Task SetupServicesAsync()
    {
        Console.WriteLine("🔧 Setting up services for SyntheticVIX Analytics test...");

        var services = new ServiceCollection();

        // Add configuration
        var configuration = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: false)
            .AddJsonFile("appsettings.production.json", optional: true)
            .Build();

        services.AddSingleton<IConfiguration>(configuration);

        // Add logging
        services.AddLogging(builder =>
        {
            builder.ClearProviders();
            builder.AddSerilog();
        });

        // Add required services
        services.AddSingleton<IPerformanceMonitoringService, PerformanceMonitoringService>();
        services.AddSingleton<IAlpacaService, AlpacaService>();
        services.AddSingleton<ISyntheticVixAnalyticsService, SyntheticVixAnalyticsService>();
        services.AddSingleton<ISyntheticVixService, SyntheticVixService>();
        services.AddSingleton<IAlpacaVixService, AlpacaVixService>();
        services.AddSingleton<IPolygonDataService, PolygonDataService>();

        _serviceProvider = services.BuildServiceProvider();
        _logger = _serviceProvider.GetRequiredService<ILoggerFactory>().CreateLogger("SyntheticVixAnalyticsTest");

        Console.WriteLine("✅ Services setup completed\n");
    }

    private static async Task TestSyntheticVixAnalyticsAsync()
    {
        var analyticsService = _serviceProvider!.GetRequiredService<ISyntheticVixAnalyticsService>();
        var syntheticVixService = _serviceProvider!.GetRequiredService<ISyntheticVixService>();
        var logger = _serviceProvider!.GetRequiredService<ILoggerFactory>().CreateLogger("SyntheticVixAnalyticsTest");

        Console.WriteLine("📊 Testing SyntheticVIX Analytics Service");
        Console.WriteLine(new string('-', 50));

        // Test 1: Generate some sample data
        logger.LogInformation("\n📈 Test 1: Generating sample SyntheticVIX calculations...");
        
        for (int i = 0; i < 10; i++)
        {
            try
            {
                var analysis = await syntheticVixService.GetSyntheticVixAnalysisAsync();
                logger.LogInformation($"   Sample {i + 1}: SyntheticVIX={analysis.CurrentLevel:F2}, Z-Score={analysis.ZScore:F2}");
                
                // Add some delay to simulate real-time data
                await Task.Delay(500);
            }
            catch (Exception ex)
            {
                logger.LogWarning(ex, $"Failed to generate sample {i + 1}");
            }
        }

        // Test 2: Health Report
        logger.LogInformation("\n🏥 Test 2: Testing Health Report Generation...");
        var healthReport = await analyticsService.GetHealthReportAsync();
        
        logger.LogInformation($"📊 Health Report Results:");
        logger.LogInformation($"   Overall Health: {healthReport.OverallHealth}");
        logger.LogInformation($"   Summary: {healthReport.Summary}");
        logger.LogInformation($"   Issues Count: {healthReport.Issues.Count}");
        
        if (healthReport.Issues.Any())
        {
            logger.LogInformation($"   Issues:");
            foreach (var issue in healthReport.Issues)
            {
                logger.LogInformation($"     - {issue}");
            }
        }
        
        logger.LogInformation($"   Component Health:");
        foreach (var component in healthReport.ComponentHealth)
        {
            logger.LogInformation($"     {component.Key}: {(component.Value.IsHealthy ? "✅ Healthy" : "❌ Unhealthy")} " +
                                $"(Price: ${component.Value.LastPrice:F2}, Failures: {component.Value.RecentFailureCount})");
        }

        // Test 3: Performance Metrics
        logger.LogInformation("\n⚡ Test 3: Testing Performance Metrics...");
        var performanceMetrics = await analyticsService.GetPerformanceMetricsAsync();
        
        logger.LogInformation($"📊 Performance Metrics:");
        logger.LogInformation($"   Average Calculation Time: {performanceMetrics.AverageCalculationTime:F1}ms");
        logger.LogInformation($"   Calculation Frequency: {performanceMetrics.CalculationFrequency} per hour");
        logger.LogInformation($"   Average Z-Score: {performanceMetrics.AverageZScore:F2}");
        logger.LogInformation($"   Value Stability: {performanceMetrics.ValueStability:F2}");
        
        if (performanceMetrics.ComponentMetrics.Any())
        {
            logger.LogInformation($"   Component Metrics:");
            foreach (var metric in performanceMetrics.ComponentMetrics)
            {
                logger.LogInformation($"     {metric.Key}: {metric.Value.TotalCalculations} calculations, " +
                                    $"Avg Price: ${metric.Value.AveragePrice:F2}, " +
                                    $"Confidence: {metric.Value.AverageConfidence:F2}");
            }
        }

        // Test 4: Active Alerts
        logger.LogInformation("\n🚨 Test 4: Testing Alert System...");
        var alerts = await analyticsService.GetActiveAlertsAsync();
        
        logger.LogInformation($"📊 Active Alerts: {alerts.Count}");
        foreach (var alert in alerts.Take(5)) // Show first 5 alerts
        {
            logger.LogInformation($"   {alert.Severity} Alert: {alert.Type} - {alert.Message} ({alert.Timestamp:HH:mm:ss})");
        }

        // Test 5: Correlation Analysis
        logger.LogInformation("\n🔗 Test 5: Testing Correlation Analysis...");
        var correlationAnalysis = await analyticsService.GetCorrelationAnalysisAsync();
        
        logger.LogInformation($"📊 Correlation Analysis:");
        logger.LogInformation($"   Overall Correlation: {correlationAnalysis.OverallCorrelation:F3}");
        logger.LogInformation($"   Correlation Stability: {correlationAnalysis.CorrelationStability:F3}");
        
        if (correlationAnalysis.ComponentCorrelations.Any())
        {
            logger.LogInformation($"   Component Correlations:");
            foreach (var correlation in correlationAnalysis.ComponentCorrelations)
            {
                logger.LogInformation($"     {correlation.Key}: {correlation.Value:F3}");
            }
        }

        // Test 6: Component Accuracy Validation
        logger.LogInformation("\n✅ Test 6: Testing Component Accuracy Validation...");
        var accuracyValidation = await analyticsService.ValidateComponentAccuracyAsync();
        
        logger.LogInformation($"📊 Component Accuracy Validation: {(accuracyValidation ? "✅ PASSED" : "❌ FAILED")}");

        // Test 7: Calibration Report
        logger.LogInformation("\n⚙️ Test 7: Testing Calibration Report...");
        var calibrationReport = await analyticsService.GetCalibrationReportAsync();
        
        logger.LogInformation($"📊 Calibration Report:");
        logger.LogInformation($"   Z-Score Distribution:");
        logger.LogInformation($"     Mean: {calibrationReport.ZScoreDistribution.Mean:F3}");
        logger.LogInformation($"     Std Dev: {calibrationReport.ZScoreDistribution.StandardDeviation:F3}");
        logger.LogInformation($"     Skewness: {calibrationReport.ZScoreDistribution.SkewnessFactor:F3}");
        
        if (calibrationReport.RecommendedAdjustments.Any())
        {
            logger.LogInformation($"   Recommended Adjustments:");
            foreach (var adjustment in calibrationReport.RecommendedAdjustments)
            {
                logger.LogInformation($"     {adjustment.Severity} - {adjustment.Type}: {adjustment.Description}");
            }
        }
        else
        {
            logger.LogInformation($"   No calibration adjustments recommended");
        }

        // Test 8: Simulate Component Failures
        logger.LogInformation("\n💥 Test 8: Testing Component Failure Recording...");
        
        // Simulate some component failures
        analyticsService.RecordComponentFailure("VXX", new Exception("Simulated network timeout"));
        analyticsService.RecordComponentFailure("UVXY", new Exception("Simulated API rate limit"));
        analyticsService.RecordComponentFailure("VXX", new Exception("Simulated data unavailable"));
        
        logger.LogInformation($"   Recorded 3 simulated component failures");
        
        // Check updated health report
        var updatedHealthReport = await analyticsService.GetHealthReportAsync();
        logger.LogInformation($"   Updated Health Status: {updatedHealthReport.OverallHealth}");
        
        // Test 9: Performance Summary
        logger.LogInformation("\n📈 Test 9: Analytics Performance Summary...");
        
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        
        // Run multiple analytics operations
        var tasks = new List<Task>
        {
            analyticsService.GetHealthReportAsync(),
            analyticsService.GetPerformanceMetricsAsync(),
            analyticsService.GetActiveAlertsAsync(),
            analyticsService.GetCorrelationAnalysisAsync(),
            analyticsService.GetCalibrationReportAsync()
        };
        
        await Task.WhenAll(tasks);
        stopwatch.Stop();
        
        logger.LogInformation($"📊 Analytics Performance:");
        logger.LogInformation($"   Total time for 5 operations: {stopwatch.ElapsedMilliseconds}ms");
        logger.LogInformation($"   Average time per operation: {stopwatch.ElapsedMilliseconds / 5.0:F1}ms");
        logger.LogInformation($"   Operations per second: {5000.0 / stopwatch.ElapsedMilliseconds:F1}");

        Console.WriteLine("\n📊 SyntheticVIX Analytics test completed!");
    }
}
