using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Serilog;
using ZeroDateStrat.Models;
using ZeroDateStrat.Services;
using ILogger = Microsoft.Extensions.Logging.ILogger;

namespace ZeroDateStrat.Tests;

/// <summary>
/// Comprehensive test for the new SyntheticVixService implementation
/// Tests z-score normalization, component weighting, and VIX-equivalent calculation
/// </summary>
public static class SyntheticVixServiceTest
{
    private static ServiceProvider? _serviceProvider;
    private static ILogger? _logger;

    public static async Task RunTestAsync()
    {
        // Configure Serilog for detailed logging
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Debug()
            .WriteTo.Console(outputTemplate: "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}")
            .WriteTo.File($"logs/synthetic-vix-service-test-{DateTime.Now:yyyyMMdd-HHmmss}.txt")
            .CreateLogger();

        try
        {
            Console.WriteLine("🧪 Starting SyntheticVixService Comprehensive Test");
            Console.WriteLine(new string('=', 60));

            await SetupServicesAsync();
            await TestSyntheticVixServiceAsync();

            Console.WriteLine("\n✅ SyntheticVixService test completed successfully!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"\n❌ SyntheticVixService test failed: {ex.Message}");
            _logger?.LogError(ex, "SyntheticVixService test failed");
        }
        finally
        {
            _serviceProvider?.Dispose();
            Log.CloseAndFlush();
        }
    }

    private static async Task SetupServicesAsync()
    {
        Console.WriteLine("🔧 Setting up services for SyntheticVixService test...");

        var services = new ServiceCollection();

        // Add configuration
        var configuration = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: false)
            .AddJsonFile("appsettings.production.json", optional: true)
            .Build();

        services.AddSingleton<IConfiguration>(configuration);

        // Add logging
        services.AddLogging(builder =>
        {
            builder.ClearProviders();
            builder.AddSerilog();
        });

        // Add required services
        services.AddSingleton<IAlpacaService, AlpacaService>();
        services.AddSingleton<ISyntheticVixService, SyntheticVixService>();
        services.AddSingleton<IAlpacaVixService, AlpacaVixService>();
        services.AddSingleton<IPolygonDataService, PolygonDataService>();

        _serviceProvider = services.BuildServiceProvider();
        _logger = _serviceProvider.GetRequiredService<ILoggerFactory>().CreateLogger("SyntheticVixServiceTest");

        Console.WriteLine("✅ Services setup completed\n");
    }

    private static async Task TestSyntheticVixServiceAsync()
    {
        var syntheticVixService = _serviceProvider!.GetRequiredService<ISyntheticVixService>();
        var alpacaVixService = _serviceProvider!.GetRequiredService<IAlpacaVixService>();
        var logger = _serviceProvider!.GetRequiredService<ILoggerFactory>().CreateLogger("SyntheticVixServiceTest");

        Console.WriteLine("📊 Testing SyntheticVixService Implementation");
        Console.WriteLine(new string('-', 50));

        // Test 1: Connection Test
        logger.LogInformation("\n🔌 Test 1: Testing SyntheticVixService Connection...");
        var connectionTest = await syntheticVixService.TestConnectionAsync();
        
        if (connectionTest)
        {
            logger.LogInformation("✅ SyntheticVixService connection test successful");
        }
        else
        {
            logger.LogWarning("⚠️ SyntheticVixService connection test failed");
        }

        // Test 2: Current SyntheticVIX Calculation
        logger.LogInformation("\n📈 Test 2: Testing Current SyntheticVIX Calculation...");
        var currentSyntheticVix = await syntheticVixService.GetCurrentSyntheticVixAsync();
        
        if (currentSyntheticVix > 0)
        {
            logger.LogInformation($"✅ Current SyntheticVIX: {currentSyntheticVix:F2}");
            
            // Interpret SyntheticVIX level
            var interpretation = currentSyntheticVix switch
            {
                < 12 => "Very Low (Complacency Risk)",
                < 15 => "Low (Good for Premium Selling)",
                < 20 => "Normal (Standard Trading)",
                < 25 => "Elevated (Caution Advised)",
                < 30 => "High (Defensive Strategies)",
                < 35 => "Very High (Minimal Trading)",
                _ => "Extreme (Emergency Mode)"
            };
            
            logger.LogInformation($"📊 SyntheticVIX Level: {interpretation}");
        }
        else
        {
            logger.LogWarning("⚠️ Could not calculate SyntheticVIX");
        }

        // Test 3: SyntheticVIX Analysis
        logger.LogInformation("\n🔍 Test 3: Testing SyntheticVIX Analysis...");
        var analysis = await syntheticVixService.GetSyntheticVixAnalysisAsync();
        
        logger.LogInformation($"📊 SyntheticVIX Analysis Results:");
        logger.LogInformation($"   Current Level: {analysis.CurrentLevel:F2}");
        logger.LogInformation($"   Z-Score: {analysis.ZScore:F2}");
        logger.LogInformation($"   Risk Level: {analysis.RiskLevel}");
        logger.LogInformation($"   Interpretation: {analysis.Interpretation}");
        logger.LogInformation($"   Trading Recommendation: {analysis.TradingRecommendation}");
        logger.LogInformation($"   Position Size Multiplier: {analysis.PositionSizeMultiplier:F2}");
        
        if (analysis.ComponentBreakdown.Any())
        {
            logger.LogInformation($"📈 Component Breakdown:");
            foreach (var component in analysis.ComponentBreakdown)
            {
                logger.LogInformation($"   {component.Symbol}: ${component.Price:F2} -> " +
                                    $"Normalized: {component.NormalizedValue:F2} " +
                                    $"(Weight: {component.Weight:F1}, Confidence: {component.Confidence:F1})");
            }
        }

        // Test 4: Historical Data and Change Calculation
        logger.LogInformation("\n📈 Test 4: Testing Historical Data and Change Calculation...");
        
        // Wait a bit and get another reading to test change calculation
        await Task.Delay(2000);
        var secondReading = await syntheticVixService.GetCurrentSyntheticVixAsync();
        
        // Test change calculation over different periods
        var periods = new[] { TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(5), TimeSpan.FromHours(1) };
        
        foreach (var period in periods)
        {
            var change = await syntheticVixService.GetSyntheticVixChangeAsync(period);
            logger.LogInformation($"   SyntheticVIX change over {period}: {change:F2}");
        }

        // Test 5: Historical Data Retrieval
        logger.LogInformation("\n📊 Test 5: Testing Historical Data Retrieval...");
        var historicalData = await syntheticVixService.GetHistoricalDataAsync(1);
        
        if (historicalData.Any())
        {
            logger.LogInformation($"📈 Retrieved {historicalData.Count} historical data points:");
            foreach (var point in historicalData.TakeLast(3))
            {
                logger.LogInformation($"   {point.Timestamp:HH:mm:ss}: Raw={point.RawValue:F2}, " +
                                    $"Normalized={point.NormalizedValue:F2}");
            }
        }
        else
        {
            logger.LogInformation("📊 No historical data available yet (expected for new service)");
        }

        // Test 6: Integration with AlpacaVixService
        logger.LogInformation("\n🔗 Test 6: Testing Integration with AlpacaVixService...");
        var alpacaVix = await alpacaVixService.GetCurrentVixAsync();
        var alpacaAnalysis = await alpacaVixService.GetVixAnalysisAsync();
        
        logger.LogInformation($"📊 AlpacaVixService (using SyntheticVIX): {alpacaVix:F2}");
        logger.LogInformation($"   Risk Level: {alpacaAnalysis.RiskLevel}");
        logger.LogInformation($"   Position Size Multiplier: {alpacaAnalysis.PositionSizeMultiplier:F2}");

        // Test 7: Performance and Consistency Test
        logger.LogInformation("\n⚡ Test 7: Testing Performance and Consistency...");
        var readings = new List<decimal>();
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        
        for (int i = 0; i < 5; i++)
        {
            var reading = await syntheticVixService.GetCurrentSyntheticVixAsync();
            readings.Add(reading);
            logger.LogInformation($"   Reading #{i + 1}: {reading:F2}");
        }
        
        stopwatch.Stop();
        
        var avgReading = readings.Average();
        var stdDev = Math.Sqrt(readings.Sum(r => Math.Pow((double)(r - avgReading), 2)) / readings.Count);
        
        logger.LogInformation($"📊 Performance Results:");
        logger.LogInformation($"   Average time per reading: {stopwatch.ElapsedMilliseconds / 5.0:F1}ms");
        logger.LogInformation($"   Average SyntheticVIX: {avgReading:F2}");
        logger.LogInformation($"   Standard deviation: {stdDev:F2}");
        logger.LogInformation($"   Consistency: {(stdDev < 1.0 ? "Good" : "Needs attention")}");

        // Test 8: Configuration Validation
        logger.LogInformation("\n⚙️ Test 8: Validating SyntheticVIX Configuration...");
        logger.LogInformation("📋 Expected Configuration (from JSON spec):");
        logger.LogInformation("   VXX: 0.5 weight (ETF, Alpaca source)");
        logger.LogInformation("   UVXY: 0.3 weight (ETF, Alpaca source)");
        logger.LogInformation("   SVXY: -0.2 weight (ETF, Alpaca source, inverse)");
        logger.LogInformation("   Normalization: z-score with 20-period window");
        logger.LogInformation("   Label: SyntheticVIX");

        Console.WriteLine("\n📊 SyntheticVixService test completed!");
    }
}
