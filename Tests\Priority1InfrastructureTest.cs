using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Serilog;
using ZeroDateStrat.Models;
using ZeroDateStrat.Services;

namespace ZeroDateStrat.Tests;

public static class Priority1InfrastructureTest
{
    public static async Task RunPriority1InfrastructureTest()
    {
        // Configure Serilog for testing
        Log.Logger = new LoggerConfiguration()
            .WriteTo.Console()
            .WriteTo.File("logs/priority1-infrastructure-test-.txt", rollingInterval: RollingInterval.Day)
            .CreateLogger();

        try
        {
            Log.Information("=== Priority 1 Infrastructure Test ===");
            Log.Information("Testing critical infrastructure improvements");

            // Create test host with all services
            var host = CreateTestHost();
            
            // Test 1: Configuration Validation
            await TestConfigurationValidation(host);
            
            // Test 2: Security Service
            await TestSecurityService(host);
            
            // Test 3: Global Exception Handler
            await TestGlobalException<PERSON>and<PERSON>(host);
            
            // Test 4: Service Registration
            await TestServiceRegistration(host);
            
            // Test 5: Error Handling Integration
            await TestErrorHandlingIntegration(host);

            Log.Information("=== Priority 1 Infrastructure Test Completed Successfully ===");
        }
        catch (Exception ex)
        {
            Log.Error(ex, "Priority 1 Infrastructure Test failed");
            throw;
        }
        finally
        {
            Log.CloseAndFlush();
        }
    }

    private static IHost CreateTestHost()
    {
        return Host.CreateDefaultBuilder()
            .UseSerilog()
            .ConfigureAppConfiguration((context, config) =>
            {
                config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
                config.AddInMemoryCollection(new Dictionary<string, string?>
                {
                    ["Alpaca:ApiKey"] = "AKTEST1234567890TEST",
                    ["Alpaca:SecretKey"] = "1234567890123456789012345678901234567890",
                    ["Trading:MaxPositionSize"] = "5000",
                    ["Trading:MaxDailyLoss"] = "250"
                });
            })
            .ConfigureServices((context, services) =>
            {
                // Configuration validation
                services.AddOptions<TradingConfiguration>()
                    .Bind(context.Configuration.GetSection("Trading"))
                    .ValidateDataAnnotations()
                    .ValidateOnStart();

                services.AddOptions<AlpacaConfiguration>()
                    .Bind(context.Configuration.GetSection("Alpaca"))
                    .ValidateDataAnnotations()
                    .ValidateOnStart();

                services.AddOptions<RiskConfiguration>()
                    .Bind(context.Configuration.GetSection("Risk"))
                    .ValidateDataAnnotations()
                    .ValidateOnStart();

                // Core Services
                services.AddSingleton<ISecurityService, SecurityService>();
                services.AddSingleton<IConfigurationValidator, ConfigurationValidator>();
                services.AddSingleton<IGlobalExceptionHandler, GlobalExceptionHandler>();

                // Add logging
                services.AddLogging(builder =>
                {
                    builder.ClearProviders();
                    builder.AddSerilog();
                });
            })
            .Build();
    }

    private static async Task TestConfigurationValidation(IHost host)
    {
        Log.Information("--- Testing Configuration Validation ---");
        
        var configValidator = host.Services.GetRequiredService<IConfigurationValidator>();
        
        // Test overall configuration validation
        var validationResult = await configValidator.ValidateConfigurationAsync();
        Log.Information($"Configuration validation result: {(validationResult.IsValid ? "PASSED" : "FAILED")}");
        
        if (!validationResult.IsValid)
        {
            foreach (var error in validationResult.Errors)
            {
                Log.Warning($"Configuration Error: {error}");
            }
        }
        
        if (validationResult.Warnings.Any())
        {
            foreach (var warning in validationResult.Warnings)
            {
                Log.Information($"Configuration Warning: {warning}");
            }
        }

        // Test specific validation methods
        var tradingTimesResult = await configValidator.ValidateTradingTimesAsync();
        Log.Information($"Trading times validation: {(tradingTimesResult.IsValid ? "PASSED" : "FAILED")}");

        var apiCredentialsResult = await configValidator.ValidateApiCredentialsAsync();
        Log.Information($"API credentials validation: {(apiCredentialsResult.IsValid ? "PASSED" : "FAILED")}");

        var riskParametersResult = await configValidator.ValidateRiskParametersAsync();
        Log.Information($"Risk parameters validation: {(riskParametersResult.IsValid ? "PASSED" : "FAILED")}");

        var strategyConfigResult = await configValidator.ValidateStrategyConfigurationAsync();
        Log.Information($"Strategy configuration validation: {(strategyConfigResult.IsValid ? "PASSED" : "FAILED")}");

        Log.Information("Configuration validation tests completed");
    }

    private static async Task TestSecurityService(IHost host)
    {
        Log.Information("--- Testing Security Service ---");
        
        var securityService = host.Services.GetRequiredService<ISecurityService>();
        
        // Test encryption/decryption
        var testData = "TestSensitiveData123";
        var encrypted = await securityService.EncryptSensitiveDataAsync(testData);
        var decrypted = await securityService.DecryptSensitiveDataAsync(encrypted);
        
        if (decrypted == testData)
        {
            Log.Information("Encryption/Decryption test: PASSED");
        }
        else
        {
            Log.Error("Encryption/Decryption test: FAILED");
        }

        // Test API key validation
        var validApiKey = "AKTEST1234567890TEST";
        var invalidApiKey = "INVALID_KEY";
        
        var validKeyResult = await securityService.ValidateApiKeyFormatAsync(validApiKey);
        var invalidKeyResult = await securityService.ValidateApiKeyFormatAsync(invalidApiKey);
        
        Log.Information($"Valid API key validation: {(validKeyResult ? "PASSED" : "FAILED")}");
        Log.Information($"Invalid API key validation: {(!invalidKeyResult ? "PASSED" : "FAILED")}");

        // Test secret key validation
        var validSecretKey = "1234567890123456789012345678901234567890";
        var invalidSecretKey = "SHORT";
        
        var validSecretResult = await securityService.ValidateSecretKeyFormatAsync(validSecretKey);
        var invalidSecretResult = await securityService.ValidateSecretKeyFormatAsync(invalidSecretKey);
        
        Log.Information($"Valid secret key validation: {(validSecretResult ? "PASSED" : "FAILED")}");
        Log.Information($"Invalid secret key validation: {(!invalidSecretResult ? "PASSED" : "FAILED")}");

        // Test security audit
        var auditResult = await securityService.PerformSecurityAuditAsync();
        Log.Information($"Security audit completed. Score: {auditResult.OverallSecurityScore:P0}, Secure: {auditResult.IsSecure}");
        
        foreach (var check in auditResult.Checks)
        {
            Log.Information($"Security Check - {check.CheckName}: {(check.Passed ? "PASSED" : "FAILED")} - {check.Message}");
        }

        // Test secure credential retrieval
        var apiKey = await securityService.GetSecureApiKeyAsync();
        var secretKey = await securityService.GetSecureSecretKeyAsync();
        
        Log.Information($"Secure API key retrieval: {(!string.IsNullOrEmpty(apiKey) ? "PASSED" : "FAILED")}");
        Log.Information($"Secure secret key retrieval: {(!string.IsNullOrEmpty(secretKey) ? "PASSED" : "FAILED")}");

        Log.Information("Security service tests completed");
    }

    private static async Task TestGlobalExceptionHandler(IHost host)
    {
        Log.Information("--- Testing Global Exception Handler ---");
        
        var exceptionHandler = host.Services.GetRequiredService<IGlobalExceptionHandler>();
        
        // Test exception handling
        var testException = new InvalidOperationException("Test exception for handling");
        var handled = await exceptionHandler.HandleExceptionAsync(testException, "TestContext");
        Log.Information($"Exception handling test: {(handled != null ? "PASSED" : "FAILED")}");

        // Test retry mechanism with success
        var retrySuccessResult = await exceptionHandler.ExecuteWithRetryAsync(async () =>
        {
            Log.Information("Retry test - operation succeeded");
            return "Success";
        }, 3, "RetrySuccessTest");
        
        Log.Information($"Retry success test: {(retrySuccessResult == "Success" ? "PASSED" : "FAILED")}");

        // Test retry mechanism with failure
        var attemptCount = 0;
        var retryFailureResult = await exceptionHandler.ExecuteWithRetryAsync(async () =>
        {
            attemptCount++;
            if (attemptCount < 3)
            {
                throw new TimeoutException("Simulated timeout");
            }
            return "Success after retries";
        }, 3, "RetryFailureTest");
        
        Log.Information($"Retry with eventual success test: {(retryFailureResult == "Success after retries" ? "PASSED" : "FAILED")}");

        // Test recoverable exception detection
        var timeoutException = new TimeoutException("Test timeout");
        var httpException = new HttpRequestException("Test HTTP error");
        var argumentException = new ArgumentException("Test argument error");
        
        var timeoutRecoverable = await exceptionHandler.IsRecoverableExceptionAsync(timeoutException);
        var httpRecoverable = await exceptionHandler.IsRecoverableExceptionAsync(httpException);
        var argumentRecoverable = await exceptionHandler.IsRecoverableExceptionAsync(argumentException);
        
        Log.Information($"Timeout exception recoverable: {(timeoutRecoverable ? "PASSED" : "FAILED")}");
        Log.Information($"HTTP exception recoverable: {(httpRecoverable ? "PASSED" : "FAILED")}");
        Log.Information($"Argument exception not recoverable: {(!argumentRecoverable ? "PASSED" : "FAILED")}");

        // Test exception statistics
        var stats = await exceptionHandler.GetExceptionStatisticsAsync();
        Log.Information($"Exception statistics: Total: {stats.TotalExceptions}, Last 24h: {stats.ExceptionsLast24Hours}");

        Log.Information("Global exception handler tests completed");
    }

    private static async Task TestServiceRegistration(IHost host)
    {
        Log.Information("--- Testing Service Registration ---");
        
        // Test that all required services are registered
        var services = new[]
        {
            typeof(ISecurityService),
            typeof(IConfigurationValidator),
            typeof(IGlobalExceptionHandler),
            typeof(ILogger<Program>)
        };

        foreach (var serviceType in services)
        {
            try
            {
                var service = host.Services.GetRequiredService(serviceType);
                Log.Information($"Service registration test for {serviceType.Name}: PASSED");
            }
            catch (Exception ex)
            {
                Log.Error($"Service registration test for {serviceType.Name}: FAILED - {ex.Message}");
            }
        }

        // Test configuration options
        try
        {
            var tradingConfig = host.Services.GetRequiredService<IOptions<TradingConfiguration>>();
            var alpacaConfig = host.Services.GetRequiredService<IOptions<AlpacaConfiguration>>();
            var riskConfig = host.Services.GetRequiredService<IOptions<RiskConfiguration>>();
            
            Log.Information("Configuration options registration: PASSED");
            Log.Information($"Trading config - MaxPositionSize: {tradingConfig.Value.MaxPositionSize}");
            Log.Information($"Alpaca config - BaseUrl: {alpacaConfig.Value.BaseUrl}");
            Log.Information($"Risk config - MaxDrawdown: {riskConfig.Value.MaxDrawdown}");
        }
        catch (Exception ex)
        {
            Log.Error($"Configuration options registration: FAILED - {ex.Message}");
        }

        Log.Information("Service registration tests completed");
    }

    private static async Task TestErrorHandlingIntegration(IHost host)
    {
        Log.Information("--- Testing Error Handling Integration ---");
        
        var exceptionHandler = host.Services.GetRequiredService<IGlobalExceptionHandler>();
        var securityService = host.Services.GetRequiredService<ISecurityService>();
        
        // Test integrated error handling with security service
        var integrationResult = await exceptionHandler.ExecuteWithRetryAsync(async () =>
        {
            // This should work
            var apiKey = await securityService.GetSecureApiKeyAsync();
            return !string.IsNullOrEmpty(apiKey);
        }, 2, "SecurityIntegrationTest");
        
        Log.Information($"Security service integration test: {(integrationResult == true ? "PASSED" : "FAILED")}");

        // Test error handling with configuration validation
        var configValidator = host.Services.GetRequiredService<IConfigurationValidator>();
        var configIntegrationResult = await exceptionHandler.ExecuteWithRetryAsync(async () =>
        {
            var result = await configValidator.IsConfigurationValidAsync();
            return result;
        }, 2, "ConfigValidationIntegrationTest");
        
        Log.Information($"Configuration validation integration test: {(configIntegrationResult != null ? "PASSED" : "FAILED")}");

        Log.Information("Error handling integration tests completed");
    }
}
