using ZeroDateStrat.Models;

namespace ZeroDateStrat.Tests;

public class BasicTests
{
    public BasicTests()
    {
        // Simple test class without mocking dependencies
    }

    public void TestOptionContractProperties()
    {
        // Arrange
        var option = new OptionContract
        {
            Symbol = "SPY240315C00500000",
            UnderlyingSymbol = "SPY",
            ExpirationDate = DateTime.Today,
            StrikePrice = 500m,
            OptionType = OptionType.Call,
            Bid = 1.50m,
            Ask = 1.60m,
            Volume = 100,
            OpenInterest = 500
        };

        // Act & Assert
        Console.WriteLine($"Mid Price: {option.MidPrice}"); // Should be 1.55
        Console.WriteLine($"Spread: {option.Spread}"); // Should be 0.10
        Console.WriteLine($"Is Zero DTE: {option.IsZeroDte}"); // Should be true
        Console.WriteLine($"Is Liquid: {option.IsLiquid}"); // Should be true

        // Verify calculations
        var expectedMidPrice = 1.55m;
        var expectedSpread = 0.10m;
        var expectedIsZeroDte = true;
        var expectedIsLiquid = true;

        if (Math.Abs(option.MidPrice - expectedMidPrice) < 0.01m &&
            Math.Abs(option.Spread - expectedSpread) < 0.01m &&
            option.IsZeroDte == expectedIsZeroDte &&
            option.IsLiquid == expectedIsLiquid)
        {
            Console.WriteLine("✓ Option Contract Properties Test PASSED");
        }
        else
        {
            Console.WriteLine("❌ Option Contract Properties Test FAILED");
            Console.WriteLine($"   Expected MidPrice: {expectedMidPrice:F2}, Got: {option.MidPrice:F2}");
            Console.WriteLine($"   Expected Spread: {expectedSpread:F2}, Got: {option.Spread:F2}");
            Console.WriteLine($"   Expected IsZeroDte: {expectedIsZeroDte}, Got: {option.IsZeroDte}");
            Console.WriteLine($"   Expected IsLiquid: {expectedIsLiquid}, Got: {option.IsLiquid}");
        }
    }

    public void TestTradingSignalValidation()
    {
        // Arrange
        var signal = new TradingSignal
        {
            Strategy = "IronCondor",
            UnderlyingSymbol = "SPY",
            Type = SignalType.IronCondor,
            Confidence = 0.75m,
            ExpectedProfit = 50m,
            MaxLoss = 150m,
            Legs = new List<OptionLeg>
            {
                new() { Symbol = "SPY240315P00495000", OptionType = OptionType.Put, Side = OrderSide.Sell, Quantity = 1 },
                new() { Symbol = "SPY240315P00490000", OptionType = OptionType.Put, Side = OrderSide.Buy, Quantity = 1 },
                new() { Symbol = "SPY240315C00505000", OptionType = OptionType.Call, Side = OrderSide.Sell, Quantity = 1 },
                new() { Symbol = "SPY240315C00510000", OptionType = OptionType.Call, Side = OrderSide.Buy, Quantity = 1 }
            }
        };

        // Set the RiskRewardRatio property that IsValid checks
        signal.RiskRewardRatio = signal.CalculatedRiskRewardRatio;

        // Act & Assert
        Console.WriteLine($"Calculated Risk/Reward Ratio: {signal.CalculatedRiskRewardRatio:F2}"); // Should be 0.33
        Console.WriteLine($"Risk/Reward Ratio (Property): {signal.RiskRewardRatio:F2}"); // Should be 0.33
        Console.WriteLine($"Is Valid: {signal.IsValid}"); // Should be true

        // Verify calculations
        var expectedCalculatedRatio = 50m / 150m; // 0.33
        if (Math.Abs(signal.CalculatedRiskRewardRatio - expectedCalculatedRatio) < 0.01m && signal.IsValid)
        {
            Console.WriteLine("✓ Trading Signal Validation Test PASSED");
        }
        else
        {
            Console.WriteLine("❌ Trading Signal Validation Test FAILED");
            Console.WriteLine($"   Expected Calculated Ratio: {expectedCalculatedRatio:F2}, Got: {signal.CalculatedRiskRewardRatio:F2}");
            Console.WriteLine($"   Expected Valid: True, Got: {signal.IsValid}");
        }
    }

    public void TestPositionManagement()
    {
        // Arrange
        var position = new Position
        {
            Strategy = "IronCondor",
            UnderlyingSymbol = "SPY",
            OpenCredit = 200m,
            CurrentValue = -100m,
            ProfitTarget = 100m,
            StopLoss = 160m,
            ExpirationDate = DateTime.Today,
            Status = PositionStatus.Open
        };

        // Act
        position.UnrealizedPnL = position.OpenCredit + position.CurrentValue;

        // Assert
        Console.WriteLine($"Unrealized P&L: {position.UnrealizedPnL:C}"); // Should be $100
        Console.WriteLine($"P&L Percentage: {position.PnLPercentage:F1}%"); // Should be 50%
        Console.WriteLine($"Should Close: {position.ShouldClose}"); // Should be true (profit target reached)

        // Verify calculations
        var expectedPnL = 100m;
        var expectedPnLPercentage = 50m;
        var expectedShouldClose = true;

        if (Math.Abs(position.UnrealizedPnL - expectedPnL) < 0.01m &&
            Math.Abs(position.PnLPercentage - expectedPnLPercentage) < 0.01m &&
            position.ShouldClose == expectedShouldClose)
        {
            Console.WriteLine("✓ Position Management Test PASSED");
        }
        else
        {
            Console.WriteLine("❌ Position Management Test FAILED");
            Console.WriteLine($"   Expected P&L: {expectedPnL:C}, Got: {position.UnrealizedPnL:C}");
            Console.WriteLine($"   Expected P&L%: {expectedPnLPercentage:F1}%, Got: {position.PnLPercentage:F1}%");
            Console.WriteLine($"   Expected ShouldClose: {expectedShouldClose}, Got: {position.ShouldClose}");
        }
    }

    public static void RunAllTests()
    {
        var tests = new BasicTests();

        Console.WriteLine("=== Running Basic Tests ===\n");

        Console.WriteLine("1. Testing Option Contract Properties:");
        tests.TestOptionContractProperties();

        Console.WriteLine("\n2. Testing Trading Signal Validation:");
        tests.TestTradingSignalValidation();

        Console.WriteLine("\n3. Testing Position Management:");
        tests.TestPositionManagement();

        Console.WriteLine("\n=== All Tests Completed ===");
    }

    public static async Task RunDiscordErrorTestAsync()
    {
        Console.WriteLine("=== Running Discord Error Notification Test ===\n");
        await DiscordErrorNotificationTest.RunTestAsync();
        Console.WriteLine("\n=== Discord Error Test Completed ===");
    }

    public static async Task RunComprehensiveDiscordTestAsync()
    {
        Console.WriteLine("=== Running Comprehensive Discord Message Test Suite ===\n");
        await ComprehensiveDiscordTest.RunAllDiscordTestsAsync();
        Console.WriteLine("\n=== Comprehensive Discord Test Completed ===");
    }
}
