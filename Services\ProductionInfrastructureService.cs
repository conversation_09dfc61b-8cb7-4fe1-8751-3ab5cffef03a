using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using ZeroDateStrat.Models;
using System.Collections.Concurrent;
using System.Text.Json;
using System.Diagnostics;

namespace ZeroDateStrat.Services;

// Phase 3: Production Infrastructure Service
public interface IProductionInfrastructureService
{
    Task<bool> InitializeAsync();
    Task<CircuitBreakerState> GetCircuitBreakerStateAsync(string serviceName);
    Task<bool> ExecuteWithCircuitBreakerAsync<T>(string serviceName, Func<Task<T>> operation);
    Task<bool> ExecuteWithRetryAsync<T>(Func<Task<T>> operation, int maxRetries = 3);
    Task<bool> ValidateConfigurationAsync();
    Task<Dictionary<string, object>> GetEnvironmentInfoAsync();
    Task<bool> PerformStartupChecksAsync();
    Task<bool> PerformShutdownAsync();
    Task LogCriticalEventAsync(string eventType, string message, Dictionary<string, object>? metadata = null);
    Task<bool> BackupConfigurationAsync();
    Task<bool> RestoreConfigurationAsync(string backupPath);
}

public class ProductionInfrastructureService : IProductionInfrastructureService
{
    private readonly ILogger<ProductionInfrastructureService> _logger;
    private readonly IConfiguration _configuration;
    private readonly ConcurrentDictionary<string, CircuitBreakerState> _circuitBreakers = new();
    private readonly ConcurrentQueue<CriticalEvent> _criticalEvents = new();
    
    private bool _isInitialized = false;
    private readonly object _initLock = new object();

    public ProductionInfrastructureService(
        ILogger<ProductionInfrastructureService> logger,
        IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;
    }

    public async Task<bool> InitializeAsync()
    {
        lock (_initLock)
        {
            if (_isInitialized)
            {
                _logger.LogWarning("Production infrastructure already initialized");
                return true;
            }
        }

        try
        {
            _logger.LogInformation("Initializing production infrastructure...");

            // Initialize circuit breakers
            await InitializeCircuitBreakersAsync();

            // Validate configuration
            if (!await ValidateConfigurationAsync())
            {
                _logger.LogError("Configuration validation failed");
                return false;
            }

            // Perform startup checks
            if (!await PerformStartupChecksAsync())
            {
                _logger.LogError("Startup checks failed");
                return false;
            }

            // Backup current configuration
            await BackupConfigurationAsync();

            lock (_initLock)
            {
                _isInitialized = true;
            }

            _logger.LogInformation("Production infrastructure initialized successfully");
            await LogCriticalEventAsync("SystemStartup", "Production infrastructure initialized", 
                new Dictionary<string, object> { ["Timestamp"] = DateTime.UtcNow });

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize production infrastructure");
            return false;
        }
    }

    private async Task InitializeCircuitBreakersAsync()
    {
        try
        {
            var services = new[]
            {
                "AlpacaAPI",
                "OptionsData",
                "MarketData",
                "RiskManagement",
                "OrderExecution"
            };

            foreach (var service in services)
            {
                var circuitBreaker = new CircuitBreakerState
                {
                    ServiceName = service,
                    Status = CircuitBreakerStatus.Closed,
                    FailureCount = 0,
                    Timeout = TimeSpan.FromMinutes(_configuration.GetValue<int>($"CircuitBreaker:{service}:TimeoutMinutes", 5)),
                    RecentErrors = new List<string>()
                };

                _circuitBreakers.TryAdd(service, circuitBreaker);
            }

            _logger.LogInformation($"Initialized {services.Length} circuit breakers");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error initializing circuit breakers");
            throw;
        }
    }

    public async Task<CircuitBreakerState> GetCircuitBreakerStateAsync(string serviceName)
    {
        await Task.CompletedTask;
        return _circuitBreakers.GetValueOrDefault(serviceName, new CircuitBreakerState
        {
            ServiceName = serviceName,
            Status = CircuitBreakerStatus.Closed
        });
    }

    public async Task<bool> ExecuteWithCircuitBreakerAsync<T>(string serviceName, Func<Task<T>> operation)
    {
        try
        {
            var circuitBreaker = await GetCircuitBreakerStateAsync(serviceName);

            // Check if circuit breaker is open
            if (circuitBreaker.Status == CircuitBreakerStatus.Open)
            {
                if (DateTime.UtcNow < circuitBreaker.NextRetryTime)
                {
                    _logger.LogWarning($"Circuit breaker for {serviceName} is open. Skipping operation.");
                    return false;
                }
                else
                {
                    // Try to transition to half-open
                    circuitBreaker.Status = CircuitBreakerStatus.HalfOpen;
                    _logger.LogInformation($"Circuit breaker for {serviceName} transitioning to half-open");
                }
            }

            // Execute operation
            var result = await operation();

            // Operation succeeded
            if (circuitBreaker.Status == CircuitBreakerStatus.HalfOpen)
            {
                circuitBreaker.Status = CircuitBreakerStatus.Closed;
                circuitBreaker.FailureCount = 0;
                _logger.LogInformation($"Circuit breaker for {serviceName} closed after successful operation");
            }

            return true;
        }
        catch (Exception ex)
        {
            await HandleCircuitBreakerFailure(serviceName, ex);
            return false;
        }
    }

    private async Task HandleCircuitBreakerFailure(string serviceName, Exception ex)
    {
        try
        {
            var circuitBreaker = await GetCircuitBreakerStateAsync(serviceName);
            circuitBreaker.FailureCount++;
            circuitBreaker.LastFailure = DateTime.UtcNow;
            circuitBreaker.RecentErrors.Add($"{DateTime.UtcNow:HH:mm:ss}: {ex.Message}");

            // Keep only recent errors
            if (circuitBreaker.RecentErrors.Count > 10)
            {
                circuitBreaker.RecentErrors.RemoveAt(0);
            }

            var failureThreshold = _configuration.GetValue<int>($"CircuitBreaker:{serviceName}:FailureThreshold", 5);

            if (circuitBreaker.FailureCount >= failureThreshold)
            {
                circuitBreaker.Status = CircuitBreakerStatus.Open;
                circuitBreaker.NextRetryTime = DateTime.UtcNow.Add(circuitBreaker.Timeout);
                
                _logger.LogError($"Circuit breaker for {serviceName} opened after {circuitBreaker.FailureCount} failures");
                
                await LogCriticalEventAsync("CircuitBreakerOpened", 
                    $"Circuit breaker opened for {serviceName}", 
                    new Dictionary<string, object>
                    {
                        ["ServiceName"] = serviceName,
                        ["FailureCount"] = circuitBreaker.FailureCount,
                        ["LastError"] = ex.Message
                    });
            }
        }
        catch (Exception logEx)
        {
            _logger.LogError(logEx, $"Error handling circuit breaker failure for {serviceName}");
        }
    }

    public async Task<bool> ExecuteWithRetryAsync<T>(Func<Task<T>> operation, int maxRetries = 3)
    {
        var attempt = 0;
        var baseDelay = TimeSpan.FromMilliseconds(100);

        while (attempt < maxRetries)
        {
            try
            {
                await operation();
                return true;
            }
            catch (Exception ex)
            {
                attempt++;
                
                if (attempt >= maxRetries)
                {
                    _logger.LogError(ex, $"Operation failed after {maxRetries} attempts");
                    return false;
                }

                // Exponential backoff
                var delay = TimeSpan.FromMilliseconds(baseDelay.TotalMilliseconds * Math.Pow(2, attempt - 1));
                _logger.LogWarning($"Operation failed (attempt {attempt}/{maxRetries}). Retrying in {delay.TotalMilliseconds}ms. Error: {ex.Message}");
                
                await Task.Delay(delay);
            }
        }

        return false;
    }

    public async Task<bool> ValidateConfigurationAsync()
    {
        try
        {
            _logger.LogInformation("Validating configuration...");

            var validationErrors = new List<string>();

            // Validate Alpaca configuration
            var alpacaApiKey = _configuration["Alpaca:ApiKey"];
            var alpacaSecretKey = _configuration["Alpaca:SecretKey"];
            var alpacaBaseUrl = _configuration["Alpaca:BaseUrl"];

            if (string.IsNullOrEmpty(alpacaApiKey))
                validationErrors.Add("Alpaca API Key is missing");
            if (string.IsNullOrEmpty(alpacaSecretKey))
                validationErrors.Add("Alpaca Secret Key is missing");
            if (string.IsNullOrEmpty(alpacaBaseUrl))
                validationErrors.Add("Alpaca Base URL is missing");

            // Validate trading configuration
            var maxDailyLoss = _configuration.GetValue<decimal>("Trading:MaxDailyLoss", -1);
            var riskPerTrade = _configuration.GetValue<decimal>("Trading:RiskPerTrade", -1);

            if (maxDailyLoss <= 0)
                validationErrors.Add("Max daily loss must be greater than 0");
            if (riskPerTrade <= 0 || riskPerTrade > 1)
                validationErrors.Add("Risk per trade must be between 0 and 1");

            // Validate time settings
            var tradingStartTime = _configuration["Trading:TradingStartTime"];
            var tradingEndTime = _configuration["Trading:TradingEndTime"];

            if (!TimeSpan.TryParse(tradingStartTime, out _))
                validationErrors.Add("Invalid trading start time format");
            if (!TimeSpan.TryParse(tradingEndTime, out _))
                validationErrors.Add("Invalid trading end time format");

            if (validationErrors.Any())
            {
                _logger.LogError($"Configuration validation failed with {validationErrors.Count} errors:");
                foreach (var error in validationErrors)
                {
                    _logger.LogError($"  - {error}");
                }
                return false;
            }

            _logger.LogInformation("Configuration validation passed");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during configuration validation");
            return false;
        }
    }

    public async Task<Dictionary<string, object>> GetEnvironmentInfoAsync()
    {
        try
        {
            var info = new Dictionary<string, object>
            {
                ["MachineName"] = Environment.MachineName,
                ["OSVersion"] = Environment.OSVersion.ToString(),
                ["ProcessorCount"] = Environment.ProcessorCount,
                ["WorkingSet"] = Environment.WorkingSet,
                ["CLRVersion"] = Environment.Version.ToString(),
                ["CurrentDirectory"] = Environment.CurrentDirectory,
                ["ApplicationStartTime"] = Process.GetCurrentProcess().StartTime,
                ["Uptime"] = DateTime.UtcNow - Process.GetCurrentProcess().StartTime,
                ["Is64BitProcess"] = Environment.Is64BitProcess,
                ["Is64BitOperatingSystem"] = Environment.Is64BitOperatingSystem
            };

            return info;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting environment info");
            return new Dictionary<string, object>();
        }
    }

    public async Task<bool> PerformStartupChecksAsync()
    {
        try
        {
            _logger.LogInformation("Performing startup checks...");

            var checks = new List<(string Name, Func<Task<bool>> Check)>
            {
                ("System Resources", CheckSystemResourcesAsync),
                ("Network Connectivity", CheckNetworkConnectivityAsync),
                ("File System Access", CheckFileSystemAccessAsync),
                ("Required Directories", CheckRequiredDirectoriesAsync),
                ("Configuration Files", CheckConfigurationFilesAsync)
            };

            var failedChecks = new List<string>();

            foreach (var (name, check) in checks)
            {
                try
                {
                    _logger.LogDebug($"Running startup check: {name}");
                    var result = await check();
                    
                    if (!result)
                    {
                        failedChecks.Add(name);
                        _logger.LogError($"Startup check failed: {name}");
                    }
                    else
                    {
                        _logger.LogDebug($"Startup check passed: {name}");
                    }
                }
                catch (Exception ex)
                {
                    failedChecks.Add(name);
                    _logger.LogError(ex, $"Startup check error: {name}");
                }
            }

            if (failedChecks.Any())
            {
                _logger.LogError($"Startup checks failed: {string.Join(", ", failedChecks)}");
                return false;
            }

            _logger.LogInformation("All startup checks passed");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing startup checks");
            return false;
        }
    }

    public async Task<bool> PerformShutdownAsync()
    {
        try
        {
            _logger.LogInformation("Performing graceful shutdown...");

            await LogCriticalEventAsync("SystemShutdown", "Graceful shutdown initiated");

            // Close all circuit breakers
            foreach (var circuitBreaker in _circuitBreakers.Values)
            {
                circuitBreaker.Status = CircuitBreakerStatus.Open;
            }

            // Backup final configuration
            await BackupConfigurationAsync();

            _logger.LogInformation("Graceful shutdown completed");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during shutdown");
            return false;
        }
    }

    public async Task LogCriticalEventAsync(string eventType, string message, Dictionary<string, object>? metadata = null)
    {
        try
        {
            var criticalEvent = new CriticalEvent
            {
                Id = Guid.NewGuid().ToString(),
                EventType = eventType,
                Message = message,
                Timestamp = DateTime.UtcNow,
                Metadata = metadata ?? new Dictionary<string, object>()
            };

            _criticalEvents.Enqueue(criticalEvent);

            // Keep only recent events
            while (_criticalEvents.Count > 1000)
            {
                _criticalEvents.TryDequeue(out _);
            }

            // Log to file and console
            _logger.LogCritical($"[CRITICAL EVENT] {eventType}: {message}");

            // In a production system, you might also send to external monitoring systems
            await SendToExternalMonitoringAsync(criticalEvent);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error logging critical event");
        }
    }

    public async Task<bool> BackupConfigurationAsync()
    {
        try
        {
            var backupDir = Path.Combine(Environment.CurrentDirectory, "backups");
            Directory.CreateDirectory(backupDir);

            var timestamp = DateTime.UtcNow.ToString("yyyyMMdd_HHmmss");
            var backupPath = Path.Combine(backupDir, $"config_backup_{timestamp}.json");

            var configData = new Dictionary<string, object>();
            
            // Backup non-sensitive configuration
            foreach (var section in _configuration.GetChildren())
            {
                if (!IsSensitiveSection(section.Key))
                {
                    if (section.Value != null)
                    {
                        configData[section.Key] = section.Value;
                    }
                    else
                    {
                        configData[section.Key] = section.GetChildren().ToDictionary(c => c.Key, c => c.Value ?? string.Empty);
                    }
                }
            }

            var json = JsonSerializer.Serialize(configData, new JsonSerializerOptions { WriteIndented = true });
            await File.WriteAllTextAsync(backupPath, json);

            _logger.LogInformation($"Configuration backed up to: {backupPath}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error backing up configuration");
            return false;
        }
    }

    public async Task<bool> RestoreConfigurationAsync(string backupPath)
    {
        try
        {
            if (!File.Exists(backupPath))
            {
                _logger.LogError($"Backup file not found: {backupPath}");
                return false;
            }

            var json = await File.ReadAllTextAsync(backupPath);
            var configData = JsonSerializer.Deserialize<Dictionary<string, object>>(json);

            if (configData == null)
            {
                _logger.LogError("Failed to deserialize backup configuration");
                return false;
            }

            _logger.LogInformation($"Configuration restored from: {backupPath}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error restoring configuration from {backupPath}");
            return false;
        }
    }

    // Private helper methods
    private bool IsSensitiveSection(string sectionKey)
    {
        var sensitiveSections = new[] { "Alpaca", "ConnectionStrings", "Secrets" };
        return sensitiveSections.Any(s => sectionKey.StartsWith(s, StringComparison.OrdinalIgnoreCase));
    }

    private async Task<bool> CheckSystemResourcesAsync()
    {
        try
        {
            var availableMemory = GC.GetTotalMemory(false);
            var minRequiredMemory = 100 * 1024 * 1024; // 100 MB

            return availableMemory > minRequiredMemory;
        }
        catch
        {
            return false;
        }
    }

    private async Task<bool> CheckNetworkConnectivityAsync()
    {
        try
        {
            using var client = new HttpClient();
            client.Timeout = TimeSpan.FromSeconds(10);
            var response = await client.GetAsync("https://www.google.com");
            return response.IsSuccessStatusCode;
        }
        catch
        {
            return false;
        }
    }

    private async Task<bool> CheckFileSystemAccessAsync()
    {
        try
        {
            var testFile = Path.Combine(Path.GetTempPath(), $"test_{Guid.NewGuid()}.tmp");
            await File.WriteAllTextAsync(testFile, "test");
            File.Delete(testFile);
            return true;
        }
        catch
        {
            return false;
        }
    }

    private async Task<bool> CheckRequiredDirectoriesAsync()
    {
        try
        {
            var requiredDirs = new[] { "logs", "backups", "data" };
            
            foreach (var dir in requiredDirs)
            {
                var fullPath = Path.Combine(Environment.CurrentDirectory, dir);
                if (!Directory.Exists(fullPath))
                {
                    Directory.CreateDirectory(fullPath);
                }
            }
            
            return true;
        }
        catch
        {
            return false;
        }
    }

    private async Task<bool> CheckConfigurationFilesAsync()
    {
        try
        {
            var configFile = Path.Combine(Environment.CurrentDirectory, "appsettings.json");
            return File.Exists(configFile);
        }
        catch
        {
            return false;
        }
    }

    private async Task SendToExternalMonitoringAsync(CriticalEvent criticalEvent)
    {
        // Placeholder for external monitoring integration
        // Could send to services like DataDog, New Relic, Application Insights, etc.
        await Task.CompletedTask;
    }
}

// Supporting classes
public class CriticalEvent
{
    public string Id { get; set; } = string.Empty;
    public string EventType { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}
