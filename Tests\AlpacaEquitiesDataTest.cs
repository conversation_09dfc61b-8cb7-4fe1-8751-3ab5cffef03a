using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Serilog;
using ZeroDateStrat.Services;

namespace ZeroDateStrat.Tests;

public static class AlpacaEquitiesDataTest
{
    public static async Task RunAlpacaEquitiesDataTest()
    {
        Console.WriteLine("🔍 Starting Alpaca Equities Data Test");
        Console.WriteLine("Testing real-time data retrieval for major equities using Alpaca Algo Trader Plus");
        Console.WriteLine();

        try
        {
            // Setup configuration
            var configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json", optional: false)
                .AddEnvironmentVariables()
                .Build();

            // Setup logging
            Log.Logger = new LoggerConfiguration()
                .MinimumLevel.Information()
                .WriteTo.Console()
                .CreateLogger();

            // Setup DI container
            var services = new ServiceCollection();
            services.AddSingleton<IConfiguration>(configuration);
            services.AddLogging(builder => builder.AddSerilog());
            services.AddSingleton<ISecurityService, SecurityService>();
            services.AddSingleton<IPolygonDataService, PolygonDataService>();
            services.AddHttpClient<IPolygonDataService, PolygonDataService>();
            services.AddSingleton<IGlobalExceptionHandler>(provider =>
            {
                var logger = provider.GetRequiredService<ILogger<GlobalExceptionHandler>>();
                return new GlobalExceptionHandler(logger, null);
            });
            services.AddSingleton<IAlpacaService, AlpacaService>();

            var serviceProvider = services.BuildServiceProvider();

            // Get services
            var alpacaService = serviceProvider.GetRequiredService<IAlpacaService>();

            Console.WriteLine("1. Initializing Alpaca service...");
            var initialized = await alpacaService.InitializeAsync();
            if (!initialized)
            {
                Console.WriteLine("❌ Failed to initialize Alpaca service");
                return;
            }
            Console.WriteLine("✅ Alpaca service initialized successfully");
            Console.WriteLine();

            // Test major equities
            var equities = new[]
            {
                "SPY",   // S&P 500 ETF
                "QQQ",   // NASDAQ ETF
                "IWM",   // Russell 2000 ETF
                "AAPL",  // Apple
                "MSFT",  // Microsoft
                "GOOGL", // Google
                "TSLA",  // Tesla
                "NVDA",  // NVIDIA
                "META",  // Meta
                "AMZN"   // Amazon
            };

            Console.WriteLine("2. Testing real-time equity prices from Alpaca...");
            Console.WriteLine("Symbol    Price      Status");
            Console.WriteLine("--------------------------------");

            var successCount = 0;
            var totalCount = equities.Length;

            foreach (var symbol in equities)
            {
                try
                {
                    var price = await alpacaService.GetCurrentPriceAsync(symbol);
                    
                    if (price > 0)
                    {
                        Console.WriteLine($"{symbol,-8} ${price,8:F2}   ✅ Real-time");
                        successCount++;
                    }
                    else
                    {
                        Console.WriteLine($"{symbol,-8} ${"N/A",8}   ❌ No data");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"{symbol,-8} ${"ERROR",8}   ❌ {ex.Message}");
                }

                // Small delay to avoid rate limiting
                await Task.Delay(100);
            }

            Console.WriteLine("--------------------------------");
            Console.WriteLine($"Success Rate: {successCount}/{totalCount} ({(double)successCount/totalCount*100:F1}%)");
            Console.WriteLine();

            // Test account information
            Console.WriteLine("3. Testing account information...");
            var account = await alpacaService.GetAccountAsync();
            if (account != null)
            {
                Console.WriteLine($"✅ Account Number: {account.AccountNumber}");
                Console.WriteLine($"✅ Equity: {account.Equity:C}");
                Console.WriteLine($"✅ Buying Power: {account.BuyingPower:C}");
                Console.WriteLine($"✅ Day Trading Buying Power: {account.DayTradingBuyingPower:C}");
            }
            else
            {
                Console.WriteLine("❌ Failed to retrieve account information");
            }

            Console.WriteLine();

            // Test VIX proxy calculation
            Console.WriteLine("4. Testing VIX proxy calculation...");
            var vixPrice = await alpacaService.GetCurrentPriceAsync("VIX");
            Console.WriteLine($"VIX Proxy Price: ${vixPrice:F2}");
            
            if (vixPrice > 0)
            {
                Console.WriteLine("✅ VIX proxy calculation working");
                
                // Show which ETFs are being used
                var vxxPrice = await alpacaService.GetCurrentPriceAsync("VXX");
                var vixyPrice = await alpacaService.GetCurrentPriceAsync("VIXY");
                var uvxyPrice = await alpacaService.GetCurrentPriceAsync("UVXY");
                
                Console.WriteLine($"   VXX:  ${vxxPrice:F2}");
                Console.WriteLine($"   VIXY: ${vixyPrice:F2}");
                Console.WriteLine($"   UVXY: ${uvxyPrice:F2}");
            }
            else
            {
                Console.WriteLine("⚠️ VIX proxy using fallback value");
            }

            Console.WriteLine();
            Console.WriteLine("🎯 Test Summary:");
            Console.WriteLine($"   Alpaca Connection: ✅ Working");
            Console.WriteLine($"   Real-time Equity Data: ✅ {successCount}/{totalCount} symbols");
            Console.WriteLine($"   Account Access: ✅ Working");
            Console.WriteLine($"   VIX Proxy: ✅ Working");
            Console.WriteLine();
            Console.WriteLine("✅ Alpaca Equities Data Test Completed Successfully!");
            
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Test failed with error: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
    }
}
