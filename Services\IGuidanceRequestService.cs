using ZeroDateStrat.Models;

namespace ZeroDateStrat.Services;

/// <summary>
/// Service for requesting external guidance from @ChatGptBot in Discord
/// </summary>
public interface IGuidanceRequestService
{
    /// <summary>
    /// Send a plaintext guidance request to @ChatGptBot
    /// </summary>
    /// <param name="taskStatement">The task or problem statement</param>
    /// <param name="language">Programming language (default: C#)</param>
    /// <param name="components">List of technical components involved</param>
    /// <param name="notes">Additional notes or context</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Guidance response from @ChatGptBot</returns>
    Task<GuidanceResponse?> RequestGuidanceAsync(
        string taskStatement,
        string language = "C#",
        List<string>? components = null,
        string notes = "",
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Send a structured JSON guidance request to @ChatGptBot
    /// </summary>
    /// <param name="request">Structured guidance request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Guidance response from @ChatGptBot</returns>
    Task<GuidanceResponse?> RequestStructuredGuidanceAsync(
        StructuredGuidanceRequest request,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Send a custom guidance request with full control over formatting
    /// </summary>
    /// <param name="guidanceRequest">Complete guidance request object</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Guidance response from @ChatGptBot</returns>
    Task<GuidanceResponse?> SendGuidanceRequestAsync(
        GuidanceRequest guidanceRequest,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get the status of a guidance request
    /// </summary>
    /// <param name="requestId">Request ID</param>
    /// <returns>Current status of the request</returns>
    Task<GuidanceRequestStatus> GetRequestStatusAsync(string requestId);

    /// <summary>
    /// Get a stored guidance response by request ID
    /// </summary>
    /// <param name="requestId">Request ID</param>
    /// <returns>Stored guidance response if found</returns>
    Task<GuidanceResponse?> GetStoredResponseAsync(string requestId);

    /// <summary>
    /// Get all stored guidance responses
    /// </summary>
    /// <returns>List of all stored responses</returns>
    Task<List<GuidanceResponse>> GetAllStoredResponsesAsync();

    /// <summary>
    /// Cancel a pending guidance request
    /// </summary>
    /// <param name="requestId">Request ID to cancel</param>
    /// <returns>True if successfully cancelled</returns>
    Task<bool> CancelRequestAsync(string requestId);

    /// <summary>
    /// Clear old stored responses based on age
    /// </summary>
    /// <param name="olderThan">Remove responses older than this timespan</param>
    /// <returns>Number of responses removed</returns>
    Task<int> CleanupOldResponsesAsync(TimeSpan olderThan);

    /// <summary>
    /// Event fired when a guidance response is received
    /// </summary>
    event Func<GuidanceResponse, Task> OnGuidanceResponseReceived;

    /// <summary>
    /// Event fired when a guidance request times out
    /// </summary>
    event Func<GuidanceRequest, Task> OnGuidanceRequestTimeout;

    /// <summary>
    /// Event fired when a guidance request fails
    /// </summary>
    event Func<GuidanceRequest, Exception, Task> OnGuidanceRequestFailed;
}
