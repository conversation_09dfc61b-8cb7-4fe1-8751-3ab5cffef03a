using System.Text.Json.Serialization;

namespace ZeroDateStrat.Models;

/// <summary>
/// Represents a request for external guidance from @ChatGptBot
/// </summary>
public class GuidanceRequest
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string TaskStatement { get; set; } = string.Empty;
    public string Language { get; set; } = "C#";
    public List<string> TechnicalRequirements { get; set; } = new();
    public List<string> Components { get; set; } = new();
    public string Notes { get; set; } = string.Empty;
    public GuidanceRequestFormat Format { get; set; } = GuidanceRequestFormat.Plaintext;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime? SentAt { get; set; }
    public DateTime? ResponseReceivedAt { get; set; }
    public GuidanceRequestStatus Status { get; set; } = GuidanceRequestStatus.Pending;
    public int RetryCount { get; set; } = 0;
    public int MaxRetries { get; set; } = 3;
    public TimeSpan ResponseTimeout { get; set; } = TimeSpan.FromSeconds(30);
    public string? Response { get; set; }
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Format options for guidance requests
/// </summary>
public enum GuidanceRequestFormat
{
    Plaintext,
    StructuredJson
}

/// <summary>
/// Status of a guidance request
/// </summary>
public enum GuidanceRequestStatus
{
    Pending,
    Sent,
    ResponseReceived,
    Timeout,
    Failed,
    Cancelled
}

/// <summary>
/// Structured JSON format for guidance requests
/// </summary>
public class StructuredGuidanceRequest
{
    [JsonPropertyName("intent")]
    public string Intent { get; set; } = "request_instruction";
    
    [JsonPropertyName("topic")]
    public string Topic { get; set; } = string.Empty;
    
    [JsonPropertyName("language")]
    public string Language { get; set; } = "C#";
    
    [JsonPropertyName("components")]
    public List<string> Components { get; set; } = new();
    
    [JsonPropertyName("notes")]
    public string Notes { get; set; } = string.Empty;
    
    [JsonPropertyName("requirements")]
    public List<string> Requirements { get; set; } = new();
}

/// <summary>
/// Response from @ChatGptBot
/// </summary>
public class GuidanceResponse
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string RequestId { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public DateTime ReceivedAt { get; set; } = DateTime.UtcNow;
    public string AuthorId { get; set; } = string.Empty;
    public string AuthorUsername { get; set; } = string.Empty;
    public ulong MessageId { get; set; }
    public bool IsFromChatGptBot { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Configuration for guidance request service
/// </summary>
public class GuidanceRequestConfiguration
{
    public bool Enabled { get; set; } = true;
    public string ChatGptBotMention { get; set; } = "@ChatGptBot";
    public string ChatGptBotUserId { get; set; } = string.Empty; // Discord user ID if known
    public TimeSpan DefaultTimeout { get; set; } = TimeSpan.FromSeconds(30);
    public int DefaultMaxRetries { get; set; } = 3;
    public bool StoreResponses { get; set; } = true;
    public string ResponseStoragePath { get; set; } = "GuidanceResponses";
    public bool LogRequests { get; set; } = true;
    public bool EnableRetryLogic { get; set; } = true;
}
