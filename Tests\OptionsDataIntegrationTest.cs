using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using ZeroDateStrat.Services;

namespace ZeroDateStrat.Tests;

public class OptionsDataIntegrationTest
{
    private readonly ILogger<AlpacaService> _logger;
    private readonly IConfiguration _configuration;

    public OptionsDataIntegrationTest()
    {
        // Create a simple logger for testing
        var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
        _logger = loggerFactory.CreateLogger<AlpacaService>();

        // Create configuration
        var configBuilder = new ConfigurationBuilder()
            .AddJsonFile("appsettings.json", optional: false);
        _configuration = configBuilder.Build();
    }

    public async Task TestRealDataIntegration()
    {
        Console.WriteLine("=== Testing Real Options Data Integration ===\n");

        // Create mock services for testing
        var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
        var securityLogger = loggerFactory.CreateLogger<SecurityService>();
        var exceptionLogger = loggerFactory.CreateLogger<GlobalExceptionHandler>();

        var securityService = new SecurityService(securityLogger, _configuration);
        var exceptionHandler = new GlobalExceptionHandler(exceptionLogger);

        // Create a mock PolygonDataService for testing
        var polygonLogger = loggerFactory.CreateLogger<PolygonDataService>();
        var httpClient = new HttpClient();
        var polygonService = new PolygonDataService(_configuration, polygonLogger, httpClient, securityService);

        var alpacaService = new AlpacaService(_logger, _configuration, securityService, exceptionHandler, polygonService);

        try
        {
            // Test Alpaca connection
            Console.WriteLine("1. Testing Alpaca Connection...");
            var connected = await alpacaService.InitializeAsync();
            Console.WriteLine($"   Connection Status: {(connected ? "SUCCESS" : "FAILED")}");

            if (!connected)
            {
                Console.WriteLine("   Cannot proceed without Alpaca connection");
                return;
            }

            // Test real underlying price retrieval
            Console.WriteLine("\n2. Testing Real Underlying Price Retrieval...");
            var symbols = new[] { "SPY", "QQQ", "IWM" };
            
            foreach (var symbol in symbols)
            {
                try
                {
                    var price = await alpacaService.GetCurrentPriceAsync(symbol);
                    Console.WriteLine($"   {symbol}: ${price:F2}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   {symbol}: ERROR - {ex.Message}");
                }
            }

            // Test option chain generation with real underlying prices
            Console.WriteLine("\n3. Testing Option Chain Generation with Real Prices...");
            var today = DateTime.Today;
            
            try
            {
                var optionChain = await alpacaService.GetOptionChainAsync("SPY", today);
                Console.WriteLine($"   Generated {optionChain.Count} option contracts for SPY");
                
                if (optionChain.Any())
                {
                    var atmCall = optionChain
                        .Where(o => o.OptionType == ZeroDateStrat.Models.OptionType.Call)
                        .OrderBy(o => Math.Abs(o.StrikePrice - optionChain.First().LastPrice))
                        .FirstOrDefault();
                    
                    if (atmCall != null)
                    {
                        Console.WriteLine($"   Sample ATM Call: {atmCall.Symbol}");
                        Console.WriteLine($"     Strike: ${atmCall.StrikePrice:F2}");
                        Console.WriteLine($"     Bid/Ask: ${atmCall.Bid:F2}/${atmCall.Ask:F2}");
                        Console.WriteLine($"     Delta: {atmCall.Delta:F3}");
                        Console.WriteLine($"     IV: {atmCall.ImpliedVolatility:P1}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ERROR: {ex.Message}");
            }

            // Test account information
            Console.WriteLine("\n4. Testing Account Information...");
            try
            {
                var account = await alpacaService.GetAccountAsync();
                if (account != null)
                {
                    Console.WriteLine($"   Account Number: {account.AccountNumber}");
                    Console.WriteLine($"   Equity: ${account.Equity:F2}");
                    Console.WriteLine($"   Buying Power: ${account.BuyingPower:F2}");
                    Console.WriteLine($"   Day Trading Buying Power: ${account.DayTradingBuyingPower:F2}");
                }
                else
                {
                    Console.WriteLine("   Could not retrieve account information");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ERROR: {ex.Message}");
            }

            Console.WriteLine("\n=== Integration Test Completed ===");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Test failed with error: {ex.Message}");
        }
        finally
        {
            alpacaService.Dispose();
        }
    }

    public static async Task RunIntegrationTest()
    {
        var test = new OptionsDataIntegrationTest();
        await test.TestRealDataIntegration();
    }
}
