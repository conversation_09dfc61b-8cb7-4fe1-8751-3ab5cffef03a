using System;
using System.Collections.Generic;
using System.Linq;

namespace ZeroDateStrat.Models
{
    public class SafetyValidationResult
    {
        public List<ValidationCheck> Checks { get; set; } = new List<ValidationCheck>();
        public List<string> Recommendations { get; set; } = new List<string>();
        public decimal OverallScore { get; set; }
        public DateTime ValidationTime { get; set; } = DateTime.UtcNow;

        public int PassCount => Checks.Count(c => c.Status == ValidationStatus.Pass);
        public int WarningCount => Checks.Count(c => c.Status == ValidationStatus.Warning);
        public int FailureCount => Checks.Count(c => c.Status == ValidationStatus.Fail);
        public int InfoCount => Checks.Count(c => c.Status == ValidationStatus.Info);

        public void AddPass(string checkName, string message)
        {
            Checks.Add(new ValidationCheck
            {
                Name = checkName,
                Status = ValidationStatus.Pass,
                Message = message
            });
        }

        public void AddWarning(string checkName, string message)
        {
            Checks.Add(new ValidationCheck
            {
                Name = checkName,
                Status = ValidationStatus.Warning,
                Message = message
            });
        }

        public void AddFail(string checkName, string message)
        {
            Checks.Add(new ValidationCheck
            {
                Name = checkName,
                Status = ValidationStatus.Fail,
                Message = message
            });
        }

        public void AddInfo(string checkName, string message)
        {
            Checks.Add(new ValidationCheck
            {
                Name = checkName,
                Status = ValidationStatus.Info,
                Message = message
            });
        }

        public void CalculateOverallScore()
        {
            if (Checks.Count == 0)
            {
                OverallScore = 0;
                return;
            }

            var totalPoints = 0;
            var maxPoints = 0;

            foreach (var check in Checks)
            {
                maxPoints += 100;
                totalPoints += check.Status switch
                {
                    ValidationStatus.Pass => 100,
                    ValidationStatus.Warning => 60,
                    ValidationStatus.Info => 80,
                    ValidationStatus.Fail => 0,
                    _ => 0
                };
            }

            OverallScore = maxPoints > 0 ? (decimal)totalPoints / maxPoints * 100 : 0;
        }

        public string GetOverallStatus()
        {
            if (FailureCount > 0)
                return "🚨 CRITICAL ISSUES - NOT SAFE FOR TRADING";
            
            if (OverallScore >= 90)
                return "✅ EXCELLENT - Ready for production";
            
            if (OverallScore >= 80)
                return "✅ GOOD - Safe for trading with monitoring";
            
            if (OverallScore >= 70)
                return "⚠️ ACCEPTABLE - Proceed with caution";
            
            if (OverallScore >= 60)
                return "⚠️ NEEDS IMPROVEMENT - Address warnings";
            
            return "🚨 POOR - Significant improvements needed";
        }

        public bool IsSafeForTrading()
        {
            return FailureCount == 0 && OverallScore >= 70;
        }

        public bool IsSafeForPaperTrading()
        {
            return FailureCount <= 1 && OverallScore >= 60;
        }

        public string GenerateReport()
        {
            var report = $@"
🔍 SAFETY VALIDATION REPORT
Generated: {ValidationTime:yyyy-MM-dd HH:mm:ss} UTC

📊 OVERALL RESULTS:
   Score: {OverallScore:F1}%
   Status: {GetOverallStatus()}
   
📈 SUMMARY:
   ✅ Passed: {PassCount}
   ⚠️ Warnings: {WarningCount}
   🚨 Failed: {FailureCount}
   ℹ️ Info: {InfoCount}

📋 DETAILED RESULTS:
";

            foreach (var check in Checks.OrderBy(c => c.Status))
            {
                var icon = check.Status switch
                {
                    ValidationStatus.Pass => "✅",
                    ValidationStatus.Warning => "⚠️",
                    ValidationStatus.Fail => "🚨",
                    ValidationStatus.Info => "ℹ️",
                    _ => "❓"
                };
                
                report += $"   {icon} {check.Name}: {check.Message}\n";
            }

            if (Recommendations.Any())
            {
                report += "\n💡 RECOMMENDATIONS:\n";
                foreach (var recommendation in Recommendations)
                {
                    report += $"   {recommendation}\n";
                }
            }

            report += $"\n🎯 TRADING READINESS:\n";
            report += $"   Paper Trading: {(IsSafeForPaperTrading() ? "✅ Ready" : "❌ Not Ready")}\n";
            report += $"   Live Trading: {(IsSafeForTrading() ? "✅ Ready" : "❌ Not Ready")}\n";

            return report;
        }
    }

    public class ValidationCheck
    {
        public string Name { get; set; } = string.Empty;
        public ValidationStatus Status { get; set; }
        public string Message { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    public enum ValidationStatus
    {
        Pass,
        Warning,
        Fail,
        Info
    }
}
