using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using System.Diagnostics;

namespace ZeroDateStrat.Services;

public class TradingSystemHealthCheck : IHealthCheck
{
    private readonly IAlpacaService _alpacaService;
    private readonly IPolygonDataService _polygonDataService;
    private readonly IHighPerformanceCacheService _cacheService;
    private readonly ILogger<TradingSystemHealthCheck> _logger;

    public TradingSystemHealthCheck(
        IAlpacaService alpacaService,
        IPolygonDataService polygonDataService,
        IHighPerformanceCacheService cacheService,
        ILogger<TradingSystemHealthCheck> logger)
    {
        _alpacaService = alpacaService;
        _polygonDataService = polygonDataService;
        _cacheService = cacheService;
        _logger = logger;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        var healthData = new Dictionary<string, object>();
        var issues = new List<string>();

        try
        {
            // Check system performance
            await CheckSystemPerformance(healthData, issues);

            // Check Alpaca connection
            await CheckAlpacaConnection(healthData, issues);

            // Check market data services
            await CheckMarketDataServices(healthData, issues);

            // Check cache performance
            CheckCachePerformance(healthData, issues);

            // Check memory usage
            CheckMemoryUsage(healthData, issues);

            // Determine overall health status
            var status = issues.Count switch
            {
                0 => HealthStatus.Healthy,
                <= 2 => HealthStatus.Degraded,
                _ => HealthStatus.Unhealthy
            };

            var description = status switch
            {
                HealthStatus.Healthy => "All systems operational",
                HealthStatus.Degraded => $"Minor issues detected: {string.Join(", ", issues)}",
                _ => $"Critical issues detected: {string.Join(", ", issues)}"
            };

            return new HealthCheckResult(status, description, data: healthData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Health check failed with exception");
            return new HealthCheckResult(HealthStatus.Unhealthy, "Health check failed", ex, healthData);
        }
    }

    private async Task CheckSystemPerformance(Dictionary<string, object> healthData, List<string> issues)
    {
        try
        {
            var process = Process.GetCurrentProcess();
            var cpuUsage = await GetCpuUsageAsync();
            
            healthData["cpu_usage_percent"] = cpuUsage;
            healthData["memory_usage_mb"] = process.WorkingSet64 / 1024 / 1024;
            healthData["thread_count"] = process.Threads.Count;
            healthData["uptime_hours"] = (DateTime.Now - process.StartTime).TotalHours;

            // Check for performance issues
            if (cpuUsage > 80)
            {
                issues.Add($"High CPU usage: {cpuUsage:F1}%");
            }

            if (process.WorkingSet64 > 8L * 1024 * 1024 * 1024) // 8GB (25% of 32GB)
            {
                issues.Add($"High memory usage: {process.WorkingSet64 / 1024 / 1024}MB");
            }

            if (process.Threads.Count > 100)
            {
                issues.Add($"High thread count: {process.Threads.Count}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Could not check system performance");
            issues.Add("System performance check failed");
        }
    }

    private async Task CheckAlpacaConnection(Dictionary<string, object> healthData, List<string> issues)
    {
        try
        {
            var stopwatch = Stopwatch.StartNew();
            var account = await _alpacaService.GetAccountAsync();
            stopwatch.Stop();

            healthData["alpaca_connection"] = "Connected";
            healthData["alpaca_response_time_ms"] = stopwatch.ElapsedMilliseconds;
            healthData["account_equity"] = account?.Equity ?? 0;
            healthData["account_buying_power"] = account?.BuyingPower ?? 0;

            if (stopwatch.ElapsedMilliseconds > 5000) // 5 seconds
            {
                issues.Add($"Slow Alpaca response: {stopwatch.ElapsedMilliseconds}ms");
            }

            if (account == null)
            {
                issues.Add("Could not retrieve account information");
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Alpaca connection check failed");
            healthData["alpaca_connection"] = "Failed";
            issues.Add("Alpaca connection failed");
        }
    }

    private async Task CheckMarketDataServices(Dictionary<string, object> healthData, List<string> issues)
    {
        try
        {
            // Check Polygon.io VIX data
            var stopwatch = Stopwatch.StartNew();
            var vix = await _polygonDataService.GetCurrentVixAsync();
            stopwatch.Stop();

            healthData["polygon_response_time_ms"] = stopwatch.ElapsedMilliseconds;
            healthData["vix_value"] = vix;

            if (stopwatch.ElapsedMilliseconds > 3000) // 3 seconds
            {
                issues.Add($"Slow Polygon response: {stopwatch.ElapsedMilliseconds}ms");
            }

            if (vix <= 0 || vix > 100)
            {
                issues.Add($"Invalid VIX value: {vix}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Market data service check failed");
            healthData["polygon_connection"] = "Failed";
            issues.Add("Market data service failed");
        }
    }

    private void CheckCachePerformance(Dictionary<string, object> healthData, List<string> issues)
    {
        try
        {
            var stats = _cacheService.GetStatistics();
            
            healthData["cache_hit_ratio"] = stats.HitRatio;
            healthData["cache_entry_count"] = stats.EntryCount;
            healthData["cache_memory_usage_mb"] = stats.TotalMemoryUsage / 1024 / 1024;

            if (stats.HitRatio < 0.7) // Less than 70% hit ratio
            {
                issues.Add($"Low cache hit ratio: {stats.HitRatio:P1}");
            }

            if (stats.EntryCount > 100000) // Increased from 10K to 100K for larger cache
            {
                issues.Add($"High cache entry count: {stats.EntryCount}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Cache performance check failed");
            issues.Add("Cache performance check failed");
        }
    }

    private void CheckMemoryUsage(Dictionary<string, object> healthData, List<string> issues)
    {
        try
        {
            var gcMemory = GC.GetTotalMemory(false);
            var gen0Collections = GC.CollectionCount(0);
            var gen1Collections = GC.CollectionCount(1);
            var gen2Collections = GC.CollectionCount(2);

            healthData["gc_memory_mb"] = gcMemory / 1024 / 1024;
            healthData["gc_gen0_collections"] = gen0Collections;
            healthData["gc_gen1_collections"] = gen1Collections;
            healthData["gc_gen2_collections"] = gen2Collections;

            // Check for excessive garbage collection
            if (gen2Collections > 100)
            {
                issues.Add($"High Gen2 GC collections: {gen2Collections}");
            }

            if (gcMemory > 4L * 1024 * 1024 * 1024) // 4GB (increased for larger cache)
            {
                issues.Add($"High GC memory usage: {gcMemory / 1024 / 1024}MB");
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Memory usage check failed");
            issues.Add("Memory usage check failed");
        }
    }

    private async Task<double> GetCpuUsageAsync()
    {
        try
        {
            using var process = Process.GetCurrentProcess();
            var startTime = DateTime.UtcNow;
            var startCpuUsage = process.TotalProcessorTime;

            await Task.Delay(500); // Wait 500ms

            var endTime = DateTime.UtcNow;
            var endCpuUsage = process.TotalProcessorTime;

            var cpuUsedMs = (endCpuUsage - startCpuUsage).TotalMilliseconds;
            var totalMsPassed = (endTime - startTime).TotalMilliseconds;
            var cpuUsageTotal = cpuUsedMs / (Environment.ProcessorCount * totalMsPassed);

            return cpuUsageTotal * 100;
        }
        catch
        {
            return 0; // Return 0 if we can't measure CPU usage
        }
    }
}

public class MemoryHealthCheck : IHealthCheck
{
    private readonly ILogger<MemoryHealthCheck> _logger;

    public MemoryHealthCheck(ILogger<MemoryHealthCheck> logger)
    {
        _logger = logger;
    }

    public Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            var process = Process.GetCurrentProcess();
            var memoryUsageMB = process.WorkingSet64 / 1024 / 1024;
            
            // For 32GB system with 6GB cache, warn at 8GB, critical at 12GB
            var status = memoryUsageMB switch
            {
                < 8192 => HealthStatus.Healthy,   // < 8GB
                < 12288 => HealthStatus.Degraded, // < 12GB
                _ => HealthStatus.Unhealthy       // >= 12GB
            };

            var data = new Dictionary<string, object>
            {
                ["memory_usage_mb"] = memoryUsageMB,
                ["memory_limit_mb"] = 12288, // 12GB warning threshold
                ["memory_usage_percent"] = (memoryUsageMB / 32768.0) * 100, // Percentage of total 32GB
                ["cache_limit_mb"] = 6144 // 6GB cache limit
            };

            return Task.FromResult(new HealthCheckResult(status, $"Memory usage: {memoryUsageMB}MB", data: data));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Memory health check failed");
            return Task.FromResult(new HealthCheckResult(HealthStatus.Unhealthy, "Memory check failed", ex));
        }
    }
}
