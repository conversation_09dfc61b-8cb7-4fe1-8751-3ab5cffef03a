using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using ZeroDateStrat.Services;
using ILogger = Serilog.ILogger;

namespace ZeroDateStrat.Tests;

/// <summary>
/// Test to verify proper handling of Polygon index data vs tradeable securities
/// Ensures indices don't request bid/ask quotes inappropriately
/// </summary>
public class PolygonIndexDataHandlingTest
{
    public static async Task Main(string[] args)
    {
        // Configure Serilog
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Debug()
            .WriteTo.Console()
            .WriteTo.File($"logs/polygon-index-handling-test-{DateTime.Now:yyyyMMdd-HHmmss}.txt")
            .CreateLogger();

        var logger = Log.Logger;

        try
        {
            logger.Information("=== Polygon Index Data Handling Test ===");
            logger.Information("Testing proper handling of indices vs tradeable securities");

            // Build host with services
            var host = CreateHost();
            var polygonService = host.Services.GetRequiredService<IPolygonDataService>();

            // Test connection first
            logger.Information("\n1. Testing Polygon connection...");
            var connectionTest = await polygonService.TestConnectionAsync();
            if (!connectionTest)
            {
                logger.Error("❌ Polygon connection failed - cannot proceed with tests");
                return;
            }
            logger.Information("✅ Polygon connection successful");

            // Test index symbols (should use last trade, not bid/ask)
            logger.Information("\n2. Testing Index Symbols (should use last trade price)...");
            await TestIndexSymbols(polygonService, logger);

            // Test tradeable securities (should use real bid/ask)
            logger.Information("\n3. Testing Tradeable Securities (should use real bid/ask)...");
            await TestTradeableSecurities(polygonService, logger);

            // Test VIX specifically
            logger.Information("\n4. Testing VIX Index Handling...");
            await TestVixHandling(polygonService, logger);

            logger.Information("\n=== Test Complete ===");
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Test failed with exception");
        }
        finally
        {
            Log.CloseAndFlush();
        }
    }

    private static IHost CreateHost()
    {
        var configuration = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: false)
            .AddJsonFile("appsettings.production.json", optional: true)
            .AddEnvironmentVariables()
            .Build();

        return Host.CreateDefaultBuilder()
            .ConfigureServices(services =>
            {
                services.AddSingleton<IConfiguration>(configuration);
                services.AddLogging(builder => builder.AddSerilog());
                services.AddHttpClient();
                services.AddSingleton<ISecurityService, SecurityService>();
                services.AddSingleton<IPolygonDataService, PolygonDataService>();
            })
            .Build();
    }

    private static async Task TestIndexSymbols(IPolygonDataService polygonService, ILogger logger)
    {
        var indexSymbols = new[] { "I:SPX", "I:VIX", "I:NDX", "I:RUT" };

        foreach (var symbol in indexSymbols)
        {
            try
            {
                logger.Information($"Testing index symbol: {symbol}");
                var quote = await polygonService.GetCurrentQuoteAsync(symbol);
                
                if (quote != null && quote.Bid > 0)
                {
                    if (quote.Bid == quote.Ask)
                    {
                        logger.Information($"✅ {symbol} - Correctly handled as index: Price={quote.Bid:F2} (last trade)");
                    }
                    else
                    {
                        logger.Warning($"⚠️ {symbol} - Unexpected: Bid={quote.Bid:F2}, Ask={quote.Ask:F2} (should be same for index)");
                    }
                }
                else
                {
                    logger.Warning($"⚠️ {symbol} - No data returned");
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex, $"❌ {symbol} - Error: {ex.Message}");
            }
        }
    }

    private static async Task TestTradeableSecurities(IPolygonDataService polygonService, ILogger logger)
    {
        var tradeableSymbols = new[] { "SPY", "QQQ", "IWM", "AAPL" };

        foreach (var symbol in tradeableSymbols)
        {
            try
            {
                logger.Information($"Testing tradeable symbol: {symbol}");
                var quote = await polygonService.GetCurrentQuoteAsync(symbol);
                
                if (quote != null && quote.Bid > 0 && quote.Ask > 0)
                {
                    var spread = quote.Ask - quote.Bid;
                    var spreadPct = quote.SpreadPercentage;
                    
                    if (spread > 0)
                    {
                        logger.Information($"✅ {symbol} - Real bid/ask: Bid={quote.Bid:F2}, Ask={quote.Ask:F2}, Spread={spreadPct:F3}%");
                    }
                    else
                    {
                        logger.Warning($"⚠️ {symbol} - Zero spread (unusual): Bid={quote.Bid:F2}, Ask={quote.Ask:F2}");
                    }
                }
                else
                {
                    logger.Warning($"⚠️ {symbol} - No valid quote data");
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex, $"❌ {symbol} - Error: {ex.Message}");
            }
        }
    }

    private static async Task TestVixHandling(IPolygonDataService polygonService, ILogger logger)
    {
        try
        {
            // Test VIX through dedicated method
            logger.Information("Testing VIX through GetCurrentVixAsync()...");
            var vixValue = await polygonService.GetCurrentVixAsync();
            logger.Information($"VIX via dedicated method: {vixValue:F2}");

            // Test VIX through quote method
            logger.Information("Testing VIX through GetCurrentQuoteAsync('I:VIX')...");
            var vixQuote = await polygonService.GetCurrentQuoteAsync("I:VIX");
            if (vixQuote != null && vixQuote.Bid > 0)
            {
                logger.Information($"VIX via quote method: {vixQuote.Bid:F2} (should match dedicated method)");
                
                if (Math.Abs(vixValue - vixQuote.Bid) < 0.1m)
                {
                    logger.Information("✅ VIX values match between methods");
                }
                else
                {
                    logger.Warning($"⚠️ VIX values differ: Dedicated={vixValue:F2}, Quote={vixQuote.Bid:F2}");
                }
            }
            else
            {
                logger.Warning("⚠️ No VIX quote data returned");
            }
        }
        catch (Exception ex)
        {
            logger.Error(ex, "❌ Error testing VIX handling");
        }
    }
}
