using ZeroDateStrat.Models;

namespace ZeroDateStrat.Services;

/// <summary>
/// Service for interacting with OpenAI API
/// </summary>
public interface IOpenAIService
{
    /// <summary>
    /// Send a chat completion request to OpenAI API
    /// </summary>
    /// <param name="prompt">The user prompt/message</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>OpenAI response or null if failed</returns>
    Task<OpenAIResponse?> GetChatCompletionAsync(string prompt, CancellationToken cancellationToken = default);

    /// <summary>
    /// Send a chat completion request with custom system prompt
    /// </summary>
    /// <param name="prompt">The user prompt/message</param>
    /// <param name="systemPrompt">Custom system prompt</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>OpenAI response or null if failed</returns>
    Task<OpenAIResponse?> GetChatCompletionAsync(string prompt, string systemPrompt, CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if the OpenAI service is properly configured and available
    /// </summary>
    /// <returns>True if service is available</returns>
    Task<bool> IsAvailableAsync();
}
