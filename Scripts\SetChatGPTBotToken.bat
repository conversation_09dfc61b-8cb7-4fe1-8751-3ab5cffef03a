@echo off
setlocal enabledelayedexpansion

echo ========================================
echo ChatGPT Discord Bot Token Setup
echo ========================================
echo.

if "%~1"=="" (
    echo Usage: SetChatGPTBotToken.bat "YOUR_CHATGPT_BOT_TOKEN"
    echo.
    echo Example: SetChatGPTBotToken.bat "MTM4MjE0OTM1MzQ3NjQ1NjQ5OA.GTJCg_.AqXUWgahLRKM6SQtuHFXYqWQnMj8wvX22WYzfM"
    echo.
    echo To get a ChatGPT bot token:
    echo 1. Go to https://discord.com/developers/applications
    echo 2. Create a new application called "ChatGPTBot"
    echo 3. Go to the Bot section
    echo 4. Create a bot and copy the token
    echo 5. Invite the bot to your server with "Send Messages" permission
    echo.
    pause
    exit /b 1
)

set "BOT_TOKEN=%~1"

echo Setting ChatGPT Discord bot token...
echo Token: %BOT_TOKEN:~0,20%...
echo.

:: Set environment variable for current session
set "CHATGPT_BOT_TOKEN=%BOT_TOKEN%"

:: Set environment variable permanently for current user
setx CHATGPT_BOT_TOKEN "%BOT_TOKEN%" >nul 2>&1

if %errorlevel% equ 0 (
    echo ✅ ChatGPT bot token set successfully!
    echo.
    echo Environment variable CHATGPT_BOT_TOKEN has been set.
    echo.
    echo Next steps:
    echo 1. Restart your command prompt or IDE to pick up the new environment variable
    echo 2. Run: dotnet run chatgpt-discord-test
    echo 3. If test passes, start the application normally
    echo.
    echo The ChatGPT bot will run alongside your main trading bot.
) else (
    echo ❌ Failed to set environment variable
    echo You may need to run this script as administrator
)

echo.
pause
