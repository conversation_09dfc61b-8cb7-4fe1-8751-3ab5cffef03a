using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Text;
using ZeroDateStrat.Models;

namespace ZeroDateStrat.Services;

/// <summary>
/// Service for interacting with OpenAI API
/// </summary>
public class OpenAIService : IOpenAIService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<OpenAIService> _logger;
    private readonly OpenAIConfiguration _config;
    private readonly string _apiKey;

    public OpenAIService(HttpClient httpClient, IConfiguration configuration, ILogger<OpenAIService> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
        
        // Load configuration
        _config = new OpenAIConfiguration();
        configuration.GetSection("OpenAI").Bind(_config);
        
        // Get API key from environment variable or configuration
        _apiKey = Environment.GetEnvironmentVariable("OPENAI_API_KEY") ?? _config.ApiKey;
        
        // Configure HttpClient
        _httpClient.BaseAddress = new Uri("https://api.openai.com/");
        _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_apiKey}");
        _httpClient.DefaultRequestHeaders.Add("User-Agent", "ZeroDateStrat/1.0");
        _httpClient.Timeout = TimeSpan.FromSeconds(60);
    }

    public async Task<OpenAIResponse?> GetChatCompletionAsync(string prompt, CancellationToken cancellationToken = default)
    {
        return await GetChatCompletionAsync(prompt, _config.SystemPrompt, cancellationToken);
    }

    public async Task<OpenAIResponse?> GetChatCompletionAsync(string prompt, string systemPrompt, CancellationToken cancellationToken = default)
    {
        if (!_config.Enabled)
        {
            _logger.LogWarning("OpenAI service is disabled");
            return null;
        }

        if (string.IsNullOrEmpty(_apiKey))
        {
            _logger.LogError("OpenAI API key not configured");
            return null;
        }

        if (string.IsNullOrWhiteSpace(prompt))
        {
            _logger.LogWarning("Empty prompt provided to OpenAI service");
            return new OpenAIResponse
            {
                Success = false,
                ErrorMessage = "Empty prompt provided"
            };
        }

        try
        {
            var request = new OpenAIRequest
            {
                Model = _config.Model,
                MaxTokens = _config.MaxTokens,
                Temperature = _config.Temperature,
                Messages = new List<OpenAIMessage>
                {
                    new() { Role = "system", Content = systemPrompt },
                    new() { Role = "user", Content = prompt }
                }
            };

            var jsonContent = JsonConvert.SerializeObject(request);
            var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

            _logger.LogInformation($"Sending OpenAI request for prompt: {prompt.Substring(0, Math.Min(100, prompt.Length))}...");

            var response = await _httpClient.PostAsync("v1/chat/completions", content, cancellationToken);
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError($"OpenAI API returned {response.StatusCode}: {responseContent}");
                return new OpenAIResponse
                {
                    Success = false,
                    ErrorMessage = $"API returned {response.StatusCode}"
                };
            }

            var apiResponse = JsonConvert.DeserializeObject<OpenAIApiResponse>(responseContent);

            if (apiResponse?.Error != null)
            {
                _logger.LogError($"OpenAI API error: {apiResponse.Error.Message}");
                return new OpenAIResponse
                {
                    Success = false,
                    ErrorMessage = apiResponse.Error.Message
                };
            }

            if (apiResponse?.Choices?.FirstOrDefault()?.Message?.Content == null)
            {
                _logger.LogWarning("No content received from OpenAI API");
                return new OpenAIResponse
                {
                    Success = false,
                    ErrorMessage = "No content received from API"
                };
            }

            var result = new OpenAIResponse
            {
                Content = apiResponse.Choices.First().Message.Content,
                Model = apiResponse.Model,
                TokensUsed = apiResponse.Usage?.TotalTokens ?? 0,
                Success = true
            };

            _logger.LogInformation($"OpenAI response received: {result.TokensUsed} tokens used");
            return result;
        }
        catch (OperationCanceledException)
        {
            _logger.LogWarning("OpenAI request was cancelled");
            return new OpenAIResponse
            {
                Success = false,
                ErrorMessage = "Request was cancelled"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calling OpenAI API");
            return new OpenAIResponse
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }

    public async Task<bool> IsAvailableAsync()
    {
        if (!_config.Enabled || string.IsNullOrEmpty(_apiKey))
        {
            return false;
        }

        try
        {
            // Simple test request to check if API is available
            var testResponse = await GetChatCompletionAsync("Hello", "You are a helpful assistant.", CancellationToken.None);
            return testResponse?.Success == true;
        }
        catch
        {
            return false;
        }
    }
}
