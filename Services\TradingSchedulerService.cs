using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using ZeroDateStrat.Models;

namespace ZeroDateStrat.Services;

public class TradingSchedulerService : BackgroundService
{
    private readonly ITradingNotificationService _notificationService;
    private readonly IMarketRegimeAnalyzer _marketRegimeAnalyzer;
    private readonly ILogger<TradingSchedulerService> _logger;
    private readonly IConfiguration _configuration;

    private DateTime _lastMorningReportDate = DateTime.MinValue;
    private DateTime _lastEndOfDayReportDate = DateTime.MinValue;
    private DateTime _lastMarketStatusUpdate = DateTime.MinValue;

    public TradingSchedulerService(
        ITradingNotificationService notificationService,
        IMarketRegimeAnalyzer marketRegimeAnalyzer,
        ILogger<TradingSchedulerService> logger,
        IConfiguration configuration)
    {
        _notificationService = notificationService;
        _marketRegimeAnalyzer = marketRegimeAnalyzer;
        _logger = logger;
        _configuration = configuration;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Trading Scheduler Service started");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                var now = DateTime.Now;
                
                await CheckMorningReportSchedule(now);
                await CheckEndOfDayReportSchedule(now);
                await CheckMarketStatusUpdates(now);

                // Check every minute
                await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in trading scheduler service");
                await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
            }
        }

        _logger.LogInformation("Trading Scheduler Service stopped");
    }

    private async Task CheckMorningReportSchedule(DateTime now)
    {
        try
        {
            // Send morning report at 8:30 AM ET on weekdays
            var morningReportTime = TimeSpan.FromHours(8.5); // 8:30 AM
            var morningReportWindow = TimeSpan.FromMinutes(5); // 5-minute window

            if (IsWeekday(now) &&
                now.TimeOfDay >= morningReportTime &&
                now.TimeOfDay <= morningReportTime.Add(morningReportWindow) &&
                _lastMorningReportDate.Date != now.Date)
            {
                _logger.LogInformation("Sending scheduled morning portfolio report");
                await _notificationService.SendMorningReportAsync();
                _lastMorningReportDate = now;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending morning report");
        }
    }

    private async Task CheckEndOfDayReportSchedule(DateTime now)
    {
        try
        {
            // Send end-of-day report at 4:15 PM ET on weekdays
            var endOfDayReportTime = TimeSpan.FromHours(16.25); // 4:15 PM
            var endOfDayReportWindow = TimeSpan.FromMinutes(10); // 10-minute window

            if (IsWeekday(now) &&
                now.TimeOfDay >= endOfDayReportTime &&
                now.TimeOfDay <= endOfDayReportTime.Add(endOfDayReportWindow) &&
                _lastEndOfDayReportDate.Date != now.Date)
            {
                _logger.LogInformation("Sending scheduled end-of-day trading report");
                await _notificationService.SendEndOfDayReportAsync();
                _lastEndOfDayReportDate = now;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending end-of-day report");
        }
    }

    private async Task CheckMarketStatusUpdates(DateTime now)
    {
        try
        {
            // Send market status updates every 30 minutes during trading hours
            var updateInterval = TimeSpan.FromMinutes(30);
            
            if (IsWeekday(now) &&
                IsTradingHours(now) &&
                (now - _lastMarketStatusUpdate) >= updateInterval)
            {
                var regime = await _marketRegimeAnalyzer.GetCurrentRegimeAsync();
                var marketStatus = GetMarketStatus(now);

                await _notificationService.SendMarketStatusUpdateAsync(
                    marketStatus, 
                    regime.Vix, 
                    regime.VolatilityRegime.ToString());

                _lastMarketStatusUpdate = now;
                _logger.LogDebug($"Sent market status update: {marketStatus}, VIX: {regime.Vix:F2}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending market status update");
        }
    }

    private bool IsWeekday(DateTime date)
    {
        return date.DayOfWeek != DayOfWeek.Saturday && date.DayOfWeek != DayOfWeek.Sunday;
    }

    private bool IsTradingHours(DateTime now)
    {
        var time = now.TimeOfDay;
        var marketOpen = TimeSpan.FromHours(9.5); // 9:30 AM ET
        var marketClose = TimeSpan.FromHours(16); // 4:00 PM ET

        return time >= marketOpen && time <= marketClose;
    }

    private string GetMarketStatus(DateTime now)
    {
        var time = now.TimeOfDay;
        var marketOpen = TimeSpan.FromHours(9.5); // 9:30 AM ET
        var marketClose = TimeSpan.FromHours(16); // 4:00 PM ET
        var preMarketStart = TimeSpan.FromHours(4); // 4:00 AM ET
        var afterHoursEnd = TimeSpan.FromHours(20); // 8:00 PM ET

        if (!IsWeekday(now))
        {
            return "Closed (Weekend)";
        }

        if (time >= marketOpen && time <= marketClose)
        {
            return "Open";
        }
        else if (time >= preMarketStart && time < marketOpen)
        {
            return "Pre-Market";
        }
        else if (time > marketClose && time <= afterHoursEnd)
        {
            return "After-Hours";
        }
        else
        {
            return "Closed";
        }
    }
}
