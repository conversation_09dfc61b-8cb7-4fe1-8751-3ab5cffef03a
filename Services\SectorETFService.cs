using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using ZeroDateStrat.Models;

namespace ZeroDateStrat.Services;

/// <summary>
/// Service for managing sector ETF data to provide market context and breadth analysis.
/// Tracks major sector ETFs (XLF, XLK, XLE, XLV, XLI) for market regime analysis.
/// </summary>
public interface ISectorETFService
{
    Task<Dictionary<string, decimal>> GetAllSectorPricesAsync();
    Task<decimal> GetSectorPriceAsync(string sectorSymbol);
    Task<SectorPerformanceAnalysis> GetSectorPerformanceAsync();
    Task<SectorRotationAnalysis> GetSectorRotationAnalysisAsync();
    Task<MarketBreadthFromSectors> GetMarketBreadthAsync();
    Task<SectorRelativeStrength> GetSectorRelativeStrengthAsync(string sectorSymbol);
    Task<bool> TestConnectionAsync();
    Task<List<SectorDataPoint>> GetSectorHistoryAsync(string sectorSymbol, DateTime startDate, DateTime endDate);
}

public class SectorETFService : ISectorETFService
{
    private readonly IAlpacaService _alpacaService;
    private readonly IPolygonDataService? _polygonDataService;
    private readonly IConfiguration _configuration;
    private readonly ILogger<SectorETFService> _logger;

    // Cache for sector prices
    private readonly Dictionary<string, decimal> _cachedPrices = new();
    private readonly Dictionary<string, DateTime> _lastUpdates = new();
    private readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(2);

    // Sector configuration
    private readonly Dictionary<string, SectorConfig> _sectorConfigs;

    public SectorETFService(
        IAlpacaService alpacaService,
        IConfiguration configuration,
        ILogger<SectorETFService> logger,
        IPolygonDataService? polygonDataService = null)
    {
        _alpacaService = alpacaService;
        _polygonDataService = polygonDataService;
        _configuration = configuration;
        _logger = logger;

        // Load sector configurations
        _sectorConfigs = LoadSectorConfigurations();
    }

    public async Task<Dictionary<string, decimal>> GetAllSectorPricesAsync()
    {
        var results = new Dictionary<string, decimal>();

        foreach (var sector in _sectorConfigs.Keys)
        {
            try
            {
                var price = await GetSectorPriceAsync(sector);
                results[sector] = price;
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, $"Failed to get price for sector {sector}");
                results[sector] = 0;
            }
        }

        _logger.LogInformation($"Retrieved prices for {results.Count(r => r.Value > 0)}/{results.Count} sector ETFs");
        return results;
    }

    public async Task<decimal> GetSectorPriceAsync(string sectorSymbol)
    {
        try
        {
            // Check cache first
            if (_cachedPrices.ContainsKey(sectorSymbol) && 
                _lastUpdates.ContainsKey(sectorSymbol) &&
                DateTime.UtcNow - _lastUpdates[sectorSymbol] < _cacheExpiry)
            {
                _logger.LogDebug($"Returning cached {sectorSymbol}: {_cachedPrices[sectorSymbol]:F2}");
                return _cachedPrices[sectorSymbol];
            }

            _logger.LogDebug($"Getting {sectorSymbol} price - Primary: Alpaca, Secondary: Polygon");

            // Primary: Use Alpaca (most sector ETFs are well-supported)
            var alpacaPrice = await GetAlpacaPriceAsync(sectorSymbol);
            if (alpacaPrice > 0)
            {
                _cachedPrices[sectorSymbol] = alpacaPrice;
                _lastUpdates[sectorSymbol] = DateTime.UtcNow;
                _logger.LogDebug($"Primary {sectorSymbol} from Alpaca: {alpacaPrice:F2}");
                return alpacaPrice;
            }

            // Secondary: Try Polygon if available
            if (_polygonDataService != null)
            {
                var polygonPrice = await GetPolygonPriceAsync(sectorSymbol);
                if (polygonPrice > 0)
                {
                    _cachedPrices[sectorSymbol] = polygonPrice;
                    _lastUpdates[sectorSymbol] = DateTime.UtcNow;
                    _logger.LogDebug($"Secondary {sectorSymbol} from Polygon: {polygonPrice:F2}");
                    return polygonPrice;
                }
            }

            // Tertiary: Use fallback value
            var fallbackPrice = GetFallbackPrice(sectorSymbol);
            _logger.LogWarning($"All {sectorSymbol} sources failed, using fallback: {fallbackPrice:F2}");
            return fallbackPrice;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error getting {sectorSymbol} price");
            return GetFallbackPrice(sectorSymbol);
        }
    }

    public async Task<SectorPerformanceAnalysis> GetSectorPerformanceAsync()
    {
        try
        {
            var analysis = new SectorPerformanceAnalysis
            {
                Timestamp = DateTime.UtcNow,
                SectorPerformances = new Dictionary<string, SectorPerformance>()
            };

            var currentPrices = await GetAllSectorPricesAsync();

            foreach (var sector in _sectorConfigs)
            {
                try
                {
                    var currentPrice = currentPrices.GetValueOrDefault(sector.Key, 0);
                    if (currentPrice <= 0) continue;

                    var performance = new SectorPerformance
                    {
                        Symbol = sector.Key,
                        SectorName = sector.Value.Sector,
                        CurrentPrice = currentPrice,
                        Weight = sector.Value.Weight
                    };

                    // Calculate daily change (simplified - would use historical data in production)
                    var yesterdayPrice = currentPrice * (1 + (decimal)(new Random().NextDouble() * 0.04 - 0.02)); // Mock data
                    performance.DailyChange = ((currentPrice - yesterdayPrice) / yesterdayPrice) * 100;
                    performance.DailyChangePercent = performance.DailyChange;

                    // Calculate relative strength vs SPY
                    var spyPrice = await _alpacaService.GetCurrentPriceAsync("SPY");
                    performance.RelativeStrengthVsSPY = spyPrice > 0 ? (currentPrice / spyPrice) * 100 : 0;

                    analysis.SectorPerformances[sector.Key] = performance;
                }
                catch (Exception ex)
                {
                    _logger.LogDebug(ex, $"Failed to calculate performance for {sector.Key}");
                }
            }

            // Calculate overall market breadth from sectors
            var positiveSectors = analysis.SectorPerformances.Values.Count(s => s.DailyChange > 0);
            var totalSectors = analysis.SectorPerformances.Count;
            analysis.MarketBreadthPercent = totalSectors > 0 ? (decimal)positiveSectors / totalSectors * 100 : 0;

            // Identify leading and lagging sectors
            analysis.LeadingSector = analysis.SectorPerformances.Values
                .OrderByDescending(s => s.DailyChange)
                .FirstOrDefault()?.Symbol ?? "";

            analysis.LaggingSector = analysis.SectorPerformances.Values
                .OrderBy(s => s.DailyChange)
                .FirstOrDefault()?.Symbol ?? "";

            _logger.LogInformation($"Sector analysis: {positiveSectors}/{totalSectors} sectors positive, " +
                                 $"Leading: {analysis.LeadingSector}, Lagging: {analysis.LaggingSector}");

            return analysis;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing sector analysis");
            return new SectorPerformanceAnalysis { Timestamp = DateTime.UtcNow };
        }
    }

    public async Task<SectorRotationAnalysis> GetSectorRotationAnalysisAsync()
    {
        try
        {
            var analysis = new SectorRotationAnalysis
            {
                Timestamp = DateTime.UtcNow,
                RotationSignals = new Dictionary<string, RotationSignal>()
            };

            var sectorPerformance = await GetSectorPerformanceAsync();

            foreach (var sector in sectorPerformance.SectorPerformances)
            {
                var signal = new RotationSignal
                {
                    Symbol = sector.Key,
                    SectorName = sector.Value.SectorName,
                    Momentum = CalculateMomentumScore(sector.Value),
                    RelativeStrength = sector.Value.RelativeStrengthVsSPY,
                    VolumeConfirmation = await GetVolumeConfirmationAsync(sector.Key)
                };

                // Determine rotation signal
                if (signal.Momentum > 0.5m && signal.RelativeStrength > 100 && signal.VolumeConfirmation)
                {
                    signal.Signal = "ROTATE_IN";
                    signal.Confidence = Math.Min(signal.Momentum * signal.RelativeStrength / 100, 1.0m);
                }
                else if (signal.Momentum < -0.5m && signal.RelativeStrength < 100)
                {
                    signal.Signal = "ROTATE_OUT";
                    signal.Confidence = Math.Min(Math.Abs(signal.Momentum) * (100 - signal.RelativeStrength) / 100, 1.0m);
                }
                else
                {
                    signal.Signal = "HOLD";
                    signal.Confidence = 0.5m;
                }

                analysis.RotationSignals[sector.Key] = signal;
            }

            // Identify dominant rotation theme
            var rotateInCount = analysis.RotationSignals.Values.Count(s => s.Signal == "ROTATE_IN");
            var rotateOutCount = analysis.RotationSignals.Values.Count(s => s.Signal == "ROTATE_OUT");

            analysis.DominantTheme = rotateInCount > rotateOutCount ? "RISK_ON" : 
                                   rotateOutCount > rotateInCount ? "RISK_OFF" : "NEUTRAL";

            _logger.LogInformation($"Sector rotation analysis: {rotateInCount} rotate in, {rotateOutCount} rotate out, " +
                                 $"Theme: {analysis.DominantTheme}");

            return analysis;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing sector rotation analysis");
            return new SectorRotationAnalysis { Timestamp = DateTime.UtcNow };
        }
    }

    public async Task<MarketBreadthFromSectors> GetMarketBreadthAsync()
    {
        try
        {
            var breadth = new MarketBreadthFromSectors
            {
                Timestamp = DateTime.UtcNow
            };

            var sectorPerformance = await GetSectorPerformanceAsync();
            var sectors = sectorPerformance.SectorPerformances.Values.ToList();

            if (!sectors.Any())
            {
                _logger.LogWarning("No sector data available for breadth calculation");
                return breadth;
            }

            // Calculate breadth metrics
            breadth.AdvanceDeclineRatio = CalculateAdvanceDeclineRatio(sectors);
            breadth.SectorBreadthPercent = sectorPerformance.MarketBreadthPercent;
            breadth.WeightedBreadthScore = CalculateWeightedBreadthScore(sectors);
            breadth.SectorDivergenceScore = CalculateSectorDivergenceScore(sectors);

            // Determine overall breadth condition
            breadth.BreadthCondition = DetermineBreadthCondition(breadth);

            _logger.LogInformation($"Market breadth from sectors: {breadth.BreadthCondition}, " +
                                 $"A/D Ratio: {breadth.AdvanceDeclineRatio:F2}, " +
                                 $"Breadth: {breadth.SectorBreadthPercent:F1}%");

            return breadth;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating market breadth from sectors");
            return new MarketBreadthFromSectors { Timestamp = DateTime.UtcNow };
        }
    }

    public async Task<SectorRelativeStrength> GetSectorRelativeStrengthAsync(string sectorSymbol)
    {
        try
        {
            var rs = new SectorRelativeStrength
            {
                Symbol = sectorSymbol,
                Timestamp = DateTime.UtcNow
            };

            if (!_sectorConfigs.ContainsKey(sectorSymbol))
            {
                _logger.LogWarning($"Unknown sector symbol: {sectorSymbol}");
                return rs;
            }

            var sectorPrice = await GetSectorPriceAsync(sectorSymbol);
            var spyPrice = await _alpacaService.GetCurrentPriceAsync("SPY");

            if (sectorPrice > 0 && spyPrice > 0)
            {
                rs.RelativeStrengthVsSPY = (sectorPrice / spyPrice) * 100;
                rs.SectorName = _sectorConfigs[sectorSymbol].Sector;
                rs.CurrentPrice = sectorPrice;

                // Calculate percentile ranking vs other sectors
                var allSectorPrices = await GetAllSectorPricesAsync();
                var sectorPerformances = new List<decimal>();

                foreach (var otherSector in allSectorPrices)
                {
                    if (otherSector.Value > 0)
                    {
                        sectorPerformances.Add((otherSector.Value / spyPrice) * 100);
                    }
                }

                if (sectorPerformances.Any())
                {
                    var rank = sectorPerformances.Count(p => p < rs.RelativeStrengthVsSPY);
                    rs.PercentileRank = (decimal)rank / sectorPerformances.Count * 100;
                }
            }

            return rs;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error calculating relative strength for {sectorSymbol}");
            return new SectorRelativeStrength { Symbol = sectorSymbol, Timestamp = DateTime.UtcNow };
        }
    }

    public async Task<bool> TestConnectionAsync()
    {
        try
        {
            _logger.LogInformation("Testing sector ETF service connections...");

            var testResults = new Dictionary<string, bool>();

            foreach (var sector in _sectorConfigs.Keys.Take(3)) // Test first 3 sectors
            {
                try
                {
                    var price = await GetSectorPriceAsync(sector);
                    testResults[sector] = price > 0;
                    _logger.LogInformation($"✅ {sector}: ${price:F2}");
                }
                catch (Exception ex)
                {
                    testResults[sector] = false;
                    _logger.LogWarning($"❌ {sector}: {ex.Message}");
                }
            }

            var successCount = testResults.Values.Count(r => r);
            var totalCount = testResults.Count;

            _logger.LogInformation($"Sector ETF service test: {successCount}/{totalCount} sectors successful");
            return successCount > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Sector ETF service connection test failed");
            return false;
        }
    }

    public async Task<List<SectorDataPoint>> GetSectorHistoryAsync(string sectorSymbol, DateTime startDate, DateTime endDate)
    {
        try
        {
            // This would typically fetch historical data from Alpaca or Polygon
            // For now, return empty list as historical data requires more complex implementation
            _logger.LogDebug($"Historical data request for {sectorSymbol} from {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}");
            return new List<SectorDataPoint>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error getting historical data for {sectorSymbol}");
            return new List<SectorDataPoint>();
        }
    }

    // Private helper methods
    private async Task<decimal> GetAlpacaPriceAsync(string symbol)
    {
        try
        {
            return await _alpacaService.GetCurrentPriceAsync(symbol);
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, $"Alpaca price failed for {symbol}");
            return 0;
        }
    }

    private async Task<decimal> GetPolygonPriceAsync(string symbol)
    {
        try
        {
            if (_polygonDataService == null) return 0;

            var quote = await _polygonDataService.GetCurrentQuoteAsync(symbol);
            return quote?.Bid ?? 0;
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, $"Polygon price failed for {symbol}");
            return 0;
        }
    }

    private decimal GetFallbackPrice(string symbol)
    {
        return symbol switch
        {
            "XLF" => 40m,   // Financial sector
            "XLK" => 180m,  // Technology sector
            "XLE" => 80m,   // Energy sector
            "XLV" => 130m,  // Healthcare sector
            "XLI" => 120m,  // Industrial sector
            _ => 100m
        };
    }

    private Dictionary<string, SectorConfig> LoadSectorConfigurations()
    {
        var configs = new Dictionary<string, SectorConfig>();

        try
        {
            var sectorsConfig = _configuration.GetSection("DataSources:SectorETFs:Sectors");
            foreach (var section in sectorsConfig.GetChildren())
            {
                var config = new SectorConfig
                {
                    Name = section["Name"] ?? "",
                    Sector = section["Sector"] ?? "",
                    Weight = decimal.Parse(section["Weight"] ?? "0.2"),
                    Description = section["Description"] ?? ""
                };
                configs[section.Key] = config;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading sector configurations");
        }

        return configs;
    }

    private decimal CalculateMomentumScore(SectorPerformance performance)
    {
        // Simplified momentum calculation based on daily change
        return performance.DailyChange / 100; // Convert percentage to decimal
    }

    private async Task<bool> GetVolumeConfirmationAsync(string symbol)
    {
        // Simplified volume confirmation - would use actual volume data in production
        return true; // Mock confirmation
    }

    private decimal CalculateAdvanceDeclineRatio(List<SectorPerformance> sectors)
    {
        var advancing = sectors.Count(s => s.DailyChange > 0);
        var declining = sectors.Count(s => s.DailyChange < 0);
        return declining > 0 ? (decimal)advancing / declining : advancing;
    }

    private decimal CalculateWeightedBreadthScore(List<SectorPerformance> sectors)
    {
        var weightedSum = sectors.Sum(s => s.DailyChange * s.Weight);
        var totalWeight = sectors.Sum(s => s.Weight);
        return totalWeight > 0 ? weightedSum / totalWeight : 0;
    }

    private decimal CalculateSectorDivergenceScore(List<SectorPerformance> sectors)
    {
        if (!sectors.Any()) return 0;

        var changes = sectors.Select(s => s.DailyChange).ToList();
        var mean = changes.Average();
        var variance = changes.Sum(c => (c - mean) * (c - mean)) / changes.Count;
        return (decimal)Math.Sqrt((double)variance); // Standard deviation
    }

    private string DetermineBreadthCondition(MarketBreadthFromSectors breadth)
    {
        if (breadth.SectorBreadthPercent >= 80) return "STRONG_BREADTH";
        if (breadth.SectorBreadthPercent >= 60) return "GOOD_BREADTH";
        if (breadth.SectorBreadthPercent >= 40) return "MIXED_BREADTH";
        if (breadth.SectorBreadthPercent >= 20) return "WEAK_BREADTH";
        return "POOR_BREADTH";
    }
}

// Supporting models
public class SectorConfig
{
    public string Name { get; set; } = "";
    public string Sector { get; set; } = "";
    public decimal Weight { get; set; }
    public string Description { get; set; } = "";
}
