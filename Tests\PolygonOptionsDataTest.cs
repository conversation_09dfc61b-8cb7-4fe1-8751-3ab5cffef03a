using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using ZeroDateStrat.Models;
using ZeroDateStrat.Services;
using ILogger = Serilog.ILogger;

namespace ZeroDateStrat.Tests;

public static class PolygonOptionsDataTest
{
    public static async Task RunPolygonOptionsDataTest()
    {
        // Configure Serilog for testing
        Log.Logger = new LoggerConfiguration()
            .WriteTo.Console()
            .WriteTo.File($"logs/polygon-options-test-{DateTime.Now:yyyyMMdd}.txt")
            .CreateLogger();

        var logger = Log.Logger;

        try
        {
            logger.Information("🔍 Starting Polygon.io Options Data Test");
            logger.Information("Testing VIX and SPX options data access with updated API permissions");

            // Build host with services
            var host = Host.CreateDefaultBuilder()
                .UseSerilog()
                .ConfigureAppConfiguration((context, config) =>
                {
                    config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
                    config.AddEnvironmentVariables();
                })
                .ConfigureServices((context, services) =>
                {
                    services.AddSingleton<IPolygonDataService, PolygonDataService>();
                    services.AddHttpClient<IPolygonDataService, PolygonDataService>();
                    services.AddLogging(builder => builder.AddSerilog());
                })
                .Build();

            // Get services
            var polygonService = host.Services.GetRequiredService<IPolygonDataService>();
            var configuration = host.Services.GetRequiredService<IConfiguration>();

            // Display API key info (masked)
            var apiKey = configuration["Polygon:ApiKey"];
            var maskedKey = apiKey?.Length > 8 ? $"{apiKey[..4]}...{apiKey[^4..]}" : "Not configured";
            logger.Information($"Using Polygon API Key: {maskedKey}");

            // Test 1: VIX Data Access
            logger.Information("\n📊 Test 1: VIX Data Access");
            await TestVixDataAccess(polygonService, logger);

            // Test 2: SPX Quote Data
            logger.Information("\n📈 Test 2: SPX Quote Data");
            await TestSpxQuoteData(polygonService, logger);

            // Test 3: Connection Test
            logger.Information("\n🔗 Test 3: Connection Test");
            await TestConnectionStatus(polygonService, logger);

            // Test 4: API Response Analysis
            logger.Information("\n📊 Test 4: API Response Analysis");
            await TestApiResponseAnalysis(polygonService, logger);

            // Test 5: VIX Historical Data
            logger.Information("\n⏰ Test 5: VIX Historical Data");
            await TestVixHistoricalData(polygonService, logger);

            // Test 6: Data Quality Assessment
            logger.Information("\n📋 Test 6: Data Quality Assessment");
            await TestDataQuality(polygonService, logger);

            logger.Information("\n✅ Polygon.io Options Data Test Completed!");
            logger.Information("📊 Summary of API Access Capabilities:");
            logger.Information("Check the detailed logs above for specific endpoint results");

        }
        catch (Exception ex)
        {
            logger.Error(ex, "❌ Error during Polygon.io options data test");
        }
        finally
        {
            Log.CloseAndFlush();
        }
    }

    private static async Task TestVixDataAccess(IPolygonDataService polygonService, ILogger logger)
    {
        try
        {
            logger.Information("Testing VIX data access...");

            // Test VIX through PolygonDataService directly
            var vixValue = await polygonService.GetCurrentVixAsync();
            logger.Information($"VIX Value: {vixValue:F2}");

            // Test direct VIX quote from Polygon
            logger.Information("Testing direct VIX index data from Polygon...");
            logger.Information("Note: VIX indices don't have bid/ask quotes - using last trade price");

            // Try different VIX symbols
            var vixSymbols = new[] { "I:VIX", "VIX", "I:VIX9D", "I:VIX3M" };

            foreach (var symbol in vixSymbols)
            {
                try
                {
                    logger.Information($"Trying VIX index symbol: {symbol}");
                    // This will test the HTTP request and log the response
                    var quote = await polygonService.GetCurrentQuoteAsync(symbol);
                    if (quote != null && quote.Bid > 0)
                    {
                        // For indices, bid and ask will be the same (last trade price)
                        if (quote.Bid == quote.Ask)
                        {
                            logger.Information($"✅ {symbol} - Index Price: {quote.Bid:F2} (last trade)");
                        }
                        else
                        {
                            logger.Information($"✅ {symbol} - Bid: {quote.Bid:F2}, Ask: {quote.Ask:F2}, Mid: {quote.MidPrice:F2}");
                        }
                    }
                    else
                    {
                        logger.Warning($"⚠️ {symbol} - No index data returned");
                    }
                }
                catch (Exception ex)
                {
                    logger.Warning($"❌ {symbol} - Error: {ex.Message}");
                }
            }
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error testing VIX data access");
        }
    }

    private static async Task TestSpxQuoteData(IPolygonDataService polygonService, ILogger logger)
    {
        try
        {
            logger.Information("Testing SPX data access...");
            logger.Information("Note: SPX (I:SPX) is an index - no bid/ask quotes, SPY is an ETF with real quotes");

            var spxSymbols = new[] { "I:SPX", "SPX", "I:SPY", "SPY" };

            foreach (var symbol in spxSymbols)
            {
                try
                {
                    var isIndex = symbol.StartsWith("I:") || symbol == "SPX";
                    logger.Information($"Trying {(isIndex ? "index" : "ETF")} symbol: {symbol}");

                    var quote = await polygonService.GetCurrentQuoteAsync(symbol);
                    if (quote != null && quote.Bid > 0)
                    {
                        if (isIndex && quote.Bid == quote.Ask)
                        {
                            logger.Information($"✅ {symbol} - Index Price: {quote.Bid:F2} (last trade)");
                        }
                        else
                        {
                            logger.Information($"✅ {symbol} - Bid: {quote.Bid:F2}, Ask: {quote.Ask:F2}, Mid: {quote.MidPrice:F2}, Spread: {quote.SpreadPercentage:F2}%");
                        }
                    }
                    else
                    {
                        logger.Warning($"⚠️ {symbol} - No data returned");
                    }
                }
                catch (Exception ex)
                {
                    logger.Warning($"❌ {symbol} - Error: {ex.Message}");
                }
            }
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error testing SPX data");
        }
    }

    private static async Task TestConnectionStatus(IPolygonDataService polygonService, ILogger logger)
    {
        try
        {
            logger.Information("Testing Polygon.io connection status...");

            var connectionTest = await polygonService.TestConnectionAsync();

            if (connectionTest)
            {
                logger.Information("✅ Polygon.io connection successful");
            }
            else
            {
                logger.Warning("⚠️ Polygon.io connection failed");
            }

            logger.Information($"Connection test result: {connectionTest}");
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error testing connection status");
        }
    }

    private static async Task TestApiResponseAnalysis(IPolygonDataService polygonService, ILogger logger)
    {
        try
        {
            logger.Information("Testing API response analysis...");

            // Test multiple symbols to see response patterns
            var testSymbols = new[] { "SPY", "QQQ", "IWM", "AAPL" };
            var successCount = 0;
            var totalTests = testSymbols.Length;

            foreach (var symbol in testSymbols)
            {
                try
                {
                    var quote = await polygonService.GetCurrentQuoteAsync(symbol);
                    if (quote != null && quote.Bid > 0)
                    {
                        successCount++;
                        logger.Information($"✅ {symbol} - Success (Spread: {quote.SpreadPercentage:F2}%)");
                    }
                    else
                    {
                        logger.Warning($"⚠️ {symbol} - No valid data");
                    }
                }
                catch (Exception ex)
                {
                    logger.Warning($"❌ {symbol} - Error: {ex.Message}");
                }
            }

            var successRate = (decimal)successCount / totalTests * 100;
            logger.Information($"API Success Rate: {successRate:F1}% ({successCount}/{totalTests})");
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error testing API response analysis");
        }
    }

    private static async Task TestVixHistoricalData(IPolygonDataService polygonService, ILogger logger)
    {
        try
        {
            logger.Information("Testing VIX historical data access...");

            var endDate = DateTime.Today;
            var startDate = endDate.AddDays(-5); // Last 5 days

            logger.Information($"Requesting VIX history from {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}");

            var vixHistory = await polygonService.GetVixHistoryAsync(startDate, endDate);

            if (vixHistory.Any())
            {
                logger.Information($"✅ VIX Historical Data Retrieved: {vixHistory.Count} data points");

                var latest = vixHistory.OrderByDescending(v => v.Date).FirstOrDefault();
                if (latest != null)
                {
                    logger.Information($"   Latest: {latest.Date:yyyy-MM-dd} - Value: {latest.Value:F2}, High: {latest.High:F2}, Low: {latest.Low:F2}");
                }

                var avgVix = vixHistory.Average(v => v.Value);
                logger.Information($"   Average VIX over period: {avgVix:F2}");
            }
            else
            {
                logger.Warning("⚠️ No VIX historical data returned");
            }
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error testing VIX historical data");
        }
    }

    private static async Task TestDataQuality(IPolygonDataService polygonService, ILogger logger)
    {
        try
        {
            logger.Information("Testing data quality assessment...");

            // Test VIX data quality
            var currentVix = await polygonService.GetCurrentVixAsync();
            logger.Information($"Current VIX: {currentVix:F2}");

            // Assess VIX reasonableness (typically 10-80 range)
            if (currentVix >= 10 && currentVix <= 80)
            {
                logger.Information("✅ VIX value appears reasonable");
            }
            else
            {
                logger.Warning($"⚠️ VIX value seems unusual: {currentVix:F2}");
            }

            // Test VIX change calculation
            var vixChange = await polygonService.GetVixChangeAsync(TimeSpan.FromDays(1));
            logger.Information($"VIX 1-day change: {vixChange:F2}");

            // Test data freshness
            var spyQuote = await polygonService.GetCurrentQuoteAsync("SPY");
            if (spyQuote != null && spyQuote.Bid > 0)
            {
                var dataAge = DateTime.UtcNow - spyQuote.Timestamp;
                logger.Information($"SPY data age: {dataAge.TotalMinutes:F1} minutes");

                if (dataAge.TotalMinutes < 15)
                {
                    logger.Information("✅ Data appears fresh");
                }
                else
                {
                    logger.Warning($"⚠️ Data may be stale ({dataAge.TotalMinutes:F0} min old)");
                }
            }
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error testing data quality");
        }
    }


}
