using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Serilog;
using ILogger = Serilog.ILogger;

namespace ZeroDateStrat.Tests;

public static class PolygonEndpointTest
{
    public static async Task RunPolygonEndpointTest()
    {
        // Configure Serilog for testing
        Log.Logger = new LoggerConfiguration()
            .WriteTo.Console()
            .WriteTo.File($"logs/polygon-endpoint-test-{DateTime.Now:yyyyMMdd}.txt")
            .CreateLogger();

        var logger = Log.Logger;

        try
        {
            logger.Information("🔍 Starting Polygon.io Endpoint Access Test");
            logger.Information("Testing various endpoints to determine API access level");

            // Build host with minimal services
            var host = Host.CreateDefaultBuilder()
                .UseSerilog()
                .ConfigureAppConfiguration((context, config) =>
                {
                    config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
                    config.AddEnvironmentVariables();
                })
                .ConfigureServices((context, services) =>
                {
                    services.AddHttpClient();
                    services.AddLogging(builder => builder.AddSerilog());
                })
                .Build();

            // Get services
            var httpClientFactory = host.Services.GetRequiredService<IHttpClientFactory>();
            var configuration = host.Services.GetRequiredService<IConfiguration>();
            var httpClient = httpClientFactory.CreateClient();

            var apiKey = configuration["Polygon:ApiKey"];
            if (string.IsNullOrEmpty(apiKey))
            {
                logger.Error("❌ Polygon API key not configured");
                return;
            }

            var maskedKey = apiKey.Length > 8 ? $"{apiKey[..4]}...{apiKey[^4..]}" : "Not configured";
            logger.Information($"Using Polygon API Key: {maskedKey}");

            // Test various endpoints
            await TestEndpoint(httpClient, apiKey, "Market Status", "/v1/marketstatus/now", logger);
            await TestEndpoint(httpClient, apiKey, "SPY Quote", "/v2/last/nbbo/SPY", logger);
            await TestEndpoint(httpClient, apiKey, "SPY Trade", "/v2/last/trade/SPY", logger);
            await TestEndpoint(httpClient, apiKey, "AAPL Quote", "/v2/last/nbbo/AAPL", logger);
            await TestEndpoint(httpClient, apiKey, "VIX Trade", "/v2/last/trade/I:VIX", logger);
            await TestEndpoint(httpClient, apiKey, "SPX Quote", "/v2/last/nbbo/I:SPX", logger);
            await TestEndpoint(httpClient, apiKey, "QQQ Quote", "/v2/last/nbbo/QQQ", logger);
            
            // Test historical data
            var yesterday = DateTime.Today.AddDays(-1).ToString("yyyy-MM-dd");
            var today = DateTime.Today.ToString("yyyy-MM-dd");
            await TestEndpoint(httpClient, apiKey, "SPY Historical", $"/v2/aggs/ticker/SPY/range/1/day/{yesterday}/{today}", logger);
            await TestEndpoint(httpClient, apiKey, "VIX Historical", $"/v2/aggs/ticker/I:VIX/range/1/day/{yesterday}/{today}", logger);
            
            // Test options-related endpoints
            await TestEndpoint(httpClient, apiKey, "Options Contracts", "/v3/reference/options/contracts", logger);
            await TestEndpoint(httpClient, apiKey, "SPY Options", "/v3/reference/options/contracts?underlying_ticker=SPY", logger);
            
            // Test a specific options contract (if it exists)
            var optionSymbol = $"O:SPY{DateTime.Today:yyMMdd}C00580000";
            await TestEndpoint(httpClient, apiKey, $"Option Contract {optionSymbol}", $"/v2/last/trade/{optionSymbol}", logger);

            logger.Information("\n✅ Polygon.io Endpoint Access Test Completed!");
            logger.Information("📊 Check the results above to see which endpoints are accessible");

        }
        catch (Exception ex)
        {
            logger.Error(ex, "❌ Error during Polygon.io endpoint test");
        }
        finally
        {
            Log.CloseAndFlush();
        }
    }

    private static async Task TestEndpoint(HttpClient httpClient, string apiKey, string name, string endpoint, ILogger logger)
    {
        try
        {
            var url = $"https://api.polygon.io{endpoint}";
            
            // Add API key parameter
            var separator = endpoint.Contains('?') ? "&" : "?";
            url += $"{separator}apikey={apiKey}";

            logger.Information($"\n🔍 Testing: {name}");
            logger.Information($"📡 Endpoint: {endpoint}");

            var response = await httpClient.GetAsync(url);
            var content = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
            {
                logger.Information($"✅ Status: {response.StatusCode} - SUCCESS");
                
                // Try to parse and show basic info
                if (content.Length > 0)
                {
                    var preview = content.Length > 200 ? content[..200] + "..." : content;
                    logger.Information($"📄 Response Preview: {preview}");
                }
            }
            else
            {
                logger.Warning($"❌ Status: {response.StatusCode} - {response.ReasonPhrase}");
                
                if (!string.IsNullOrEmpty(content))
                {
                    var errorPreview = content.Length > 100 ? content[..100] + "..." : content;
                    logger.Warning($"📄 Error Details: {errorPreview}");
                }
            }
        }
        catch (Exception ex)
        {
            logger.Error($"❌ Exception testing {name}: {ex.Message}");
        }
    }
}
