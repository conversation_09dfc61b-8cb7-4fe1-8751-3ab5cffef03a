using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using ZeroDateStrat.Services;
using ZeroDateStrat.Models;

namespace ZeroDateStrat.Tests;

/// <summary>
/// Practical example of using the guidance request system to get help from ChatGptBot
/// </summary>
public static class PracticalGuidanceExample
{
    public static async Task RunPracticalExampleAsync()
    {
        Console.WriteLine("🚀 Practical Guidance Request Example");
        Console.WriteLine("=====================================");
        Console.WriteLine();

        try
        {
            // Build a minimal service provider for testing
            var services = new ServiceCollection();
            
            // Add logging
            services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Information));
            
            // Add configuration
            services.AddSingleton<Microsoft.Extensions.Configuration.IConfiguration>(provider =>
            {
                var config = new Microsoft.Extensions.Configuration.ConfigurationBuilder()
                    .AddJsonFile("appsettings.json", optional: false)
                    .Build();
                return config;
            });

            // Add Discord and Guidance services
            services.AddSingleton<IDiscordService, DiscordService>();
            services.AddSingleton<IGuidanceRequestService, GuidanceRequestService>();
            
            var serviceProvider = services.BuildServiceProvider();
            var guidanceService = serviceProvider.GetRequiredService<IGuidanceRequestService>();

            Console.WriteLine("✅ Services initialized successfully");
            Console.WriteLine();

            // Wait a moment for Discord to connect
            await Task.Delay(3000);

            // Example 1: Request help with the circular dependency issue we just encountered
            Console.WriteLine("📝 Example 1: Requesting help with circular dependency issue");
            Console.WriteLine("-----------------------------------------------------------");
            
            var response1 = await guidanceService.RequestGuidanceAsync(
                taskStatement: "I have a circular dependency in my .NET dependency injection container. The error shows: ISyntheticVixService -> ISyntheticVixAnalyticsService -> ISyntheticVixService. How do I resolve this circular dependency pattern in C#?",
                language: "C#",
                components: new List<string> { "Dependency Injection", "Service Registration", "Interface Design" },
                notes: "This is for a trading application with VIX analysis services. Need a clean solution that maintains separation of concerns."
            );

            if (response1 != null)
            {
                Console.WriteLine($"✅ Received guidance from ChatGptBot:");
                Console.WriteLine($"   Content: {response1.Content.Substring(0, Math.Min(200, response1.Content.Length))}...");
                Console.WriteLine($"   Author: {response1.AuthorUsername}");
                Console.WriteLine($"   Received at: {response1.ReceivedAt}");
                Console.WriteLine();
                
                // Store the response for later use
                Console.WriteLine("💾 Response stored for implementation");
            }
            else
            {
                Console.WriteLine("⚠️ No response received - this is expected in test mode without live Discord");
            }

            Console.WriteLine();

            // Example 2: Request help with VIX data scraping implementation
            Console.WriteLine("📝 Example 2: Requesting VIX data scraping guidance");
            Console.WriteLine("--------------------------------------------------");
            
            var response2 = await guidanceService.RequestGuidanceAsync(
                taskStatement: "I need to implement a robust VIX data scraper that can fetch real-time VIX values with proper error handling, retry logic, and fallback data sources. The scraper should integrate with my existing trading system.",
                language: "C#",
                components: new List<string> { "HTTP Client", "Error Handling", "Retry Logic", "Data Validation", "Caching" },
                notes: "For 0DTE options trading system. Must be production-ready with comprehensive error handling."
            );

            if (response2 != null)
            {
                Console.WriteLine($"✅ Received VIX scraping guidance:");
                Console.WriteLine($"   Content: {response2.Content.Substring(0, Math.Min(200, response2.Content.Length))}...");
                Console.WriteLine();
                
                // Demonstrate how AugmentBot would use this response
                Console.WriteLine("🔧 AugmentBot would now implement the guidance:");
                Console.WriteLine("   1. Create VixDataScraper class based on response");
                Console.WriteLine("   2. Implement error handling patterns suggested");
                Console.WriteLine("   3. Add retry logic as recommended");
                Console.WriteLine("   4. Integrate with existing services");
            }
            else
            {
                Console.WriteLine("⚠️ No response received - this is expected in test mode");
            }

            Console.WriteLine();
            Console.WriteLine("🎉 Practical guidance example completed!");
            Console.WriteLine();
            Console.WriteLine("💡 In a live environment:");
            Console.WriteLine("   - AugmentBot sends these requests to Discord");
            Console.WriteLine("   - @ChatGptBot processes them via OpenAI API");
            Console.WriteLine("   - AugmentBot receives detailed technical guidance");
            Console.WriteLine("   - AugmentBot implements the suggested solutions");
            Console.WriteLine("   - All responses are stored for future reference");

        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Error in practical guidance example: {ex.Message}");
            Console.WriteLine($"   Stack trace: {ex.StackTrace}");
        }
    }
}
