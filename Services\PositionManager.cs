using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using ZeroDateStrat.Models;

namespace ZeroDateStrat.Services;

// Phase 2: Enhanced Position Management Interface
public interface IPositionManager
{
    Task<List<Models.ManagedPosition>> GetActivePositionsAsync();
    Task<Models.ManagedPosition?> GetPositionAsync(string positionId);
    Task<bool> UpdatePositionAsync(Models.ManagedPosition position);
    Task<bool> ClosePositionAsync(string positionId, string reason);
    Task<List<Models.ManagedPosition>> GetPositionsRequiringActionAsync();
    Task<bool> AdjustPositionAsync(string positionId, string adjustmentType);
    Task<PortfolioSnapshot> GetPortfolioSnapshotAsync();
    Task<bool> MonitorPositionsAsync();
    Task<List<PositionSummary>> GetPositionSummariesAsync();
    Task<bool> ApplyProfitTargetsAsync();
    Task<bool> ApplyStopLossesAsync();
}

public class PositionManager : IPositionManager
{
    private readonly ILogger<PositionManager> _logger;
    private readonly IConfiguration _configuration;
    private readonly IAlpacaService _alpacaService;
    private readonly IAdvancedRiskManager _riskManager;
    private readonly List<Models.ManagedPosition> _positions = new();

    public PositionManager(
        ILogger<PositionManager> logger,
        IConfiguration configuration,
        IAlpacaService alpacaService,
        IAdvancedRiskManager riskManager)
    {
        _logger = logger;
        _configuration = configuration;
        _alpacaService = alpacaService;
        _riskManager = riskManager;
    }

    public async Task<List<Models.ManagedPosition>> GetActivePositionsAsync()
    {
        try
        {
            // Sync with broker positions
            await SyncWithBrokerPositionsAsync();
            
            return _positions.Where(p => p.Status == PositionStatus.Open).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active positions");
            return new List<Models.ManagedPosition>();
        }
    }

    public async Task<Models.ManagedPosition?> GetPositionAsync(string positionId)
    {
        try
        {
            var position = _positions.FirstOrDefault(p => p.PositionId == positionId);
            if (position != null)
            {
                await UpdatePositionValuesAsync(position);
            }
            return position;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error getting position {positionId}");
            return null;
        }
    }

    public async Task<bool> UpdatePositionAsync(Models.ManagedPosition position)
    {
        try
        {
            await UpdatePositionValuesAsync(position);
            
            var existingPosition = _positions.FirstOrDefault(p => p.PositionId == position.PositionId);
            if (existingPosition != null)
            {
                var index = _positions.IndexOf(existingPosition);
                _positions[index] = position;
            }
            else
            {
                _positions.Add(position);
            }

            _logger.LogDebug($"Updated position {position.PositionId}: P&L = {position.UnrealizedPnL:C}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error updating position {position.PositionId}");
            return false;
        }
    }

    public async Task<bool> ClosePositionAsync(string positionId, string reason)
    {
        try
        {
            var position = _positions.FirstOrDefault(p => p.PositionId == positionId);
            if (position == null)
            {
                _logger.LogWarning($"Position {positionId} not found");
                return false;
            }

            _logger.LogInformation($"Closing position {positionId}: {reason}");

            // Create closing orders for each leg
            foreach (var leg in position.Legs)
            {
                var closingSignal = CreateClosingSignal(leg);
                var order = await _alpacaService.PlaceOrderAsync(closingSignal);
                
                if (order == null)
                {
                    _logger.LogError($"Failed to place closing order for leg {leg.Symbol}");
                    return false;
                }
            }

            // Update position status
            position.Status = PositionStatus.Closed;
            position.CloseTime = DateTime.UtcNow;
            
            // Add adjustment record
            position.Adjustments.Add(new PositionAdjustment
            {
                Timestamp = DateTime.UtcNow,
                AdjustmentType = "Close",
                Description = reason,
                PnLImpact = position.UnrealizedPnL
            });

            _logger.LogInformation($"Position {positionId} closed successfully. Final P&L: {position.UnrealizedPnL:C}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error closing position {positionId}");
            return false;
        }
    }

    public async Task<List<Models.ManagedPosition>> GetPositionsRequiringActionAsync()
    {
        try
        {
            var actionRequired = new List<Models.ManagedPosition>();
            var activePositions = await GetActivePositionsAsync();

            foreach (var position in activePositions)
            {
                await UpdatePositionValuesAsync(position);

                // Check profit targets
                if (position.UnrealizedPnL >= position.ProfitTarget)
                {
                    actionRequired.Add(position);
                    continue;
                }

                // Check stop losses
                if (position.UnrealizedPnL <= -position.StopLoss)
                {
                    actionRequired.Add(position);
                    continue;
                }

                // Check time-based exits (for 0 DTE)
                var timeToExpiration = GetTimeToExpiration(position);
                var forceCloseTime = _configuration.GetValue<TimeSpan>("Trading:ForceCloseTime", TimeSpan.Parse("15:45:00"));
                
                if (timeToExpiration < TimeSpan.FromHours(1) || DateTime.Now.TimeOfDay >= forceCloseTime)
                {
                    actionRequired.Add(position);
                }
            }

            return actionRequired;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting positions requiring action");
            return new List<Models.ManagedPosition>();
        }
    }

    public async Task<bool> AdjustPositionAsync(string positionId, string adjustmentType)
    {
        try
        {
            var position = _positions.FirstOrDefault(p => p.PositionId == positionId);
            if (position == null) return false;

            _logger.LogInformation($"Adjusting position {positionId} with {adjustmentType}");

            switch (adjustmentType.ToLower())
            {
                case "roll":
                    return await RollPositionAsync(position);
                case "hedge":
                    return await HedgePositionAsync(position);
                case "reduce":
                    return await ReducePositionAsync(position);
                default:
                    _logger.LogWarning($"Unknown adjustment type: {adjustmentType}");
                    return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error adjusting position {positionId}");
            return false;
        }
    }

    public async Task<PortfolioSnapshot> GetPortfolioSnapshotAsync()
    {
        try
        {
            var snapshot = new PortfolioSnapshot
            {
                Timestamp = DateTime.UtcNow
            };

            var account = await _alpacaService.GetAccountAsync();
            if (account != null)
            {
                snapshot.TotalValue = (decimal)account.Equity;
                snapshot.DayPnL = (decimal)(account.Equity - account.LastEquity);
                snapshot.BuyingPower = (decimal)account.BuyingPower;
            }

            var activePositions = await GetActivePositionsAsync();
            snapshot.ActivePositions = activePositions.Count;

            // Calculate total P&L
            snapshot.TotalPnL = activePositions.Sum(p => p.UnrealizedPnL);

            // Get Greeks by symbol
            var greeks = await _alpacaService.GetPortfolioGreeksAsync();
            snapshot.GreeksBySymbol = greeks;

            // Get top positions
            snapshot.TopPositions = activePositions
                .OrderByDescending(p => Math.Abs(p.CurrentValue))
                .Take(5)
                .Select(p => new PositionSummary
                {
                    Symbol = p.UnderlyingSymbol,
                    Strategy = p.Strategy,
                    Value = p.CurrentValue,
                    PnL = p.UnrealizedPnL,
                    DaysToExpiration = (decimal)GetTimeToExpiration(p).TotalDays,
                    RiskLevel = CalculatePositionRisk(p)
                })
                .ToList();

            return snapshot;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating portfolio snapshot");
            return new PortfolioSnapshot { Timestamp = DateTime.UtcNow };
        }
    }

    public async Task<bool> MonitorPositionsAsync()
    {
        try
        {
            _logger.LogInformation("Monitoring positions for management opportunities");

            var positionsRequiringAction = await GetPositionsRequiringActionAsync();
            
            foreach (var position in positionsRequiringAction)
            {
                await ProcessPositionAction(position);
            }

            // Apply profit targets and stop losses
            await ApplyProfitTargetsAsync();
            await ApplyStopLossesAsync();

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error monitoring positions");
            return false;
        }
    }

    public async Task<List<PositionSummary>> GetPositionSummariesAsync()
    {
        try
        {
            var activePositions = await GetActivePositionsAsync();
            
            return activePositions.Select(p => new PositionSummary
            {
                Symbol = p.UnderlyingSymbol,
                Strategy = p.Strategy,
                Value = p.CurrentValue,
                PnL = p.UnrealizedPnL,
                DaysToExpiration = (decimal)GetTimeToExpiration(p).TotalDays,
                RiskLevel = CalculatePositionRisk(p)
            }).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting position summaries");
            return new List<PositionSummary>();
        }
    }

    public async Task<bool> ApplyProfitTargetsAsync()
    {
        try
        {
            var activePositions = await GetActivePositionsAsync();
            var profitTargetPercent = _configuration.GetValue<decimal>("Trading:ProfitTargetPercent", 0.5m);

            foreach (var position in activePositions)
            {
                if (position.UnrealizedPnL >= position.OpenCredit * profitTargetPercent)
                {
                    await ClosePositionAsync(position.PositionId, "Profit target reached");
                }
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error applying profit targets");
            return false;
        }
    }

    public async Task<bool> ApplyStopLossesAsync()
    {
        try
        {
            var activePositions = await GetActivePositionsAsync();
            var stopLossPercent = _configuration.GetValue<decimal>("Trading:StopLossPercent", 2.0m);

            foreach (var position in activePositions)
            {
                if (position.UnrealizedPnL <= -position.OpenCredit * stopLossPercent)
                {
                    await ClosePositionAsync(position.PositionId, "Stop loss triggered");
                }
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error applying stop losses");
            return false;
        }
    }

    // Helper methods for Phase 2 position management
    private async Task SyncWithBrokerPositionsAsync()
    {
        try
        {
            var brokerPositions = await _alpacaService.GetPositionsAsync();

            // Update existing positions or add new ones
            foreach (var brokerPosition in brokerPositions)
            {
                var existingPosition = _positions.FirstOrDefault(p =>
                    p.Legs.Any(l => l.Symbol == brokerPosition.Symbol));

                if (existingPosition == null)
                {
                    // Create new position from broker data
                    var newPosition = CreatePositionFromBrokerData(brokerPosition);
                    _positions.Add(newPosition);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error syncing with broker positions");
        }
    }

    private Models.ManagedPosition CreatePositionFromBrokerData(Alpaca.Markets.IPosition brokerPosition)
    {
        return new Models.ManagedPosition
        {
            PositionId = Guid.NewGuid().ToString(),
            UnderlyingSymbol = ExtractUnderlyingSymbol(brokerPosition.Symbol),
            Strategy = "Unknown", // Would need to determine from position structure
            OpenTime = DateTime.UtcNow, // Would get from order history
            Status = PositionStatus.Open,
            Legs = new List<PositionLeg>
            {
                new PositionLeg
                {
                    Symbol = brokerPosition.Symbol,
                    Quantity = (int)brokerPosition.Quantity,
                    OpenPrice = (decimal)brokerPosition.AverageEntryPrice,
                    CurrentPrice = (decimal)brokerPosition.MarketValue / brokerPosition.Quantity,
                    Side = brokerPosition.Quantity > 0 ? OrderSide.Buy : OrderSide.Sell
                }
            }
        };
    }

    private async Task UpdatePositionValuesAsync(Models.ManagedPosition position)
    {
        try
        {
            decimal totalValue = 0;

            foreach (var leg in position.Legs)
            {
                var currentPrice = await _alpacaService.GetCurrentPriceAsync(leg.Symbol);
                leg.CurrentPrice = currentPrice;

                var legValue = leg.Quantity * currentPrice;
                if (leg.Side == OrderSide.Sell)
                    legValue = -legValue;

                totalValue += legValue;
            }

            position.CurrentValue = totalValue;
            position.UnrealizedPnL = position.OpenCredit + totalValue;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error updating position values for {position.PositionId}");
        }
    }

    private TradingSignal CreateClosingSignal(PositionLeg leg)
    {
        return new TradingSignal
        {
            Id = Guid.NewGuid().ToString(),
            Strategy = "Close",
            UnderlyingSymbol = ExtractUnderlyingSymbol(leg.Symbol),
            Legs = new List<OptionLeg>
            {
                new OptionLeg
                {
                    Symbol = leg.Symbol,
                    Quantity = Math.Abs(leg.Quantity),
                    Side = leg.Side == OrderSide.Buy ? OrderSide.Sell : OrderSide.Buy,
                    Price = leg.CurrentPrice
                }
            }
        };
    }

    private TimeSpan GetTimeToExpiration(Models.ManagedPosition position)
    {
        // For 0 DTE options, expiration is typically 4:00 PM ET
        var today = DateTime.Today;
        var expirationTime = today.AddHours(16); // 4:00 PM

        if (DateTime.Now > expirationTime)
            return TimeSpan.Zero;

        return expirationTime - DateTime.Now;
    }

    private RiskLevel CalculatePositionRisk(Models.ManagedPosition position)
    {
        try
        {
            var timeToExpiration = GetTimeToExpiration(position);
            var pnlRatio = position.OpenCredit != 0 ? position.UnrealizedPnL / position.OpenCredit : 0;

            // High risk if close to expiration and losing money
            if (timeToExpiration < TimeSpan.FromHours(2) && pnlRatio < -0.5m)
                return RiskLevel.Critical;

            if (timeToExpiration < TimeSpan.FromHours(4) && pnlRatio < -0.3m)
                return RiskLevel.High;

            if (pnlRatio < -0.2m)
                return RiskLevel.Medium;

            return RiskLevel.Low;
        }
        catch
        {
            return RiskLevel.Medium;
        }
    }

    private async Task ProcessPositionAction(Models.ManagedPosition position)
    {
        try
        {
            var timeToExpiration = GetTimeToExpiration(position);
            var pnlRatio = position.OpenCredit != 0 ? position.UnrealizedPnL / position.OpenCredit : 0;

            // Profit target reached
            if (pnlRatio >= 0.5m)
            {
                await ClosePositionAsync(position.PositionId, "Profit target reached");
                return;
            }

            // Stop loss triggered
            if (pnlRatio <= -2.0m)
            {
                await ClosePositionAsync(position.PositionId, "Stop loss triggered");
                return;
            }

            // Force close near expiration
            if (timeToExpiration < TimeSpan.FromMinutes(30))
            {
                await ClosePositionAsync(position.PositionId, "Approaching expiration");
                return;
            }

            // Consider adjustments for positions in trouble
            if (pnlRatio < -0.5m && timeToExpiration > TimeSpan.FromHours(2))
            {
                await AdjustPositionAsync(position.PositionId, "hedge");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error processing action for position {position.PositionId}");
        }
    }

    private async Task<bool> RollPositionAsync(Models.ManagedPosition position)
    {
        // Placeholder for rolling positions to next expiration
        _logger.LogInformation($"Rolling position {position.PositionId} (not implemented)");
        return false;
    }

    private async Task<bool> HedgePositionAsync(Models.ManagedPosition position)
    {
        // Placeholder for hedging positions
        _logger.LogInformation($"Hedging position {position.PositionId} (not implemented)");
        return false;
    }

    private async Task<bool> ReducePositionAsync(Models.ManagedPosition position)
    {
        // Placeholder for reducing position size
        _logger.LogInformation($"Reducing position {position.PositionId} (not implemented)");
        return false;
    }

    private string ExtractUnderlyingSymbol(string optionSymbol)
    {
        if (optionSymbol.Contains("SPX")) return "SPX";
        if (optionSymbol.Contains("SPY")) return "SPY";
        if (optionSymbol.Contains("QQQ")) return "QQQ";

        // For other symbols, take first 3-4 characters
        return optionSymbol.Length > 3 ? optionSymbol.Substring(0, 3) : optionSymbol;
    }
}
