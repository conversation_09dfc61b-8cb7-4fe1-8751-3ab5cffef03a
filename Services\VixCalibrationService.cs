using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using ZeroDateStrat.Models;
using System.Collections.Concurrent;

namespace ZeroDateStrat.Services;

/// <summary>
/// Service for calibrating synthetic VIX calculations against real VIX data from Polygon
/// Continuously tunes synthetic VIX weights and parameters to maintain accuracy
/// </summary>
public interface IVixCalibrationService
{
    Task<VixCalibrationResult> CalibrateAsync(decimal realVix, decimal syntheticVix);
    Task<VixCalibrationReport> GetCalibrationReportAsync();
    Task<VixCalibrationRecommendations> GetRecommendationsAsync();
    Task<bool> ShouldRecalibrateAsync();
    Task ResetCalibrationAsync();
    Task<VixCalibrationMetrics> GetMetricsAsync();
}

public class VixCalibrationService : IVixCalibrationService
{
    private readonly ILogger<VixCalibrationService> _logger;
    private readonly IConfiguration _configuration;
    private readonly ISyntheticVixService _syntheticVixService;
    private readonly IPolygonDataService _polygonDataService;

    // Calibration data storage (thread-safe)
    private readonly ConcurrentQueue<VixCalibrationDataPoint> _calibrationHistory = new();
    private readonly int _maxHistoryPoints = 1000; // Keep last 1000 calibration points
    
    // Calibration metrics
    private decimal _currentBias = 0m;
    private decimal _currentScalingFactor = 1.0m;
    private decimal _correlationCoefficient = 0m;
    private DateTime _lastCalibration = DateTime.MinValue;
    private readonly TimeSpan _calibrationInterval;
    private readonly decimal _recalibrationThreshold;

    public VixCalibrationService(
        ILogger<VixCalibrationService> logger,
        IConfiguration configuration,
        ISyntheticVixService syntheticVixService,
        IPolygonDataService polygonDataService)
    {
        _logger = logger;
        _configuration = configuration;
        _syntheticVixService = syntheticVixService;
        _polygonDataService = polygonDataService;

        // Load configuration
        _calibrationInterval = TimeSpan.FromMinutes(
            _configuration.GetValue<int>("VixCalibration:IntervalMinutes", 30));
        _recalibrationThreshold = 
            _configuration.GetValue<decimal>("VixCalibration:RecalibrationThreshold", 0.15m);

        _logger.LogInformation("VixCalibrationService initialized with {Interval} minute intervals", 
            _calibrationInterval.TotalMinutes);
    }

    public async Task<VixCalibrationResult> CalibrateAsync(decimal realVix, decimal syntheticVix)
    {
        try
        {
            var timestamp = DateTime.UtcNow;
            
            // Store calibration data point
            var dataPoint = new VixCalibrationDataPoint
            {
                Timestamp = timestamp,
                RealVix = realVix,
                SyntheticVix = syntheticVix,
                Difference = realVix - syntheticVix,
                PercentDifference = Math.Abs(realVix - syntheticVix) / realVix * 100
            };

            _calibrationHistory.Enqueue(dataPoint);

            // Maintain history size
            while (_calibrationHistory.Count > _maxHistoryPoints)
            {
                _calibrationHistory.TryDequeue(out _);
            }

            // Calculate calibration metrics
            await UpdateCalibrationMetricsAsync();

            var result = new VixCalibrationResult
            {
                Timestamp = timestamp,
                RealVix = realVix,
                SyntheticVix = syntheticVix,
                CalibratedVix = ApplyCalibration(syntheticVix),
                Bias = _currentBias,
                ScalingFactor = _currentScalingFactor,
                Correlation = _correlationCoefficient,
                IsSignificantDrift = dataPoint.PercentDifference > _recalibrationThreshold * 100
            };

            _logger.LogDebug($"VIX Calibration - Real: {realVix:F2}, Synthetic: {syntheticVix:F2}, " +
                           $"Calibrated: {result.CalibratedVix:F2}, Bias: {_currentBias:F3}, " +
                           $"Scaling: {_currentScalingFactor:F3}, Correlation: {_correlationCoefficient:F3}");

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during VIX calibration");
            return new VixCalibrationResult
            {
                Timestamp = DateTime.UtcNow,
                RealVix = realVix,
                SyntheticVix = syntheticVix,
                CalibratedVix = syntheticVix,
                Bias = 0,
                ScalingFactor = 1.0m,
                Correlation = 0,
                IsSignificantDrift = false
            };
        }
    }

    public async Task<VixCalibrationReport> GetCalibrationReportAsync()
    {
        try
        {
            var history = _calibrationHistory.ToArray();
            if (history.Length == 0)
            {
                return new VixCalibrationReport
                {
                    Timestamp = DateTime.UtcNow,
                    Status = "No calibration data available",
                    DataPoints = 0
                };
            }

            var recent = history.Where(h => h.Timestamp > DateTime.UtcNow.AddHours(-24)).ToArray();
            
            var report = new VixCalibrationReport
            {
                Timestamp = DateTime.UtcNow,
                Status = GetCalibrationStatus(),
                DataPoints = history.Length,
                RecentDataPoints = recent.Length,
                AverageBias = _currentBias,
                CurrentScalingFactor = _currentScalingFactor,
                CorrelationCoefficient = _correlationCoefficient,
                AverageAbsoluteError = recent.Any() ? recent.Average(r => Math.Abs(r.Difference)) : 0,
                MaxError = recent.Any() ? recent.Max(r => Math.Abs(r.Difference)) : 0,
                LastCalibration = _lastCalibration,
                RecommendRecalibration = await ShouldRecalibrateAsync()
            };

            return report;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating calibration report");
            return new VixCalibrationReport
            {
                Timestamp = DateTime.UtcNow,
                Status = "Error generating report",
                DataPoints = 0
            };
        }
    }

    public async Task<VixCalibrationRecommendations> GetRecommendationsAsync()
    {
        try
        {
            var recommendations = new VixCalibrationRecommendations
            {
                Timestamp = DateTime.UtcNow,
                Recommendations = new List<string>()
            };

            var history = _calibrationHistory.ToArray();
            if (history.Length < 10)
            {
                recommendations.Recommendations.Add("Insufficient data for recommendations (need at least 10 data points)");
                return recommendations;
            }

            var recent = history.Where(h => h.Timestamp > DateTime.UtcNow.AddHours(-6)).ToArray();
            
            // Check for systematic bias
            if (Math.Abs(_currentBias) > 2.0m)
            {
                recommendations.Recommendations.Add($"Significant bias detected ({_currentBias:F2}). Consider adjusting synthetic VIX baseline.");
            }

            // Check for scaling issues
            if (_currentScalingFactor < 0.8m || _currentScalingFactor > 1.2m)
            {
                recommendations.Recommendations.Add($"Scaling factor out of normal range ({_currentScalingFactor:F3}). Review component weights.");
            }

            // Check correlation
            if (_correlationCoefficient < 0.7m)
            {
                recommendations.Recommendations.Add($"Low correlation with real VIX ({_correlationCoefficient:F3}). Consider component rebalancing.");
            }

            // Check recent accuracy
            if (recent.Any() && recent.Average(r => r.PercentDifference) > 20)
            {
                recommendations.Recommendations.Add("Recent accuracy degraded. Immediate recalibration recommended.");
            }

            if (recommendations.Recommendations.Count == 0)
            {
                recommendations.Recommendations.Add("Calibration appears optimal. No adjustments needed.");
            }

            return recommendations;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating calibration recommendations");
            return new VixCalibrationRecommendations
            {
                Timestamp = DateTime.UtcNow,
                Recommendations = new List<string> { "Error generating recommendations" }
            };
        }
    }

    public async Task<bool> ShouldRecalibrateAsync()
    {
        try
        {
            var history = _calibrationHistory.ToArray();
            if (history.Length < 5) return false;

            var recent = history.Where(h => h.Timestamp > DateTime.UtcNow.AddHours(-2)).ToArray();
            if (recent.Length == 0) return false;

            // Check if recent average error exceeds threshold
            var avgError = recent.Average(r => r.PercentDifference);
            return avgError > _recalibrationThreshold * 100;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking recalibration need");
            return false;
        }
    }

    public async Task ResetCalibrationAsync()
    {
        try
        {
            _calibrationHistory.Clear();
            _currentBias = 0m;
            _currentScalingFactor = 1.0m;
            _correlationCoefficient = 0m;
            _lastCalibration = DateTime.UtcNow;

            _logger.LogInformation("VIX calibration reset completed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resetting calibration");
        }
    }

    public async Task<VixCalibrationMetrics> GetMetricsAsync()
    {
        try
        {
            var history = _calibrationHistory.ToArray();
            
            return new VixCalibrationMetrics
            {
                Timestamp = DateTime.UtcNow,
                TotalDataPoints = history.Length,
                CurrentBias = _currentBias,
                CurrentScalingFactor = _currentScalingFactor,
                CorrelationCoefficient = _correlationCoefficient,
                LastCalibration = _lastCalibration,
                CalibrationInterval = _calibrationInterval,
                RecalibrationThreshold = _recalibrationThreshold
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting calibration metrics");
            return new VixCalibrationMetrics { Timestamp = DateTime.UtcNow };
        }
    }

    private async Task UpdateCalibrationMetricsAsync()
    {
        try
        {
            var history = _calibrationHistory.ToArray();
            if (history.Length < 2) return;

            // Calculate bias (average difference)
            _currentBias = history.Average(h => h.Difference);

            // Calculate scaling factor based on recent data
            var recent = history.Where(h => h.Timestamp > DateTime.UtcNow.AddHours(-6)).ToArray();
            if (recent.Length > 1)
            {
                var avgReal = recent.Average(r => r.RealVix);
                var avgSynthetic = recent.Average(r => r.SyntheticVix);
                if (avgSynthetic > 0)
                {
                    _currentScalingFactor = avgReal / avgSynthetic;
                }
            }

            // Calculate correlation coefficient
            if (history.Length > 10)
            {
                _correlationCoefficient = CalculateCorrelation(
                    history.Select(h => (double)h.RealVix).ToArray(),
                    history.Select(h => (double)h.SyntheticVix).ToArray());
            }

            _lastCalibration = DateTime.UtcNow;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating calibration metrics");
        }
    }

    private decimal ApplyCalibration(decimal syntheticVix)
    {
        // Apply scaling factor and bias correction
        return (syntheticVix * _currentScalingFactor) + _currentBias;
    }

    private string GetCalibrationStatus()
    {
        if (_correlationCoefficient > 0.8m && Math.Abs(_currentBias) < 1.0m)
            return "Excellent";
        if (_correlationCoefficient > 0.7m && Math.Abs(_currentBias) < 2.0m)
            return "Good";
        if (_correlationCoefficient > 0.5m)
            return "Fair";
        return "Poor";
    }

    private static decimal CalculateCorrelation(double[] x, double[] y)
    {
        if (x.Length != y.Length || x.Length == 0) return 0;

        var avgX = x.Average();
        var avgY = y.Average();

        var numerator = x.Zip(y, (xi, yi) => (xi - avgX) * (yi - avgY)).Sum();
        var denominator = Math.Sqrt(x.Sum(xi => Math.Pow(xi - avgX, 2)) * y.Sum(yi => Math.Pow(yi - avgY, 2)));

        return denominator == 0 ? 0 : (decimal)(numerator / denominator);
    }
}
