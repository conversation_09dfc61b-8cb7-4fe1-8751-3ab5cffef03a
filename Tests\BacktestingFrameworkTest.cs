using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using ZeroDateStrat.Models;
using ZeroDateStrat.Services;
using ZeroDateStrat.Utils;

namespace ZeroDateStrat.Tests;

public class BacktestingFrameworkTest
{
    private readonly ILogger<BacktestingFrameworkTest> _logger;
    private readonly IServiceProvider _serviceProvider;

    public BacktestingFrameworkTest()
    {
        // Setup dependency injection for testing
        var services = new ServiceCollection();
        
        // Add logging
        services.AddLogging(builder => builder.AddConsole());
        
        // Add configuration
        var configuration = new ConfigurationBuilder()
            .AddJsonFile("appsettings.json", optional: false)
            .Build();
        services.AddSingleton<IConfiguration>(configuration);
        
        // Add services
        services.AddScoped<ISecurityService, SecurityService>();
        services.AddScoped<IConfigurationValidator, ConfigurationValidator>();
        services.AddScoped<IGlobalExceptionHandler, GlobalExceptionHandler>();
        services.AddScoped<IHistoricalDataService, HistoricalDataService>();
        services.AddScoped<IBacktestingEngine, BacktestingEngine>();
        services.AddScoped<IPerformanceAnalytics, PerformanceAnalytics>();
        services.AddScoped<IAlpacaService, AlpacaService>();
        services.AddScoped<IOptionsScanner, OptionsScanner>();
        services.AddScoped<IRiskManager, RiskManager>();
        services.AddScoped<IMarketRegimeAnalyzer, MarketRegimeAnalyzer>();
        
        _serviceProvider = services.BuildServiceProvider();
        _logger = _serviceProvider.GetRequiredService<ILogger<BacktestingFrameworkTest>>();
    }

    public async Task TestBacktestingFramework()
    {
        Console.WriteLine("=== Testing Backtesting Framework ===\n");

        try
        {
            // Test 1: Historical Data Service
            await TestHistoricalDataService();

            // Test 2: Basic Backtest
            await TestBasicBacktest();

            // Test 3: Performance Analytics
            await TestPerformanceAnalytics();

            // Test 4: Parameter Optimization (simplified)
            await TestParameterOptimization();

            Console.WriteLine("\n=== All Backtesting Framework Tests Completed Successfully ===");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Backtesting framework test failed: {ex.Message}");
            _logger.LogError(ex, "Backtesting framework test failed");
        }
    }

    private async Task TestHistoricalDataService()
    {
        Console.WriteLine("1. Testing Historical Data Service...");
        
        var historicalDataService = _serviceProvider.GetRequiredService<IHistoricalDataService>();
        
        // Test historical bars
        var startDate = DateTime.Today.AddDays(-30);
        var endDate = DateTime.Today.AddDays(-1);
        var bars = await historicalDataService.GetHistoricalBarsAsync("SPY", startDate, endDate);
        
        Console.WriteLine($"   Generated {bars.Count} historical bars for SPY");
        Console.WriteLine($"   Sample bar: {bars.FirstOrDefault()?.Date:yyyy-MM-dd} O:{bars.FirstOrDefault()?.Open:F2} H:{bars.FirstOrDefault()?.High:F2} L:{bars.FirstOrDefault()?.Low:F2} C:{bars.FirstOrDefault()?.Close:F2}");
        
        // Test historical option chain
        var testDate = DateTime.Today.AddDays(-5);
        var optionChain = await historicalDataService.GetHistoricalOptionChainAsync("SPY", testDate);
        
        Console.WriteLine($"   Generated {optionChain.Count} option contracts for {testDate:yyyy-MM-dd}");
        
        var atmCall = optionChain.Where(o => o.OptionType == Models.OptionType.Call)
            .OrderBy(o => Math.Abs(o.StrikePrice - optionChain.First().LastPrice))
            .FirstOrDefault();
        
        if (atmCall != null)
        {
            Console.WriteLine($"   Sample ATM Call: Strike {atmCall.StrikePrice:F2}, Price {atmCall.LastPrice:F2}, Delta {atmCall.Delta:F3}");
        }
        
        // Test historical volatility
        var volatilities = await historicalDataService.GetHistoricalVolatilityAsync("SPY", startDate, endDate);
        Console.WriteLine($"   Calculated {volatilities.Count} volatility data points");
        
        if (volatilities.Any())
        {
            var avgVol = volatilities.Average(v => v.RealizedVolatility);
            Console.WriteLine($"   Average realized volatility: {avgVol:P1}");
        }
        
        Console.WriteLine("   ✅ Historical Data Service test completed\n");
    }

    private async Task TestBasicBacktest()
    {
        Console.WriteLine("2. Testing Basic Backtest...");
        
        var backtestingEngine = _serviceProvider.GetRequiredService<IBacktestingEngine>();
        
        var config = new BacktestConfiguration
        {
            StrategyName = "ZeroDTE_Test",
            StartDate = DateTime.Today.AddDays(-60),
            EndDate = DateTime.Today.AddDays(-1),
            InitialCapital = 10000m,
            Symbols = new List<string> { "SPY" },
            Parameters = new Dictionary<string, object>
            {
                ["ProfitTarget"] = 0.5m,
                ["StopLoss"] = 0.8m,
                ["MaxPositions"] = 3
            }
        };
        
        var result = await backtestingEngine.RunBacktestAsync(config);
        
        Console.WriteLine($"   Backtest Results for {result.StrategyName}:");
        Console.WriteLine($"   Period: {result.StartDate:yyyy-MM-dd} to {result.EndDate:yyyy-MM-dd}");
        Console.WriteLine($"   Initial Capital: {result.InitialCapital:C2}");
        Console.WriteLine($"   Final Capital: {result.FinalCapital:C2}");
        Console.WriteLine($"   Total Return: {result.TotalReturn:P2}");
        Console.WriteLine($"   Annualized Return: {result.AnnualizedReturn:P2}");
        Console.WriteLine($"   Maximum Drawdown: {result.MaxDrawdown:P2}");
        Console.WriteLine($"   Sharpe Ratio: {result.SharpeRatio:F3}");
        Console.WriteLine($"   Total Trades: {result.TotalTrades}");
        Console.WriteLine($"   Win Rate: {result.WinRate:P1}");
        Console.WriteLine($"   Profit Factor: {result.ProfitFactor:F2}");
        
        if (result.Trades.Any())
        {
            Console.WriteLine($"   Average Trade PnL: {result.Trades.Average(t => t.RealizedPnL):C2}");
            Console.WriteLine($"   Best Trade: {result.Trades.Max(t => t.RealizedPnL):C2}");
            Console.WriteLine($"   Worst Trade: {result.Trades.Min(t => t.RealizedPnL):C2}");
        }
        
        Console.WriteLine("   ✅ Basic Backtest test completed\n");
    }

    private async Task TestPerformanceAnalytics()
    {
        Console.WriteLine("3. Testing Performance Analytics...");
        
        var backtestingEngine = _serviceProvider.GetRequiredService<IBacktestingEngine>();
        var performanceAnalytics = _serviceProvider.GetRequiredService<IPerformanceAnalytics>();
        
        // Run a quick backtest to get results
        var config = new BacktestConfiguration
        {
            StrategyName = "Analytics_Test",
            StartDate = DateTime.Today.AddDays(-30),
            EndDate = DateTime.Today.AddDays(-1),
            InitialCapital = 10000m,
            Symbols = new List<string> { "SPY" }
        };
        
        var result = await backtestingEngine.RunBacktestAsync(config);
        
        // Generate performance report
        var report = performanceAnalytics.GeneratePerformanceReport(result);
        
        Console.WriteLine($"   Performance Report Generated:");
        Console.WriteLine($"   Strategy: {report.StrategyName}");
        Console.WriteLine($"   Total Return: {report.TotalReturn:P2}");
        Console.WriteLine($"   Sharpe Ratio: {report.SharpeRatio:F3}");
        Console.WriteLine($"   Max Drawdown: {report.MaxDrawdown:P2}");
        
        // Test advanced metrics
        Console.WriteLine($"   Advanced Metrics ({report.AdvancedMetrics.Count}):");
        foreach (var metric in report.AdvancedMetrics.Take(3))
        {
            Console.WriteLine($"     {metric.Name}: {metric.Value:F4}");
        }
        
        // Test risk metrics
        Console.WriteLine($"   Risk Metrics:");
        Console.WriteLine($"     VaR (95%): {report.RiskMetrics.ValueAtRisk95:P2}");
        Console.WriteLine($"     Volatility: {report.RiskMetrics.AnnualizedVolatility:P2}");
        Console.WriteLine($"     Sortino Ratio: {report.RiskMetrics.SortinoRatio:F3}");
        
        // Test monthly performance
        Console.WriteLine($"   Monthly Performance ({report.MonthlyPerformance.Count} months):");
        foreach (var monthly in report.MonthlyPerformance.Take(3))
        {
            Console.WriteLine($"     {monthly.Month:yyyy-MM}: {monthly.Return:P2} ({monthly.TradeCount} trades)");
        }
        
        // Test detailed report generation
        var detailedReport = performanceAnalytics.GenerateDetailedReport(result);
        Console.WriteLine($"   Detailed report generated ({detailedReport.Length} characters)");
        
        Console.WriteLine("   ✅ Performance Analytics test completed\n");
    }

    private async Task TestParameterOptimization()
    {
        Console.WriteLine("4. Testing Parameter Optimization...");
        
        var backtestingEngine = _serviceProvider.GetRequiredService<IBacktestingEngine>();
        
        var optimizationConfig = new ParameterOptimizationConfig
        {
            StrategyName = "Optimization_Test",
            StartDate = DateTime.Today.AddDays(-30),
            EndDate = DateTime.Today.AddDays(-1),
            InitialCapital = 10000m,
            Symbols = new List<string> { "SPY" },
            ParameterRanges = new Dictionary<string, List<object>>
            {
                ["ProfitTarget"] = new List<object> { 0.3m, 0.5m, 0.7m },
                ["StopLoss"] = new List<object> { 0.6m, 0.8m, 1.0m }
            },
            OptimizationMetric = "sharpe"
        };
        
        var optimizationResults = await backtestingEngine.RunParameterOptimizationAsync(optimizationConfig);
        
        Console.WriteLine($"   Parameter Optimization Results:");
        Console.WriteLine($"   Total combinations tested: {optimizationResults.Count}");
        
        var bestResult = optimizationResults.First();
        Console.WriteLine($"   Best Result:");
        Console.WriteLine($"     Parameters: {string.Join(", ", bestResult.Parameters.Select(p => $"{p.Key}={p.Value}"))}");
        Console.WriteLine($"     Total Return: {bestResult.TotalReturn:P2}");
        Console.WriteLine($"     Sharpe Ratio: {bestResult.SharpeRatio:F3}");
        Console.WriteLine($"     Win Rate: {bestResult.WinRate:P1}");
        
        Console.WriteLine($"   Top 3 Results:");
        foreach (var result in optimizationResults.Take(3))
        {
            var paramStr = string.Join(", ", result.Parameters.Select(p => $"{p.Key}={p.Value}"));
            Console.WriteLine($"     {paramStr} -> Return: {result.TotalReturn:P2}, Sharpe: {result.SharpeRatio:F3}");
        }
        
        Console.WriteLine("   ✅ Parameter Optimization test completed\n");
    }

    public static async Task RunBacktestingFrameworkTest()
    {
        var test = new BacktestingFrameworkTest();
        await test.TestBacktestingFramework();
    }
}
