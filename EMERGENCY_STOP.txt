# EMERGENCY STOP CONTROLS

## To IMMEDIATELY stop all trading:
1. Create a file named "STOP_TRADING.flag" in this directory
2. The system will detect this file and halt all new trades
3. Existing positions will be managed according to normal rules

## To resume trading:
1. Delete the "STOP_TRADING.flag" file
2. The system will resume normal operations

## Emergency Commands:
- Ctrl+C in the terminal to stop the application
- Check logs in the console for all trading activity
- Monitor account at: https://app.alpaca.markets

## Today's Safety Settings:
- Max Position Size: $1,000 (down from $10,000)
- Max Daily Loss: $100 (down from $500)
- Max Positions: 2 (down from 12)
- Max Daily Trades: 3 (down from 8)
- Risk Per Trade: 0.8% (down from 2%)

## Account Status:
- Account: *********
- Current Equity: $12,035.00
- Risk Level: CONSERVATIVE (8.3% max position size)

## Monitoring:
- Watch console output for all trades
- Each trade will be logged with full details
- Risk checks will be logged before each trade
