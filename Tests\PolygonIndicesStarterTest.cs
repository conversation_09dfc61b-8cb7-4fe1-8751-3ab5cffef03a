using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using ZeroDateStrat.Services;

namespace ZeroDateStrat.Tests;

/// <summary>
/// Comprehensive test for Polygon Indices Starter subscription
/// Tests new indices endpoints and enhanced VIX/SPX data access
/// </summary>
public static class PolygonIndicesStarterTest
{
    public static async Task RunPolygonIndicesStarterTest()
    {
        var logger = LoggerFactory.Create(builder => builder.AddConsole()).CreateLogger<Program>();
        
        try
        {
            logger.LogInformation("=== Polygon Indices Starter Subscription Test ===");
            logger.LogInformation("Testing enhanced indices data access with new subscription");

            // Build configuration
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false)
                .Build();

            // Check if Polygon API key is configured
            var polygonApiKey = configuration["Polygon:ApiKey"];
            if (string.IsNullOrEmpty(polygonApiKey) || polygonApiKey == "YOUR_POLYGON_API_KEY_HERE")
            {
                logger.LogError("❌ Polygon API key not configured");
                return;
            }

            // Build service provider
            var services = new ServiceCollection();
            services.AddSingleton<IConfiguration>(configuration);
            services.AddLogging(builder => builder.AddConsole());
            services.AddHttpClient();
            services.AddSingleton<ISecurityService, SecurityService>();
            services.AddSingleton<IPolygonDataService, PolygonDataService>();

            var serviceProvider = services.BuildServiceProvider();
            var polygonService = serviceProvider.GetRequiredService<IPolygonDataService>();

            // Test connection first
            logger.LogInformation("\n1. Testing Polygon connection...");
            var connectionTest = await polygonService.TestConnectionAsync();
            
            if (!connectionTest)
            {
                logger.LogError("❌ Polygon connection failed - cannot proceed with tests");
                return;
            }
            logger.LogInformation("✅ Polygon connection successful");

            // Test 2: Index Snapshots
            logger.LogInformation("\n2. Testing Index Snapshots (NEW - Indices Starter Feature)...");
            await TestIndexSnapshots(polygonService, logger);

            // Test 3: Index Aggregates
            logger.LogInformation("\n3. Testing Index Aggregates (OHLC Data)...");
            await TestIndexAggregates(polygonService, logger);

            // Test 4: Enhanced VIX Data Access
            logger.LogInformation("\n4. Testing Enhanced VIX Data Access...");
            await TestEnhancedVixAccess(polygonService, logger);

            // Test 5: Enhanced SPX Data Access
            logger.LogInformation("\n5. Testing Enhanced SPX Data Access...");
            await TestEnhancedSpxAccess(polygonService, logger);

            // Test 6: Index vs Tradeable Security Handling
            logger.LogInformation("\n6. Testing Index vs Tradeable Security Handling...");
            await TestIndexVsTradeableHandling(polygonService, logger);

            logger.LogInformation("\n=== Test Complete ===");
            logger.LogInformation("✅ Indices Starter subscription test completed successfully");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "❌ Test failed with exception");
        }
    }

    private static async Task TestIndexSnapshots(IPolygonDataService polygonService, ILogger logger)
    {
        var indexSymbols = new[] { "I:SPX", "I:VIX", "I:NDX", "I:RUT", "I:DJI" };

        foreach (var symbol in indexSymbols)
        {
            try
            {
                logger.LogInformation($"Testing index snapshot: {symbol}");
                var snapshot = await polygonService.GetIndexSnapshotAsync(symbol);
                
                if (snapshot != null && snapshot.Value > 0)
                {
                    logger.LogInformation($"✅ {symbol} - Value: {snapshot.Value:F2}, Change: {snapshot.Change:F2} ({snapshot.ChangePercent:F2}%)");
                    logger.LogInformation($"   OHLC: O={snapshot.Open:F2}, H={snapshot.High:F2}, L={snapshot.Low:F2}, C={snapshot.Close:F2}");
                }
                else
                {
                    logger.LogWarning($"⚠️ {symbol} - No snapshot data returned");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"❌ {symbol} - Error: {ex.Message}");
            }
        }
    }

    private static async Task TestIndexAggregates(IPolygonDataService polygonService, ILogger logger)
    {
        var indexSymbols = new[] { "I:SPX", "I:VIX", "I:NDX" };
        var fromDate = DateTime.Today.AddDays(-5);
        var toDate = DateTime.Today;

        foreach (var symbol in indexSymbols)
        {
            try
            {
                logger.LogInformation($"Testing index aggregates: {symbol} ({fromDate:yyyy-MM-dd} to {toDate:yyyy-MM-dd})");
                var aggregates = await polygonService.GetIndexAggregatesAsync(symbol, fromDate, toDate, "day");
                
                if (aggregates != null && aggregates.Results.Any())
                {
                    logger.LogInformation($"✅ {symbol} - Retrieved {aggregates.ResultsCount} daily bars");
                    var latestBar = aggregates.Results.OrderByDescending(r => r.Timestamp).First();
                    logger.LogInformation($"   Latest: {latestBar.Date:yyyy-MM-dd} OHLC: {latestBar.Open:F2}/{latestBar.High:F2}/{latestBar.Low:F2}/{latestBar.Close:F2}");
                }
                else
                {
                    logger.LogWarning($"⚠️ {symbol} - No aggregates data returned");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"❌ {symbol} - Error: {ex.Message}");
            }
        }
    }

    private static async Task TestEnhancedVixAccess(IPolygonDataService polygonService, ILogger logger)
    {
        try
        {
            // Test current VIX (should use new indices snapshot)
            logger.LogInformation("Testing enhanced VIX current value...");
            var currentVix = await polygonService.GetCurrentVixAsync();
            logger.LogInformation($"Current VIX: {currentVix:F2}");

            // Test VIX history (should use new indices aggregates)
            logger.LogInformation("Testing enhanced VIX historical data...");
            var fromDate = DateTime.Today.AddDays(-7);
            var toDate = DateTime.Today;
            var vixHistory = await polygonService.GetVixHistoryAsync(fromDate, toDate);
            
            if (vixHistory.Any())
            {
                logger.LogInformation($"✅ VIX History - Retrieved {vixHistory.Count} data points");
                var latest = vixHistory.OrderByDescending(v => v.Date).First();
                logger.LogInformation($"   Latest: {latest.Date:yyyy-MM-dd} Value: {latest.Value:F2} (H: {latest.High:F2}, L: {latest.Low:F2})");
            }
            else
            {
                logger.LogWarning("⚠️ No VIX historical data returned");
            }

            // Test VIX change calculation
            var vixChange = await polygonService.GetVixChangeAsync(TimeSpan.FromDays(1));
            logger.LogInformation($"VIX 1-day change: {vixChange:F2}");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "❌ Error testing enhanced VIX access");
        }
    }

    private static async Task TestEnhancedSpxAccess(IPolygonDataService polygonService, ILogger logger)
    {
        try
        {
            // Test SPX quote (should use enhanced index handling)
            logger.LogInformation("Testing enhanced SPX quote access...");
            var spxQuote = await polygonService.GetCurrentQuoteAsync("I:SPX");
            
            if (spxQuote != null && spxQuote.Bid > 0)
            {
                logger.LogInformation($"✅ SPX Quote - Price: {spxQuote.Bid:F2} (index value)");
                logger.LogInformation($"   Timestamp: {spxQuote.Timestamp:yyyy-MM-dd HH:mm:ss}");
            }
            else
            {
                logger.LogWarning("⚠️ No SPX quote data returned");
            }

            // Test SPX snapshot
            logger.LogInformation("Testing SPX snapshot...");
            var spxSnapshot = await polygonService.GetIndexSnapshotAsync("I:SPX");
            
            if (spxSnapshot != null && spxSnapshot.Value > 0)
            {
                logger.LogInformation($"✅ SPX Snapshot - Value: {spxSnapshot.Value:F2}");
                logger.LogInformation($"   Change: {spxSnapshot.Change:F2} ({spxSnapshot.ChangePercent:F2}%)");
            }
            else
            {
                logger.LogWarning("⚠️ No SPX snapshot data returned");
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "❌ Error testing enhanced SPX access");
        }
    }

    private static async Task TestIndexVsTradeableHandling(IPolygonDataService polygonService, ILogger logger)
    {
        try
        {
            // Test index symbols (should use index-specific endpoints)
            logger.LogInformation("Testing index symbol handling...");
            var indexSymbols = new[] { "I:SPX", "I:VIX", "I:NDX" };
            
            foreach (var symbol in indexSymbols)
            {
                var quote = await polygonService.GetCurrentQuoteAsync(symbol);
                if (quote != null && quote.Bid > 0)
                {
                    if (quote.Bid == quote.Ask)
                    {
                        logger.LogInformation($"✅ {symbol} - Correctly handled as index: {quote.Bid:F2} (last value)");
                    }
                    else
                    {
                        logger.LogWarning($"⚠️ {symbol} - Unexpected bid/ask difference: {quote.Bid:F2}/{quote.Ask:F2}");
                    }
                }
            }

            // Test tradeable symbols (should use NBBO endpoints)
            logger.LogInformation("Testing tradeable symbol handling...");
            var tradeableSymbols = new[] { "SPY", "QQQ", "IWM" };
            
            foreach (var symbol in tradeableSymbols)
            {
                var quote = await polygonService.GetCurrentQuoteAsync(symbol);
                if (quote != null && quote.Bid > 0 && quote.Ask > 0)
                {
                    var spread = quote.Ask - quote.Bid;
                    if (spread > 0)
                    {
                        logger.LogInformation($"✅ {symbol} - Real bid/ask: {quote.Bid:F2}/{quote.Ask:F2} (spread: {spread:F3})");
                    }
                    else
                    {
                        logger.LogWarning($"⚠️ {symbol} - Zero spread: {quote.Bid:F2}/{quote.Ask:F2}");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "❌ Error testing index vs tradeable handling");
        }
    }
}
