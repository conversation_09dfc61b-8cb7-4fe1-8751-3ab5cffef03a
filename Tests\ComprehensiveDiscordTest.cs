using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using ZeroDateStrat.Services;

namespace ZeroDateStrat.Tests;

public static class ComprehensiveDiscordTest
{
    public static async Task RunAllDiscordTestsAsync()
    {
        Console.WriteLine("🧪 Starting Comprehensive Discord Message Test Suite...");
        Console.WriteLine("This test will demonstrate ALL Discord messaging functionality in the ZeroDateStrat system.\n");

        try
        {
            // Build configuration
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false)
                .AddJsonFile("appsettings.Development.json", optional: true)
                .AddEnvironmentVariables()
                .Build();

            // Setup services
            var services = new ServiceCollection();
            services.AddSingleton<IConfiguration>(configuration);
            services.AddLogging(builder =>
            {
                builder.AddConsole();
                builder.SetMinimumLevel(LogLevel.Information);
            });

            // Add Discord service
            services.AddSingleton<IDiscordService, DiscordService>();

            var serviceProvider = services.BuildServiceProvider();
            var loggerFactory = serviceProvider.GetRequiredService<ILoggerFactory>();
            var logger = loggerFactory.CreateLogger("ComprehensiveDiscordTest");
            var discordService = serviceProvider.GetRequiredService<IDiscordService>();

            // Start Discord service
            logger.LogInformation("🚀 Starting Discord service...");
            await discordService.StartAsync();

            // Wait for connection
            await Task.Delay(3000);

            // Check if Discord is connected
            var isConnected = await discordService.IsConnectedAsync();
            if (!isConnected)
            {
                logger.LogWarning("❌ Discord not connected, cannot run tests");
                return;
            }

            logger.LogInformation("✅ Discord connected successfully! Starting comprehensive message tests...\n");

            // Send test introduction
            await discordService.SendMessageAsync("🧪 **DISCORD MESSAGE TEST SUITE**\n\n" +
                                                 "Testing core Discord messaging functionality in the ZeroDateStrat trading system.\n" +
                                                 "The following messages will demonstrate:\n\n" +
                                                 "• Basic Messages\n" +
                                                 "• Trading Error Notifications (All Severities)\n\n" +
                                                 "**Test starting in 3 seconds...**");
            await Task.Delay(3000);

            // Test 1: Basic Message
            await TestBasicMessage(discordService, logger);
            await Task.Delay(2000);

            // Test 2: Trading Error Notifications (the main functionality we implemented)
            await TestTradingErrorNotifications(discordService, logger);
            await Task.Delay(2000);

            // Send test completion summary
            await discordService.SendMessageAsync("✅ **DISCORD MESSAGE TEST COMPLETED!**\n\n" +
                                                 "Core Discord messaging functionality has been successfully tested:\n\n" +
                                                 "✅ Basic Messages - Working\n" +
                                                 "✅ Trading Error Notifications (All Severities) - Working\n\n" +
                                                 "🎯 **Discord messaging system is operational and ready for live trading!**\n\n" +
                                                 "*Note: Additional message types (portfolio summaries, trade notifications, etc.) " +
                                                 "can be tested individually when the full trading system is running.*");

            logger.LogInformation("✅ All Discord message tests completed successfully!");

            // Stop Discord service
            await discordService.StopAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Comprehensive Discord test failed: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
    }

    private static async Task TestBasicMessage(IDiscordService discordService, ILogger logger)
    {
        logger.LogInformation("📧 Test 1: Testing basic message functionality...");
        
        await discordService.SendMessageAsync("📧 **Test 1: Basic Message**\n\n" +
                                             "This is a basic Discord message test. The system can send simple text messages " +
                                             "for general notifications, status updates, and system communications.\n\n" +
                                             "✅ Basic messaging functionality is working correctly!");
    }



    private static async Task TestTradingErrorNotifications(IDiscordService discordService, ILogger logger)
    {
        logger.LogInformation("🚨 Test 8: Testing trading error notifications...");

        await discordService.SendMessageAsync("🚨 **Test 8: Trading Error Notifications**\n\n" +
                                             "Testing all severity levels of trading error notifications:");

        // Test different error severities
        var lowSeverityError = new TimeoutException("Network timeout during market data retrieval");
        await discordService.SendTradingErrorAsync(lowSeverityError, "Market Data Retrieval", "Low");
        await Task.Delay(1500);

        var mediumSeverityError = new InvalidOperationException("Order validation failed - insufficient buying power");
        await discordService.SendTradingErrorAsync(mediumSeverityError, "Order Placement - Iron Condor for SPY", "Medium");
        await Task.Delay(1500);

        var highSeverityError = new UnauthorizedAccessException("Alpaca API authentication failed");
        await discordService.SendTradingErrorAsync(highSeverityError, "API Authentication", "High");
        await Task.Delay(1500);

        var criticalError = new SystemException("Trading system database connection lost");
        await discordService.SendTradingErrorAsync(criticalError, "System Infrastructure", "Critical");
    }
}
