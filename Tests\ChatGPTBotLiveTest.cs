using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Serilog;
using ZeroDateStrat.Services;

namespace ZeroDateStrat.Tests;

/// <summary>
/// Live test that actually connects the ChatGPT Discord bot to Discord
/// </summary>
public static class ChatGPTBotLiveTest
{
    public static async Task RunAsync()
    {
        Console.WriteLine("🤖 Starting ChatGPT Discord Bot Live Test...\n");

        // Configure Serilog for testing
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Information()
            .WriteTo.Console()
            .CreateLogger();

        try
        {
            Console.WriteLine("Initializing ChatGPT Discord bot...");

            // Load configuration
            var configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json", optional: false)
                .AddEnvironmentVariables()
                .Build();

            // Setup DI container with minimal services needed for ChatGPT bot
            var services = new ServiceCollection();
            services.AddSingleton<IConfiguration>(configuration);
            services.AddLogging(builder => builder.AddSerilog());
            services.AddHttpClient<IOpenAIService, OpenAIService>();
            services.AddSingleton<IChatGPTDiscordBot, ChatGPTDiscordBot>();

            var serviceProvider = services.BuildServiceProvider();

            Console.WriteLine("✅ Services initialized successfully\n");

            // Get the ChatGPT Discord bot
            var chatGPTBot = serviceProvider.GetRequiredService<IChatGPTDiscordBot>();

            Console.WriteLine("🔗 Connecting ChatGPT bot to Discord...");
            
            // Start the bot
            await chatGPTBot.StartAsync();

            // Wait a moment for connection
            await Task.Delay(3000);

            // Check if connected
            var isConnected = await chatGPTBot.IsConnectedAsync();
            
            if (isConnected)
            {
                Console.WriteLine("✅ ChatGPT Discord bot connected successfully!");
                Console.WriteLine("\n🎉 Bot is now live and ready to respond!");
                Console.WriteLine("\n💬 Test the bot in Discord with these commands:");
                Console.WriteLine("   @ChatGptBot Hello! Can you help me with C# trading code?");
                Console.WriteLine("   !askchatgpt What is 0 DTE options trading?");
                Console.WriteLine("   !chatgpt [urgent] How do I calculate option Greeks?");
                Console.WriteLine("\n⏰ Bot will run for 2 minutes for testing...");
                
                // Keep the bot running for 2 minutes for testing
                await Task.Delay(TimeSpan.FromMinutes(2));
                
                Console.WriteLine("\n⏹️ Stopping ChatGPT Discord bot...");
                await chatGPTBot.StopAsync();
                Console.WriteLine("✅ ChatGPT Discord bot stopped successfully");
            }
            else
            {
                Console.WriteLine("❌ Failed to connect ChatGPT Discord bot");
                Console.WriteLine("Please check:");
                Console.WriteLine("1. Bot token is correct");
                Console.WriteLine("2. Bot has been invited to the Discord server");
                Console.WriteLine("3. Bot has proper permissions (Send Messages, Read Message History)");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Test failed with error: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
        finally
        {
            Log.CloseAndFlush();
        }
    }
}
