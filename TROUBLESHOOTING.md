# ZeroDateStrat - Troubleshooting Guide

## Common Issues & Solutions

### 🔌 Connection Issues

#### Problem: "Failed to initialize Alpaca service"
**Symptoms**: Application fails to start, connection errors in logs
**Causes**: Invalid credentials, network issues, API endpoint problems

**Solutions**:
1. **Verify Credentials**
   ```bash
   # Check appsettings.json
   "ApiKey": "AKR6SLIKSB0NCBL2CNLB"  # Should start with "AK"
   "SecretKey": "mgRw02d5XNabcUgopVmb22fDoCEVLsjs7QswywJz"  # 40 characters
   ```

2. **Test Network Connectivity**
   ```bash
   ping api.alpaca.markets
   curl -I https://api.alpaca.markets
   ```

3. **Check API Status**
   - Visit [Alpaca Status Page](https://status.alpaca.markets/)
   - Verify API endpoints are operational

4. **Validate Environment**
   ```json
   // For live trading
   "BaseUrl": "https://api.alpaca.markets"
   
   // For paper trading
   "BaseUrl": "https://paper-api.alpaca.markets"
   ```

#### Problem: "Invalid API key format"
**Symptoms**: Security validation fails, authentication errors
**Cause**: Malformed API credentials

**Solution**:
```csharp
// Alpaca API keys must:
// - Start with "AK"
// - Be exactly 20 characters
// - Contain only alphanumeric characters

// Alpaca secret keys must:
// - Be exactly 40 characters
// - Contain only alphanumeric characters
```

### 💰 Trading Issues

#### Problem: "No trading signals generated"
**Symptoms**: Application runs but no trades executed
**Causes**: Market conditions, timing, configuration

**Diagnostic Steps**:
1. **Check Market Hours**
   ```
   Trading Hours: 9:30 AM - 4:00 PM ET
   Entry Window: 9:45 AM - 10:30 AM ET (configurable)
   ```

2. **Verify Market Day**
   - No trading on weekends
   - No trading on market holidays
   - Check if market is open

3. **Check Strategy Configuration**
   ```json
   "Strategies": {
     "PutCreditSpread": {
       "Enabled": true,  // Must be true
       "MinDelta": 0.05,
       "MaxDelta": 0.15,
       "MinPremium": 0.10
     }
   }
   ```

4. **Review Risk Limits**
   ```json
   "Trading": {
     "MaxPositionsPerDay": 5,
     "MaxDailyLoss": 500,
     "MinAccountEquity": 2000
   }
   ```

#### Problem: "Trade rejected by risk manager"
**Symptoms**: Signals generated but not executed
**Cause**: Risk validation failures

**Common Risk Rejections**:
1. **Insufficient Buying Power**
   ```
   Solution: Reduce position size or increase account equity
   ```

2. **Daily Loss Limit Exceeded**
   ```
   Current Loss: $450
   Daily Limit: $500
   Remaining: $50
   ```

3. **Position Concentration**
   ```
   Max positions per symbol: 4
   Current SPY positions: 4
   Action: Wait for position closure or increase limit
   ```

4. **Account Equity Below Minimum**
   ```
   Current Equity: $1,800
   Minimum Required: $2,000
   Action: Add funds or reduce minimum
   ```

### 🔒 Security Issues

#### Problem: "Security audit indicates potential issues"
**Symptoms**: Security warnings in logs, low security score
**Cause**: Unencrypted credentials, insecure configuration

**Solutions**:
1. **Encrypt Credentials**
   ```json
   // Instead of plain text:
   "ApiKey": "AKR6SLIKSB0NCBL2CNLB"
   
   // Use encrypted format:
   "ApiKey": "ENC:base64encryptedstring"
   ```

2. **Use Environment Variables**
   ```bash
   set ALPACA_API_KEY=your_api_key
   set ALPACA_SECRET_KEY=your_secret_key
   ```

3. **Secure Configuration**
   ```json
   // Move sensitive data to appsettings.production.json
   // Add to .gitignore
   ```

#### Problem: "Running in insecure environment"
**Symptoms**: Environment security warnings
**Cause**: Development environment detection

**Solution**:
```json
// Set production environment
"Environment": "Production"

// Or use environment variable
set ASPNETCORE_ENVIRONMENT=Production
```

### 📊 Performance Issues

#### Problem: "High CPU/Memory usage"
**Symptoms**: System performance degradation, slow response
**Causes**: Memory leaks, excessive logging, inefficient operations

**Diagnostic Steps**:
1. **Check System Resources**
   ```
   CPU Usage: Should be < 50%
   Memory Usage: Should be < 80%
   ```

2. **Review Log Levels**
   ```json
   "Logging": {
     "LogLevel": {
       "Default": "Information",  // Change from "Debug"
       "ZeroDateStrat": "Information"
     }
   }
   ```

3. **Monitor Memory Usage**
   ```csharp
   // Check for memory leaks in position tracking
   // Verify proper disposal of HTTP clients
   // Monitor collection sizes
   ```

#### Problem: "Slow order execution"
**Symptoms**: Delayed trade execution, missed opportunities
**Causes**: Network latency, API throttling, inefficient code

**Solutions**:
1. **Optimize Network**
   - Use wired connection
   - Minimize network hops
   - Consider VPS near exchange

2. **Check API Rate Limits**
   ```
   Alpaca Rate Limits:
   - 200 requests per minute
   - Burst allowance available
   ```

3. **Parallel Processing**
   ```csharp
   // Execute multiple orders concurrently
   var tasks = signals.Select(signal => 
       strategy.ExecuteSignalAsync(signal));
   await Task.WhenAll(tasks);
   ```

### 🚨 Error Recovery

#### Problem: "Circuit breaker activated"
**Symptoms**: Service temporarily unavailable, automatic recovery
**Cause**: Repeated failures triggering protection

**Circuit Breaker Status**:
```
AlpacaAPI: 5 failures → 5-minute timeout
OptionsData: 3 failures → 3-minute timeout
MarketData: 3 failures → 2-minute timeout
```

**Recovery Actions**:
1. **Wait for Automatic Recovery**
   - Circuit breakers reset automatically
   - Monitor logs for recovery messages

2. **Manual Reset** (if needed)
   ```csharp
   await productionInfrastructure.ResetCircuitBreakerAsync("AlpacaAPI");
   ```

3. **Investigate Root Cause**
   - Check API status
   - Review error logs
   - Verify network connectivity

#### Problem: "Application crash/unexpected termination"
**Symptoms**: Application stops, exception logs
**Cause**: Unhandled exceptions, system issues

**Recovery Steps**:
1. **Check Exception Logs**
   ```
   Location: logs/zeroDteStrat-{date}.log
   Look for: FATAL, ERROR level messages
   ```

2. **Review Stack Traces**
   ```
   Identify: Method causing failure
   Context: Operation being performed
   ```

3. **Restart with Monitoring**
   ```bash
   dotnet run
   # Monitor logs closely for recurring issues
   ```

### 📈 Data Issues

#### Problem: "Missing options data"
**Symptoms**: No options chains, pricing errors
**Cause**: Data feed issues, symbol problems

**Solutions**:
1. **Verify Symbol Availability**
   ```
   SPX: Available for 0 DTE
   SPY: Available for 0 DTE
   Check: Alpaca supported symbols
   ```

2. **Check Market Hours**
   ```
   Options data available: 9:30 AM - 4:00 PM ET
   Pre/post market: Limited data
   ```

3. **Fallback Symbols**
   ```json
   "Trading": {
     "PrimarySymbol": "SPX",
     "BackupSymbol": "SPY"  // Automatic fallback
   }
   ```

### 🔧 Configuration Issues

#### Problem: "Configuration validation failed"
**Symptoms**: Startup errors, validation warnings
**Cause**: Invalid configuration values

**Common Validation Errors**:
1. **Invalid Time Formats**
   ```json
   // Correct format:
   "EntryTimeStart": "09:45:00",
   "EntryTimeEnd": "10:30:00"
   ```

2. **Invalid Percentages**
   ```json
   // Must be decimal (0.02 = 2%)
   "RiskPerTrade": 0.02,
   "ProfitTargetPercent": 0.5
   ```

3. **Conflicting Values**
   ```json
   // Risk/reward must be logical
   "RiskRewardThreshold": 0.15,  // Must be < ProfitTarget
   "ProfitTargetPercent": 0.5
   ```

### 📱 Notification Issues

#### Problem: "Alerts not being sent"
**Symptoms**: No email/SMS notifications
**Cause**: Configuration issues, service problems

**Email Configuration**:
```json
"Email": {
  "Enabled": true,
  "SmtpServer": "smtp.gmail.com",
  "SmtpPort": 587,
  "Username": "<EMAIL>",
  "Password": "app-password",  // Use app password for Gmail
  "ToAddress": "<EMAIL>",
  "UseSsl": true
}
```

**SMS Configuration**:
```json
"SMS": {
  "Enabled": true,
  "Provider": "Twilio",
  "AccountSid": "your-twilio-sid",
  "AuthToken": "your-twilio-token",
  "FromNumber": "+**********",
  "ToNumber": "+**********"
}
```

## Diagnostic Commands

### System Health Check
```bash
# Run Phase 3 demo to test all systems
dotnet run phase3

# Check specific components
dotnet run --test-alpaca
dotnet run --test-risk
dotnet run --test-security
```

### Log Analysis
```bash
# View recent logs
tail -f logs/zeroDteStrat-$(date +%Y%m%d).log

# Search for errors
grep -i "error\|exception\|failed" logs/*.log

# Monitor real-time
dotnet run | grep -E "(ERROR|WARN|FATAL)"
```

### Configuration Validation
```bash
# Validate configuration
dotnet run --validate-config

# Test security
dotnet run --security-audit

# Check risk parameters
dotnet run --risk-check
```

## Emergency Procedures

### 🚨 Emergency Stop
```bash
# Graceful shutdown
Ctrl+C

# Force stop if needed
taskkill /f /im dotnet.exe
```

### 💰 Position Emergency
```csharp
// Close all positions immediately
await strategy.CloseAllPositionsAsync();

// Cancel all pending orders
await alpacaService.CancelAllOrdersAsync();
```

### 🔒 Security Breach
1. **Immediately revoke API keys** in Alpaca dashboard
2. **Stop application** completely
3. **Review logs** for unauthorized access
4. **Generate new credentials**
5. **Update configuration** with new keys

## Support Resources

### Logs Location
```
Windows: logs/zeroDteStrat-{date}.log
Linux: ./logs/zeroDteStrat-{date}.log
```

### Configuration Files
```
Primary: appsettings.json
Local: appsettings.local.json (if exists)
Production: appsettings.production.json (if exists)
```

### Key Metrics to Monitor
- Account equity and buying power
- Daily P&L and position count
- Error rates and exception frequency
- System resource usage
- API response times

---

*Troubleshooting Guide Last Updated: December 2024*
*For additional support, review logs and system documentation*
