using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using ZeroDateStrat.Services;
using ZeroDateStrat.Models;
using ILogger = Serilog.ILogger;

namespace ZeroDateStrat.Tests;

public static class OptionsChainIntegrationTest
{
    public static async Task RunOptionsChainIntegrationTest()
    {
        // Configure Serilog for testing
        Log.Logger = new LoggerConfiguration()
            .WriteTo.Console()
            .WriteTo.File($"logs/options-chain-test-{DateTime.Now:yyyyMMdd-HHmmss}.txt")
            .CreateLogger();

        var logger = Log.Logger;

        try
        {
            logger.Information("🔗 Starting Options Chain Integration Test");
            logger.Information("Testing the new Polygon.io-based options chain retrieval system");

            // Build host with all services
            var host = Host.CreateDefaultBuilder()
                .UseSerilog()
                .ConfigureAppConfiguration((context, config) =>
                {
                    config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
                    config.AddEnvironmentVariables();
                })
                .ConfigureServices((context, services) =>
                {
                    // Core services needed for options chain
                    services.AddSingleton<IPolygonDataService, PolygonDataService>();
                    services.AddSingleton<IOptionsPricingService, OptionsPricingService>();
                    services.AddSingleton<IOptionsChainService, OptionsChainService>();
                    services.AddSingleton<IAlpacaService, AlpacaService>();
                    services.AddSingleton<ISecurityService, SecurityService>();
                    services.AddSingleton<IGlobalExceptionHandler, GlobalExceptionHandler>();
                    
                    services.AddHttpClient();
                    services.AddLogging(builder => builder.AddSerilog());
                })
                .Build();

            // Get services
            var polygonService = host.Services.GetRequiredService<IPolygonDataService>();
            var pricingService = host.Services.GetRequiredService<IOptionsPricingService>();
            var optionsChainService = host.Services.GetRequiredService<IOptionsChainService>();
            var alpacaService = host.Services.GetRequiredService<IAlpacaService>();

            // Initialize Alpaca service
            logger.Information("Initializing Alpaca service...");
            var alpacaInitialized = await alpacaService.InitializeAsync();
            if (!alpacaInitialized)
            {
                logger.Warning("Alpaca service initialization failed, continuing with limited functionality");
            }

            // Test 1: Polygon.io Options Contracts Retrieval
            logger.Information("\n📊 Test 1: Polygon.io Options Contracts Retrieval");
            await TestPolygonOptionsContracts(polygonService, logger);

            // Test 2: Options Pricing Service
            logger.Information("\n💰 Test 2: Options Pricing Service");
            await TestOptionsPricingService(pricingService, logger);

            // Test 3: Options Chain Service
            logger.Information("\n🔗 Test 3: Options Chain Service");
            await TestOptionsChainService(optionsChainService, logger);

            // Test 4: Enhanced AlpacaService Integration
            logger.Information("\n🚀 Test 4: Enhanced AlpacaService Integration");
            await TestEnhancedAlpacaService(alpacaService, logger);

            // Test 5: 0 DTE Options Detection
            logger.Information("\n⏰ Test 5: 0 DTE Options Detection");
            await TestZeroDteOptionsDetection(optionsChainService, logger);

            // Test 6: Performance and Caching
            logger.Information("\n⚡ Test 6: Performance and Caching");
            await TestPerformanceAndCaching(optionsChainService, logger);

            logger.Information("\n✅ Options Chain Integration Test Completed!");
            logger.Information("📊 All components are working together successfully");

        }
        catch (Exception ex)
        {
            logger.Error(ex, "❌ Error during options chain integration test");
        }
        finally
        {
            Log.CloseAndFlush();
        }
    }

    private static async Task TestPolygonOptionsContracts(IPolygonDataService polygonService, ILogger logger)
    {
        try
        {
            logger.Information("Testing Polygon.io options contracts retrieval...");

            // Test SPY options contracts
            var spyContracts = await polygonService.GetOptionsContractsAsync("SPY");
            logger.Information($"Retrieved {spyContracts.Count} SPY options contracts");

            if (spyContracts.Any())
            {
                var sampleContract = spyContracts.First();
                logger.Information($"Sample contract: {sampleContract.Ticker}");
                logger.Information($"  Underlying: {sampleContract.UnderlyingTicker}");
                logger.Information($"  Strike: ${sampleContract.StrikePrice}");
                logger.Information($"  Type: {sampleContract.ContractType}");
                logger.Information($"  Expiration: {sampleContract.ExpirationDate}");

                // Test filtering by expiration date
                var today = DateTime.Today;
                var todayContracts = await polygonService.GetOptionsContractsForDateAsync("SPY", today);
                logger.Information($"Found {todayContracts.Count} SPY contracts expiring today (0 DTE)");
            }

            // Test market status
            var isMarketOpen = await polygonService.IsMarketOpenAsync();
            logger.Information($"Market is currently: {(isMarketOpen ? "OPEN" : "CLOSED")}");

            logger.Information("✅ Polygon.io options contracts test completed");
        }
        catch (Exception ex)
        {
            logger.Error(ex, "❌ Error testing Polygon.io options contracts");
        }
    }

    private static async Task TestOptionsPricingService(IOptionsPricingService pricingService, ILogger logger)
    {
        try
        {
            logger.Information("Testing options pricing calculations...");

            // Test Black-Scholes pricing
            var underlyingPrice = 600m;
            var strikePrice = 605m;
            var timeToExpiry = 1m / 365.25m; // 1 day
            var riskFreeRate = 0.05m;
            var volatility = 0.20m;

            var callPrice = await pricingService.CalculateBlackScholesPrice(
                underlyingPrice, strikePrice, timeToExpiry, riskFreeRate, volatility, true);
            
            var putPrice = await pricingService.CalculateBlackScholesPrice(
                underlyingPrice, strikePrice, timeToExpiry, riskFreeRate, volatility, false);

            logger.Information($"Black-Scholes pricing test:");
            logger.Information($"  Underlying: ${underlyingPrice}");
            logger.Information($"  Strike: ${strikePrice}");
            logger.Information($"  Time to expiry: {(double)timeToExpiry * 365.25:F1} days");
            logger.Information($"  Volatility: {volatility:P1}");
            logger.Information($"  Call price: ${callPrice:F2}");
            logger.Information($"  Put price: ${putPrice:F2}");

            // Test Greeks calculation
            var testContract = new OptionContract
            {
                StrikePrice = strikePrice,
                ExpirationDate = DateTime.Today.AddDays(1),
                OptionType = OptionType.Call
            };

            var greeks = await pricingService.CalculateGreeksAsync(testContract, underlyingPrice, volatility, riskFreeRate);
            logger.Information($"Greeks calculation:");
            logger.Information($"  Delta: {greeks.Delta:F3}");
            logger.Information($"  Gamma: {greeks.Gamma:F4}");
            logger.Information($"  Theta: {greeks.Theta:F3}");
            logger.Information($"  Vega: {greeks.Vega:F3}");

            logger.Information("✅ Options pricing service test completed");
        }
        catch (Exception ex)
        {
            logger.Error(ex, "❌ Error testing options pricing service");
        }
    }

    private static async Task TestOptionsChainService(IOptionsChainService optionsChainService, ILogger logger)
    {
        try
        {
            logger.Information("Testing options chain service...");

            // Test building option chain for SPY
            var expirationDate = DateTime.Today.AddDays(1); // Tomorrow's expiration
            var underlyingPrice = 600m; // Use a reasonable SPY price
            var optionChain = await optionsChainService.GetOptionChainAsync("SPY", expirationDate, underlyingPrice);

            logger.Information($"Built option chain for SPY expiring {expirationDate:yyyy-MM-dd}:");
            logger.Information($"  Underlying price: ${optionChain.UnderlyingPrice:F2}");
            logger.Information($"  Total contracts: {optionChain.Options.Count}");
            logger.Information($"  Calls: {optionChain.Calls.Count}");
            logger.Information($"  Puts: {optionChain.Puts.Count}");
            logger.Information($"  Average IV: {optionChain.ImpliedVolatility:P1}");

            if (optionChain.Options.Any())
            {
                // Find ATM options
                var atmCall = optionChain.GetAtmCall();
                var atmPut = optionChain.GetAtmPut();

                if (atmCall != null)
                {
                    logger.Information($"ATM Call: {atmCall.Symbol}");
                    logger.Information($"  Strike: ${atmCall.StrikePrice}");
                    logger.Information($"  Bid/Ask: ${atmCall.Bid:F2}/${atmCall.Ask:F2}");
                    logger.Information($"  Delta: {atmCall.Delta:F3}");
                }

                if (atmPut != null)
                {
                    logger.Information($"ATM Put: {atmPut.Symbol}");
                    logger.Information($"  Strike: ${atmPut.StrikePrice}");
                    logger.Information($"  Bid/Ask: ${atmPut.Bid:F2}/${atmPut.Ask:F2}");
                    logger.Information($"  Delta: {atmPut.Delta:F3}");
                }
            }

            logger.Information("✅ Options chain service test completed");
        }
        catch (Exception ex)
        {
            logger.Error(ex, "❌ Error testing options chain service");
        }
    }

    private static async Task TestEnhancedAlpacaService(IAlpacaService alpacaService, ILogger logger)
    {
        try
        {
            logger.Information("Testing enhanced AlpacaService with options chain integration...");

            var expirationDate = DateTime.Today.AddDays(1);
            var optionContracts = await alpacaService.GetOptionChainAsync("SPY", expirationDate);

            logger.Information($"AlpacaService returned {optionContracts.Count} option contracts");

            if (optionContracts.Any())
            {
                var sampleCall = optionContracts.FirstOrDefault(c => c.OptionType == OptionType.Call);
                var samplePut = optionContracts.FirstOrDefault(c => c.OptionType == OptionType.Put);

                if (sampleCall != null)
                {
                    logger.Information($"Sample Call: {sampleCall.Symbol}");
                    logger.Information($"  Price: ${sampleCall.LastPrice:F2}");
                    logger.Information($"  Delta: {sampleCall.Delta:F3}");
                    logger.Information($"  Liquid: {sampleCall.IsLiquid}");
                }

                if (samplePut != null)
                {
                    logger.Information($"Sample Put: {samplePut.Symbol}");
                    logger.Information($"  Price: ${samplePut.LastPrice:F2}");
                    logger.Information($"  Delta: {samplePut.Delta:F3}");
                    logger.Information($"  Liquid: {samplePut.IsLiquid}");
                }
            }

            logger.Information("✅ Enhanced AlpacaService test completed");
        }
        catch (Exception ex)
        {
            logger.Error(ex, "❌ Error testing enhanced AlpacaService");
        }
    }

    private static async Task TestZeroDteOptionsDetection(IOptionsChainService optionsChainService, ILogger logger)
    {
        try
        {
            logger.Information("Testing 0 DTE options detection...");

            var symbols = new List<string> { "SPY", "QQQ", "IWM" };
            var underlyingPrices = new Dictionary<string, decimal>
            {
                { "SPY", 600m },
                { "QQQ", 520m },
                { "IWM", 240m }
            };
            var zeroDteChains = await optionsChainService.GetZeroDteOptionChainsAsync(symbols, underlyingPrices);

            logger.Information($"Found {zeroDteChains.Count} symbols with 0 DTE options:");

            foreach (var chain in zeroDteChains)
            {
                logger.Information($"  {chain.UnderlyingSymbol}: {chain.Options.Count} contracts");
                logger.Information($"    Underlying: ${chain.UnderlyingPrice:F2}");
                logger.Information($"    Calls: {chain.Calls.Count}, Puts: {chain.Puts.Count}");
            }

            logger.Information("✅ 0 DTE options detection test completed");
        }
        catch (Exception ex)
        {
            logger.Error(ex, "❌ Error testing 0 DTE options detection");
        }
    }

    private static async Task TestPerformanceAndCaching(IOptionsChainService optionsChainService, ILogger logger)
    {
        try
        {
            logger.Information("Testing performance and caching...");

            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            // First call - should be slower (no cache)
            var testPrice = 600m;
            var chain1 = await optionsChainService.GetOptionChainAsync("SPY", DateTime.Today.AddDays(1), testPrice);
            var firstCallTime = stopwatch.ElapsedMilliseconds;

            stopwatch.Restart();

            // Second call - should be faster (with cache if implemented)
            var chain2 = await optionsChainService.GetOptionChainAsync("SPY", DateTime.Today.AddDays(1), testPrice);
            var secondCallTime = stopwatch.ElapsedMilliseconds;

            logger.Information($"Performance test results:");
            logger.Information($"  First call: {firstCallTime}ms ({chain1.Options.Count} contracts)");
            logger.Information($"  Second call: {secondCallTime}ms ({chain2.Options.Count} contracts)");

            if (secondCallTime < firstCallTime * 0.8)
            {
                logger.Information("✅ Caching appears to be working effectively");
            }
            else
            {
                logger.Information("ℹ️ No significant performance improvement detected (caching may not be implemented)");
            }

            logger.Information("✅ Performance and caching test completed");
        }
        catch (Exception ex)
        {
            logger.Error(ex, "❌ Error testing performance and caching");
        }
    }
}
