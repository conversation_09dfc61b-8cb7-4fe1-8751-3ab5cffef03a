using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using ZeroDateStrat.Models;

namespace ZeroDateStrat.Services;

/// <summary>
/// Enhanced VIX Term Structure Service for analyzing VIX, VIX9D, and VIX3M
/// Provides comprehensive volatility term structure analysis for better trading decisions
/// </summary>
public interface IVixTermStructureService
{
    Task<VixTermStructure> GetCurrentTermStructureAsync();
    Task<VixTermStructureAnalysis> GetTermStructureAnalysisAsync();
    Task<List<VixTermStructureHistorical>> GetHistoricalTermStructureAsync(int days);
    Task<VolatilityContangoAnalysis> GetContangoAnalysisAsync();
    Task<VolatilityBackwardationAnalysis> GetBackwardationAnalysisAsync();
    Task<decimal> GetVolatilityRiskPremiumAsync();
    Task<VolatilityRegimeType> GetCurrentVolatilityRegimeAsync();
    Task<bool> IsTermStructureInvertedAsync();
    Task<decimal> GetTermStructureSlopeAsync();
    Task<bool> TestConnectionAsync();
}

public class VixTermStructureService : IVixTermStructureService
{
    private readonly IPolygonDataService _polygonDataService;
    private readonly ILogger<VixTermStructureService> _logger;
    private readonly IConfiguration _configuration;
    
    // Caching
    private VixTermStructure? _cachedTermStructure;
    private DateTime _lastTermStructureUpdate = DateTime.MinValue;
    private readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(1);
    
    // Historical data for analysis
    private readonly Queue<VixTermStructureHistorical> _historicalData = new();
    private readonly int _maxHistoricalPoints = 100;

    public VixTermStructureService(
        IPolygonDataService polygonDataService,
        ILogger<VixTermStructureService> logger,
        IConfiguration configuration)
    {
        _polygonDataService = polygonDataService ?? throw new ArgumentNullException(nameof(polygonDataService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
    }

    public async Task<VixTermStructure> GetCurrentTermStructureAsync()
    {
        try
        {
            // Check cache first
            if (_cachedTermStructure != null && 
                DateTime.UtcNow - _lastTermStructureUpdate < _cacheExpiry)
            {
                _logger.LogDebug("Returning cached VIX term structure");
                return _cachedTermStructure;
            }

            _logger.LogDebug("Fetching current VIX term structure (VIX, VIX9D, VIX3M)");

            var termStructure = new VixTermStructure
            {
                Timestamp = DateTime.UtcNow
            };

            // Fetch all VIX components in parallel for efficiency
            var vixTask = GetVixValueAsync("I:VIX");
            var vix9dTask = GetVixValueAsync("I:VIX9D");
            var vix3mTask = GetVixValueAsync("I:VIX3M");

            await Task.WhenAll(vixTask, vix9dTask, vix3mTask);

            termStructure.Vix = await vixTask;
            termStructure.Vix9D = await vix9dTask;
            termStructure.Vix3M = await vix3mTask;

            // Check if we have complete data
            termStructure.IsDataComplete = termStructure.Vix > 0 && 
                                         termStructure.Vix9D > 0 && 
                                         termStructure.Vix3M > 0;

            if (termStructure.IsDataComplete)
            {
                // Calculate term structure metrics
                CalculateTermStructureMetrics(termStructure);
                
                // Cache the result
                _cachedTermStructure = termStructure;
                _lastTermStructureUpdate = DateTime.UtcNow;

                _logger.LogInformation($"VIX Term Structure - VIX9D: {termStructure.Vix9D:F2}, " +
                                     $"VIX: {termStructure.Vix:F2}, VIX3M: {termStructure.Vix3M:F2}, " +
                                     $"Regime: {termStructure.RegimeType}");
            }
            else
            {
                _logger.LogWarning($"Incomplete VIX term structure data - VIX: {termStructure.Vix:F2}, " +
                                 $"VIX9D: {termStructure.Vix9D:F2}, VIX3M: {termStructure.Vix3M:F2}");
            }

            return termStructure;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching VIX term structure");
            return new VixTermStructure 
            { 
                Timestamp = DateTime.UtcNow,
                IsDataComplete = false 
            };
        }
    }

    private async Task<decimal> GetVixValueAsync(string symbol)
    {
        try
        {
            var snapshot = await _polygonDataService.GetIndexSnapshotAsync(symbol);
            if (snapshot.Value > 0)
            {
                _logger.LogDebug($"{symbol}: {snapshot.Value:F2}");
                return snapshot.Value;
            }

            // Fallback to aggregates if snapshot fails
            var yesterday = DateTime.UtcNow.AddDays(-1);
            var aggregates = await _polygonDataService.GetIndexAggregatesAsync(symbol, yesterday, DateTime.UtcNow, "minute");
            
            if (aggregates.Results.Any())
            {
                var latestValue = aggregates.Results.OrderByDescending(r => r.Date).First().Close;
                _logger.LogDebug($"{symbol} (from aggregates): {latestValue:F2}");
                return latestValue;
            }

            _logger.LogWarning($"No data available for {symbol}");
            return 0;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, $"Error fetching {symbol} data");
            return 0;
        }
    }

    private void CalculateTermStructureMetrics(VixTermStructure termStructure)
    {
        // Calculate slopes (annualized)
        // VIX9D to VIX: 21 days difference (30-9)
        // VIX to VIX3M: 60 days difference (90-30)
        termStructure.ShortTermSlope = (termStructure.Vix - termStructure.Vix9D) / 21m * 365m;
        termStructure.TermStructureSlope = (termStructure.Vix3M - termStructure.Vix) / 60m * 365m;

        // Calculate contango/backwardation
        var shortTermSpread = termStructure.Vix - termStructure.Vix9D;
        var longTermSpread = termStructure.Vix3M - termStructure.Vix;
        
        termStructure.Contango = Math.Max(0, (shortTermSpread + longTermSpread) / 2);
        termStructure.Backwardation = Math.Max(0, -(shortTermSpread + longTermSpread) / 2);

        // Determine regime type
        termStructure.RegimeType = DetermineVolatilityRegime(termStructure);
    }

    private VolatilityRegimeType DetermineVolatilityRegime(VixTermStructure structure)
    {
        var shortSpread = structure.Vix - structure.Vix9D;
        var longSpread = structure.Vix3M - structure.Vix;
        
        // Normal contango: VIX9D < VIX < VIX3M
        if (shortSpread > 0 && longSpread > 0)
        {
            return (shortSpread > 2 || longSpread > 3) ? 
                VolatilityRegimeType.SteepContango : 
                VolatilityRegimeType.NormalContango;
        }
        
        // Backwardation: VIX9D > VIX or VIX > VIX3M
        if (shortSpread < 0 || longSpread < 0)
        {
            return (shortSpread < -1 && longSpread < -2) ? 
                VolatilityRegimeType.InvertedStructure : 
                VolatilityRegimeType.Backwardation;
        }
        
        // Flat structure
        if (Math.Abs(shortSpread) < 0.5m && Math.Abs(longSpread) < 1m)
        {
            return VolatilityRegimeType.FlatStructure;
        }
        
        return VolatilityRegimeType.MixedSignals;
    }

    public async Task<bool> TestConnectionAsync()
    {
        try
        {
            var termStructure = await GetCurrentTermStructureAsync();
            return termStructure.IsDataComplete;
        }
        catch
        {
            return false;
        }
    }

    public async Task<VolatilityRegimeType> GetCurrentVolatilityRegimeAsync()
    {
        var termStructure = await GetCurrentTermStructureAsync();
        return termStructure.RegimeType;
    }

    public async Task<bool> IsTermStructureInvertedAsync()
    {
        var termStructure = await GetCurrentTermStructureAsync();
        return termStructure.RegimeType == VolatilityRegimeType.InvertedStructure ||
               termStructure.RegimeType == VolatilityRegimeType.Backwardation;
    }

    public async Task<decimal> GetTermStructureSlopeAsync()
    {
        var termStructure = await GetCurrentTermStructureAsync();
        return termStructure.TermStructureSlope;
    }

    public async Task<decimal> GetVolatilityRiskPremiumAsync()
    {
        var termStructure = await GetCurrentTermStructureAsync();
        return termStructure.Vix3M - termStructure.Vix; // Risk premium for holding longer-term volatility
    }

    public async Task<VixTermStructureAnalysis> GetTermStructureAnalysisAsync()
    {
        try
        {
            var currentStructure = await GetCurrentTermStructureAsync();

            var analysis = new VixTermStructureAnalysis
            {
                Timestamp = DateTime.UtcNow,
                CurrentStructure = currentStructure,
                VolatilityRiskPremium = currentStructure.Vix3M - currentStructure.Vix,
                ShortTermStress = currentStructure.Vix - currentStructure.Vix9D
            };

            // Get previous structure for comparison
            if (_historicalData.Any())
            {
                var previousData = _historicalData.OrderByDescending(h => h.Date).FirstOrDefault();
                if (previousData != null)
                {
                    analysis.PreviousStructure = previousData.TermStructure;
                    analysis.StructureChange = currentStructure.TermStructureSlope - previousData.TermStructure.TermStructureSlope;
                    analysis.RegimeTransition = DetermineRegimeTransition(previousData.TermStructure, currentStructure);
                }
            }

            // Generate market implications and trading signals
            GenerateMarketImplications(analysis);
            GenerateTradingSignals(analysis);

            // Calculate confidence based on data completeness and consistency
            analysis.Confidence = CalculateAnalysisConfidence(analysis);

            // Store current data for future analysis
            StoreHistoricalData(currentStructure);

            return analysis;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating VIX term structure analysis");
            return new VixTermStructureAnalysis
            {
                Timestamp = DateTime.UtcNow,
                CurrentStructure = new VixTermStructure(),
                Confidence = 0
            };
        }
    }

    public async Task<List<VixTermStructureHistorical>> GetHistoricalTermStructureAsync(int days)
    {
        try
        {
            var endDate = DateTime.UtcNow.Date;
            var startDate = endDate.AddDays(-days);

            var historicalData = new List<VixTermStructureHistorical>();

            // Fetch historical data for each day
            for (var date = startDate; date <= endDate; date = date.AddDays(1))
            {
                // Skip weekends
                if (date.DayOfWeek == DayOfWeek.Saturday || date.DayOfWeek == DayOfWeek.Sunday)
                    continue;

                var historicalStructure = await GetHistoricalTermStructureForDateAsync(date);
                if (historicalStructure.TermStructure.IsDataComplete)
                {
                    historicalData.Add(historicalStructure);
                }
            }

            _logger.LogDebug($"Retrieved {historicalData.Count} historical term structure data points for {days} days");
            return historicalData;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error fetching historical term structure for {days} days");
            return new List<VixTermStructureHistorical>();
        }
    }

    public async Task<VolatilityContangoAnalysis> GetContangoAnalysisAsync()
    {
        try
        {
            var currentStructure = await GetCurrentTermStructureAsync();
            var historicalData = await GetHistoricalTermStructureAsync(30); // Last 30 days

            var analysis = new VolatilityContangoAnalysis
            {
                Timestamp = DateTime.UtcNow,
                ContangoLevel = Math.Max(0, currentStructure.Contango),
                VolatilityRiskPremium = currentStructure.Vix3M - currentStructure.Vix
            };

            if (historicalData.Any())
            {
                // Calculate contango statistics
                var contangoValues = historicalData.Select(h => h.TermStructure.Contango).Where(c => c > 0).ToList();
                if (contangoValues.Any())
                {
                    analysis.AverageContango = contangoValues.Average();
                    analysis.IsExtremeContango = analysis.ContangoLevel > contangoValues.OrderByDescending(c => c).Take((int)(contangoValues.Count * 0.05)).Last();
                }

                // Count consecutive contango days
                analysis.ContangoDays = CountConsecutiveContangoDays(historicalData);

                // Calculate contango slope (trend)
                if (historicalData.Count >= 5)
                {
                    var recentContango = historicalData.TakeLast(5).Select(h => h.TermStructure.Contango).ToList();
                    analysis.ContangoSlope = CalculateSlope(recentContango);
                }
            }

            // Generate trading implications
            analysis.TradingImplication = GenerateContangoTradingImplication(analysis);

            return analysis;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating contango analysis");
            return new VolatilityContangoAnalysis { Timestamp = DateTime.UtcNow };
        }
    }

    public async Task<VolatilityBackwardationAnalysis> GetBackwardationAnalysisAsync()
    {
        try
        {
            var currentStructure = await GetCurrentTermStructureAsync();
            var historicalData = await GetHistoricalTermStructureAsync(30); // Last 30 days

            var analysis = new VolatilityBackwardationAnalysis
            {
                Timestamp = DateTime.UtcNow,
                BackwardationLevel = Math.Max(0, currentStructure.Backwardation),
                MarketStressLevel = CalculateMarketStressLevel(currentStructure)
            };

            if (historicalData.Any())
            {
                // Calculate backwardation statistics
                var backwardationValues = historicalData.Select(h => h.TermStructure.Backwardation).Where(b => b > 0).ToList();
                if (backwardationValues.Any())
                {
                    analysis.AverageBackwardation = backwardationValues.Average();
                    analysis.IsExtremeBackwardation = analysis.BackwardationLevel > backwardationValues.OrderByDescending(b => b).Take((int)(backwardationValues.Count * 0.05)).Last();
                }

                // Count consecutive backwardation days
                analysis.BackwardationDays = CountConsecutiveBackwardationDays(historicalData);

                // Calculate backwardation slope (trend)
                if (historicalData.Count >= 5)
                {
                    var recentBackwardation = historicalData.TakeLast(5).Select(h => h.TermStructure.Backwardation).ToList();
                    analysis.BackwardationSlope = CalculateSlope(recentBackwardation);
                }
            }

            // Generate stress indicators
            analysis.StressIndicator = GenerateStressIndicator(analysis);

            return analysis;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating backwardation analysis");
            return new VolatilityBackwardationAnalysis { Timestamp = DateTime.UtcNow };
        }
    }

    // Helper Methods
    private async Task<VixTermStructureHistorical> GetHistoricalTermStructureForDateAsync(DateTime date)
    {
        try
        {
            var historical = new VixTermStructureHistorical
            {
                Date = date,
                TermStructure = new VixTermStructure { Timestamp = date }
            };

            // Fetch historical data for the specific date
            var vixTask = GetHistoricalVixValueAsync("I:VIX", date);
            var vix9dTask = GetHistoricalVixValueAsync("I:VIX9D", date);
            var vix3mTask = GetHistoricalVixValueAsync("I:VIX3M", date);

            await Task.WhenAll(vixTask, vix9dTask, vix3mTask);

            historical.TermStructure.Vix = await vixTask;
            historical.TermStructure.Vix9D = await vix9dTask;
            historical.TermStructure.Vix3M = await vix3mTask;

            historical.TermStructure.IsDataComplete = historical.TermStructure.Vix > 0 &&
                                                    historical.TermStructure.Vix9D > 0 &&
                                                    historical.TermStructure.Vix3M > 0;

            if (historical.TermStructure.IsDataComplete)
            {
                CalculateTermStructureMetrics(historical.TermStructure);
            }

            return historical;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, $"Error fetching historical term structure for {date:yyyy-MM-dd}");
            return new VixTermStructureHistorical
            {
                Date = date,
                TermStructure = new VixTermStructure { Timestamp = date, IsDataComplete = false }
            };
        }
    }

    private async Task<decimal> GetHistoricalVixValueAsync(string symbol, DateTime date)
    {
        try
        {
            var aggregates = await _polygonDataService.GetIndexAggregatesAsync(symbol, date, date.AddDays(1), "day");

            if (aggregates.Results.Any())
            {
                return aggregates.Results.First().Close;
            }

            return 0;
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, $"Error fetching historical {symbol} for {date:yyyy-MM-dd}");
            return 0;
        }
    }

    private VolatilityRegimeTransition DetermineRegimeTransition(VixTermStructure previous, VixTermStructure current)
    {
        if (previous.RegimeType == current.RegimeType)
        {
            // Same regime - check if strengthening or weakening
            var previousStrength = Math.Abs(previous.TermStructureSlope);
            var currentStrength = Math.Abs(current.TermStructureSlope);

            return currentStrength > previousStrength ?
                VolatilityRegimeTransition.Strengthening :
                VolatilityRegimeTransition.Weakening;
        }

        // Regime change
        return current.RegimeType switch
        {
            VolatilityRegimeType.NormalContango or VolatilityRegimeType.SteepContango => VolatilityRegimeTransition.ToContango,
            VolatilityRegimeType.Backwardation or VolatilityRegimeType.InvertedStructure => VolatilityRegimeTransition.ToBackwardation,
            VolatilityRegimeType.FlatStructure => VolatilityRegimeTransition.ToFlat,
            _ => VolatilityRegimeTransition.Stable
        };
    }

    private void GenerateMarketImplications(VixTermStructureAnalysis analysis)
    {
        var implications = new List<string>();

        switch (analysis.CurrentStructure.RegimeType)
        {
            case VolatilityRegimeType.NormalContango:
                implications.Add("Normal market conditions with typical volatility term structure");
                implications.Add("Volatility selling strategies may be favorable");
                break;

            case VolatilityRegimeType.SteepContango:
                implications.Add("Strong contango suggests high volatility risk premium");
                implications.Add("Consider volatility selling strategies with caution on timing");
                break;

            case VolatilityRegimeType.Backwardation:
                implications.Add("Market stress indicated by inverted volatility structure");
                implications.Add("Volatility buying strategies may be more appropriate");
                break;

            case VolatilityRegimeType.InvertedStructure:
                implications.Add("Extreme market stress - highly inverted term structure");
                implications.Add("High probability of continued volatility expansion");
                break;

            case VolatilityRegimeType.FlatStructure:
                implications.Add("Uncertain market conditions with flat volatility structure");
                implications.Add("Range-bound strategies may be appropriate");
                break;
        }

        if (analysis.VolatilityRiskPremium > 3)
            implications.Add("High volatility risk premium suggests potential mean reversion");
        else if (analysis.VolatilityRiskPremium < 0)
            implications.Add("Negative risk premium indicates stressed market conditions");

        analysis.MarketImplication = string.Join("; ", implications);
    }

    private void GenerateTradingSignals(VixTermStructureAnalysis analysis)
    {
        var signals = new List<string>();

        // Term structure based signals
        if (analysis.CurrentStructure.RegimeType == VolatilityRegimeType.SteepContango && analysis.VolatilityRiskPremium > 4)
            signals.Add("SELL_VOLATILITY");
        else if (analysis.CurrentStructure.RegimeType == VolatilityRegimeType.Backwardation)
            signals.Add("BUY_VOLATILITY");
        else if (analysis.CurrentStructure.RegimeType == VolatilityRegimeType.FlatStructure)
            signals.Add("NEUTRAL_RANGE");

        // Transition based signals
        if (analysis.RegimeTransition == VolatilityRegimeTransition.ToBackwardation)
            signals.Add("DEFENSIVE_POSITIONING");
        else if (analysis.RegimeTransition == VolatilityRegimeTransition.ToContango)
            signals.Add("AGGRESSIVE_POSITIONING");

        // Short-term stress signals
        if (analysis.ShortTermStress > 2)
            signals.Add("SHORT_TERM_STRESS");
        else if (analysis.ShortTermStress < -1)
            signals.Add("SHORT_TERM_CALM");

        analysis.TradingSignal = signals.Any() ? string.Join("|", signals) : "NEUTRAL";
    }

    private decimal CalculateAnalysisConfidence(VixTermStructureAnalysis analysis)
    {
        var confidence = 100m;

        // Reduce confidence if data is incomplete
        if (!analysis.CurrentStructure.IsDataComplete)
            confidence -= 50m;

        // Reduce confidence for mixed signals
        if (analysis.CurrentStructure.RegimeType == VolatilityRegimeType.MixedSignals)
            confidence -= 30m;

        // Reduce confidence if no historical comparison
        if (analysis.PreviousStructure.Timestamp == DateTime.MinValue)
            confidence -= 20m;

        return Math.Max(0, confidence);
    }

    private void StoreHistoricalData(VixTermStructure currentStructure)
    {
        var historicalPoint = new VixTermStructureHistorical
        {
            Date = DateTime.UtcNow.Date,
            TermStructure = currentStructure
        };

        _historicalData.Enqueue(historicalPoint);

        // Maintain maximum stored points
        while (_historicalData.Count > _maxHistoricalPoints)
        {
            _historicalData.TryDequeue(out _);
        }
    }

    private int CountConsecutiveContangoDays(List<VixTermStructureHistorical> historicalData)
    {
        var count = 0;
        foreach (var data in historicalData.OrderByDescending(h => h.Date))
        {
            if (data.TermStructure.Contango > 0)
                count++;
            else
                break;
        }
        return count;
    }

    private int CountConsecutiveBackwardationDays(List<VixTermStructureHistorical> historicalData)
    {
        var count = 0;
        foreach (var data in historicalData.OrderByDescending(h => h.Date))
        {
            if (data.TermStructure.Backwardation > 0)
                count++;
            else
                break;
        }
        return count;
    }

    private decimal CalculateSlope(List<decimal> values)
    {
        if (values.Count < 2) return 0;

        var n = values.Count;
        var sumX = 0m;
        var sumY = values.Sum();
        var sumXY = 0m;
        var sumX2 = 0m;

        for (int i = 0; i < n; i++)
        {
            sumX += i;
            sumXY += i * values[i];
            sumX2 += i * i;
        }

        return (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
    }

    private decimal CalculateMarketStressLevel(VixTermStructure structure)
    {
        var stressLevel = 0m;

        // Base stress from VIX level
        if (structure.Vix > 30) stressLevel += 40;
        else if (structure.Vix > 25) stressLevel += 25;
        else if (structure.Vix > 20) stressLevel += 10;

        // Additional stress from term structure inversion
        if (structure.RegimeType == VolatilityRegimeType.InvertedStructure) stressLevel += 40;
        else if (structure.RegimeType == VolatilityRegimeType.Backwardation) stressLevel += 25;

        // Stress from short-term volatility spike
        var shortTermStress = structure.Vix - structure.Vix9D;
        if (shortTermStress < -3) stressLevel += 20;
        else if (shortTermStress < -1) stressLevel += 10;

        return Math.Min(100, stressLevel);
    }

    private string GenerateContangoTradingImplication(VolatilityContangoAnalysis analysis)
    {
        if (analysis.IsExtremeContango)
            return "Extreme contango - consider volatility selling strategies with tight risk management";

        if (analysis.ContangoDays > 10 && analysis.ContangoSlope > 0)
            return "Persistent contango with positive trend - volatility selling opportunities";

        if (analysis.VolatilityRiskPremium > 4)
            return "High volatility risk premium - potential mean reversion opportunity";

        return "Normal contango conditions - standard volatility strategies applicable";
    }

    private string GenerateStressIndicator(VolatilityBackwardationAnalysis analysis)
    {
        if (analysis.IsExtremeBackwardation && analysis.MarketStressLevel > 80)
            return "EXTREME_STRESS - Market in severe distress";

        if (analysis.BackwardationDays > 5 && analysis.BackwardationSlope > 0)
            return "PERSISTENT_STRESS - Ongoing market tension";

        if (analysis.MarketStressLevel > 60)
            return "HIGH_STRESS - Elevated market anxiety";

        if (analysis.MarketStressLevel > 30)
            return "MODERATE_STRESS - Some market concern";

        return "LOW_STRESS - Relatively calm market conditions";
    }
}
