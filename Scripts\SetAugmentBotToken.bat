@echo off
setlocal enabledelayedexpansion

echo ========================================
echo AugmentBot Discord Token Setup
echo ========================================
echo.

if "%~1"=="" (
    echo Usage: SetAugmentBotToken.bat "YOUR_AUGMENTBOT_TOKEN"
    echo.
    echo Example: SetAugmentBotToken.bat "MTM4MjU0NDg4MzI4NTQ5NTgwOA.G2atad.JHbCXK2yhoH4ENJC58u262lPh241Q4Vvf4xI9c"
    echo.
    echo This sets the DISCORD_BOT_TOKEN environment variable for AugmentBot
    echo AugmentBot uses this token to send guidance requests to Discord
    echo.
    pause
    exit /b 1
)

set "BOT_TOKEN=%~1"

echo Setting AugmentBot Discord token...
echo Token: %BOT_TOKEN:~0,20%...
echo.

:: Set environment variable for current session
set "DISCORD_BOT_TOKEN=%BOT_TOKEN%"

:: Set environment variable permanently for current user
setx DISCORD_BOT_TOKEN "%BOT_TOKEN%" >nul 2>&1

if %errorlevel% equ 0 (
    echo ✅ AugmentBot Discord token set successfully!
    echo.
    echo Environment variable DISCORD_BOT_TOKEN has been set.
    echo.
    echo Next steps:
    echo 1. Restart your command prompt or IDE to pick up the new environment variable
    echo 2. Run: dotnet run guidance-test
    echo 3. If test passes, AugmentBot can now send guidance requests to Discord
    echo.
    echo AugmentBot will send requests to @ChatGptBot in Discord channel.
) else (
    echo ❌ Failed to set environment variable
    echo You may need to run this script as administrator
)

echo.
pause
