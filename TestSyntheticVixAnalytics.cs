using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using ZeroDateStrat.Models;
using ZeroDateStrat.Services;

namespace ZeroDateStrat;

/// <summary>
/// Standalone test for SyntheticVIX Analytics
/// </summary>
public class TestSyntheticVixAnalytics
{
    public static async Task Main(string[] args)
    {
        Console.WriteLine("🔬 SyntheticVIX Analytics Standalone Test");
        Console.WriteLine(new string('=', 60));
        Console.WriteLine($"Test started at: {DateTime.Now}");

        try
        {
            // Setup services
            var services = new ServiceCollection();
            
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false)
                .AddJsonFile("appsettings.production.json", optional: true)
                .Build();

            services.AddSingleton<IConfiguration>(configuration);
            services.AddLogging(builder => 
            {
                builder.AddConsole();
                builder.SetMinimumLevel(LogLevel.Information);
            });
            
            // Add required services
            services.AddSingleton<IPerformanceMonitoringService, PerformanceMonitoringService>();
            services.AddSingleton<IAlpacaService, AlpacaService>();
            services.AddSingleton<ISyntheticVixAnalyticsService, SyntheticVixAnalyticsService>();
            services.AddSingleton<ISyntheticVixService, SyntheticVixService>();
            services.AddSingleton<IAlpacaVixService, AlpacaVixService>();
            services.AddSingleton<IPolygonDataService, PolygonDataService>();

            var serviceProvider = services.BuildServiceProvider();
            var analyticsService = serviceProvider.GetRequiredService<ISyntheticVixAnalyticsService>();
            var logger = serviceProvider.GetRequiredService<ILoggerFactory>().CreateLogger("SyntheticVixAnalyticsTest");

            Console.WriteLine("✅ Services initialized successfully");

            // Test 1: Health Report
            Console.WriteLine("\n📊 Test 1: Health Report");
            Console.WriteLine(new string('-', 30));
            try
            {
                var healthReport = await analyticsService.GetHealthReportAsync();
                Console.WriteLine($"   Overall Health: {healthReport.OverallHealth}");
                Console.WriteLine($"   Summary: {healthReport.Summary}");
                Console.WriteLine($"   Component Count: {healthReport.ComponentHealth.Count}");
                Console.WriteLine($"   Issues Count: {healthReport.Issues.Count}");
                
                if (healthReport.Issues.Any())
                {
                    Console.WriteLine("   Issues:");
                    foreach (var issue in healthReport.Issues.Take(3))
                    {
                        Console.WriteLine($"     - {issue}");
                    }
                }
                
                Console.WriteLine("   Component Health:");
                foreach (var component in healthReport.ComponentHealth)
                {
                    Console.WriteLine($"     {component.Key}: {(component.Value.IsHealthy ? "✅ Healthy" : "❌ Unhealthy")} " +
                                    $"(Price: ${component.Value.LastPrice:F2}, Failures: {component.Value.RecentFailureCount})");
                }
                Console.WriteLine("   ✅ Health Report test passed");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ Health Report test failed: {ex.Message}");
            }

            // Test 2: Performance Metrics
            Console.WriteLine("\n⚡ Test 2: Performance Metrics");
            Console.WriteLine(new string('-', 30));
            try
            {
                var performanceMetrics = await analyticsService.GetPerformanceMetricsAsync();
                Console.WriteLine($"   Average Calculation Time: {performanceMetrics.AverageCalculationTime:F1}ms");
                Console.WriteLine($"   Calculation Frequency: {performanceMetrics.CalculationFrequency} per hour");
                Console.WriteLine($"   Average Z-Score: {performanceMetrics.AverageZScore:F2}");
                Console.WriteLine($"   Value Stability: {performanceMetrics.ValueStability:F2}");
                
                if (performanceMetrics.ComponentMetrics.Any())
                {
                    Console.WriteLine("   Component Metrics:");
                    foreach (var metric in performanceMetrics.ComponentMetrics.Take(3))
                    {
                        Console.WriteLine($"     {metric.Key}: {metric.Value.TotalCalculations} calculations, " +
                                        $"Avg Price: ${metric.Value.AveragePrice:F2}, " +
                                        $"Confidence: {metric.Value.AverageConfidence:F2}");
                    }
                }
                Console.WriteLine("   ✅ Performance Metrics test passed");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ Performance Metrics test failed: {ex.Message}");
            }

            // Test 3: Active Alerts
            Console.WriteLine("\n🚨 Test 3: Active Alerts");
            Console.WriteLine(new string('-', 30));
            try
            {
                var alerts = await analyticsService.GetActiveAlertsAsync();
                Console.WriteLine($"   Active Alerts: {alerts.Count}");
                
                foreach (var alert in alerts.Take(5))
                {
                    Console.WriteLine($"   {alert.Severity} Alert: {alert.Type} - {alert.Message} ({alert.Timestamp:HH:mm:ss})");
                }
                
                if (alerts.Count == 0)
                {
                    Console.WriteLine("   No active alerts (this is good!)");
                }
                Console.WriteLine("   ✅ Active Alerts test passed");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ Active Alerts test failed: {ex.Message}");
            }

            // Test 4: Correlation Analysis
            Console.WriteLine("\n🔗 Test 4: Correlation Analysis");
            Console.WriteLine(new string('-', 30));
            try
            {
                var correlationAnalysis = await analyticsService.GetCorrelationAnalysisAsync();
                Console.WriteLine($"   Overall Correlation: {correlationAnalysis.OverallCorrelation:F3}");
                Console.WriteLine($"   Correlation Stability: {correlationAnalysis.CorrelationStability:F3}");
                
                if (correlationAnalysis.ComponentCorrelations.Any())
                {
                    Console.WriteLine("   Component Correlations:");
                    foreach (var correlation in correlationAnalysis.ComponentCorrelations)
                    {
                        Console.WriteLine($"     {correlation.Key}: {correlation.Value:F3}");
                    }
                }
                Console.WriteLine("   ✅ Correlation Analysis test passed");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ Correlation Analysis test failed: {ex.Message}");
            }

            // Test 5: Component Accuracy Validation
            Console.WriteLine("\n✅ Test 5: Component Accuracy Validation");
            Console.WriteLine(new string('-', 30));
            try
            {
                var accuracyValidation = await analyticsService.ValidateComponentAccuracyAsync();
                Console.WriteLine($"   Accuracy Validation: {(accuracyValidation ? "✅ PASSED" : "❌ FAILED")}");
                Console.WriteLine("   ✅ Component Accuracy test passed");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ Component Accuracy test failed: {ex.Message}");
            }

            // Test 6: Calibration Report
            Console.WriteLine("\n⚙️ Test 6: Calibration Report");
            Console.WriteLine(new string('-', 30));
            try
            {
                var calibrationReport = await analyticsService.GetCalibrationReportAsync();
                Console.WriteLine($"   Z-Score Distribution:");
                Console.WriteLine($"     Mean: {calibrationReport.ZScoreDistribution.Mean:F3}");
                Console.WriteLine($"     Std Dev: {calibrationReport.ZScoreDistribution.StandardDeviation:F3}");
                Console.WriteLine($"     Skewness: {calibrationReport.ZScoreDistribution.SkewnessFactor:F3}");
                
                if (calibrationReport.RecommendedAdjustments.Any())
                {
                    Console.WriteLine("   Recommended Adjustments:");
                    foreach (var adjustment in calibrationReport.RecommendedAdjustments.Take(3))
                    {
                        Console.WriteLine($"     {adjustment.Severity} - {adjustment.Type}: {adjustment.Description}");
                    }
                }
                else
                {
                    Console.WriteLine("   No calibration adjustments recommended");
                }
                Console.WriteLine("   ✅ Calibration Report test passed");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ Calibration Report test failed: {ex.Message}");
            }

            // Test 7: Simulate Component Failures
            Console.WriteLine("\n💥 Test 7: Component Failure Recording");
            Console.WriteLine(new string('-', 30));
            try
            {
                // Simulate some component failures
                analyticsService.RecordComponentFailure("VXX", new Exception("Simulated network timeout"));
                analyticsService.RecordComponentFailure("UVXY", new Exception("Simulated API rate limit"));
                analyticsService.RecordComponentFailure("VXX", new Exception("Simulated data unavailable"));
                
                Console.WriteLine("   Recorded 3 simulated component failures");
                
                // Check updated health report
                var updatedHealthReport = await analyticsService.GetHealthReportAsync();
                Console.WriteLine($"   Updated Health Status: {updatedHealthReport.OverallHealth}");
                Console.WriteLine("   ✅ Component Failure Recording test passed");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ Component Failure Recording test failed: {ex.Message}");
            }

            Console.WriteLine("\n🎉 All SyntheticVIX Analytics tests completed!");
            Console.WriteLine($"Test completed at: {DateTime.Now}");
            Console.WriteLine("\nSyntheticVIX Analytics Service is working correctly! ✅");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"\n❌ Test suite failed: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
        
        Console.WriteLine("\nPress any key to exit...");
        Console.ReadKey();
    }
}
