# VIX Calibration System Documentation

## Overview

The ZeroDateStrat VIX Calibration System provides real-time tuning of synthetic VIX calculations against actual VIX data from Polygon.io. This system ensures maximum accuracy for volatility-based trading decisions by continuously calibrating synthetic VIX components.

## Architecture

### Service Hierarchy

1. **Primary**: Real VIX data from Polygon.io (I:VIX)
2. **Fallback**: Calibrated Synthetic VIX calculation
3. **Final Fallback**: Conservative default (20.0)

### Key Components

#### 1. AlpacaVixService (Modified)
- **Primary Data Source**: Polygon VIX data
- **Fallback**: Synthetic VIX with calibration
- **Automatic Calibration**: Triggers calibration checks every 15 minutes
- **Integration**: Uses VixCalibrationService for advanced tuning

#### 2. VixCalibrationService (New)
- **Real-time Calibration**: Compares real vs synthetic VIX
- **Bias Correction**: Calculates and applies systematic bias
- **Scaling Adjustment**: Adjusts synthetic VIX scaling factor
- **Correlation Tracking**: Monitors correlation between real and synthetic VIX
- **Automatic Recalibration**: Triggers when drift exceeds thresholds

#### 3. SyntheticVixService (Enhanced)
- **Calibration Support**: Accepts bias and scaling factor adjustments
- **Dynamic Tuning**: Applies calibration to normalized VIX values
- **Status Tracking**: Monitors calibration state and age
- **Backward Compatibility**: Maintains existing functionality

## Configuration

### VixCalibration Settings (appsettings.json)

```json
{
  "VixCalibration": {
    "IntervalMinutes": 30,
    "RecalibrationThreshold": 0.15,
    "MaxHistoryPoints": 1000,
    "MinDataPointsForCalibration": 10,
    "CorrelationThreshold": 0.7,
    "BiasThreshold": 2.0,
    "ScalingFactorRange": {
      "Min": 0.8,
      "Max": 1.2
    },
    "AutoCalibrationEnabled": true,
    "CalibrationReportIntervalHours": 6,
    "AlertOnSignificantDrift": true,
    "DriftThresholdPercent": 20.0
  }
}
```

### Key Parameters

- **IntervalMinutes**: How often to perform calibration checks
- **RecalibrationThreshold**: Percentage difference that triggers recalibration
- **MaxHistoryPoints**: Maximum calibration data points to retain
- **CorrelationThreshold**: Minimum acceptable correlation coefficient
- **BiasThreshold**: Maximum acceptable bias before alerting
- **ScalingFactorRange**: Acceptable range for scaling factor adjustments

## Usage

### Basic VIX Data Retrieval

```csharp
// Get VIX data (automatically uses real VIX as primary)
var vix = await alpacaVixService.GetCurrentVixAsync();

// The service automatically:
// 1. Tries real Polygon VIX data first
// 2. Falls back to calibrated synthetic VIX
// 3. Triggers calibration checks in background
```

### Manual Calibration

```csharp
// Perform manual calibration
var realVix = await polygonDataService.GetCurrentVixAsync();
var syntheticVix = await syntheticVixService.GetCurrentSyntheticVixAsync();

var calibrationResult = await vixCalibrationService.CalibrateAsync(realVix, syntheticVix);

// Apply calibration to synthetic VIX
await syntheticVixService.ApplyCalibrationAsync(
    calibrationResult.Bias, 
    calibrationResult.ScalingFactor);
```

### Calibration Monitoring

```csharp
// Get calibration report
var report = await vixCalibrationService.GetCalibrationReportAsync();
Console.WriteLine($"Calibration Status: {report.Status}");
Console.WriteLine($"Correlation: {report.CorrelationCoefficient:F3}");
Console.WriteLine($"Average Error: {report.AverageAbsoluteError:F2}");

// Get recommendations
var recommendations = await vixCalibrationService.GetRecommendationsAsync();
foreach (var rec in recommendations.Recommendations)
{
    Console.WriteLine($"• {rec}");
}
```

## Calibration Process

### 1. Data Collection
- Real VIX data from Polygon.io indices endpoint
- Synthetic VIX from ETF composite calculation
- Timestamp and metadata for each data point

### 2. Statistical Analysis
- **Bias Calculation**: Average difference between real and synthetic VIX
- **Scaling Factor**: Ratio adjustment for magnitude differences
- **Correlation**: Pearson correlation coefficient
- **Error Metrics**: MAE, maximum error, percentage differences

### 3. Calibration Application
- **Bias Correction**: `calibratedVix = (syntheticVix * scalingFactor) + bias`
- **Bounds Checking**: Ensures result stays within reasonable VIX range (5-100)
- **Cache Invalidation**: Forces recalculation with new parameters

### 4. Monitoring and Alerts
- **Drift Detection**: Monitors for significant deviations
- **Correlation Tracking**: Alerts when correlation drops below threshold
- **Performance Metrics**: Tracks calibration effectiveness over time

## Benefits

### 1. Improved Accuracy
- Real VIX data provides ground truth for volatility measurements
- Synthetic VIX serves as reliable backup with continuous tuning
- Calibration reduces systematic errors and bias

### 2. Robust Fallback
- Graceful degradation when real VIX data unavailable
- Synthetic VIX maintains trading capability during outages
- Conservative defaults prevent system failures

### 3. Adaptive Learning
- Continuous calibration improves accuracy over time
- Market regime changes automatically detected and adjusted
- Component weight optimization based on real-world performance

### 4. Operational Excellence
- Automated calibration reduces manual intervention
- Comprehensive monitoring and alerting
- Detailed reporting for performance analysis

## Testing

### VixCalibrationTest Program

Run the test program to verify system functionality:

```bash
dotnet run --project Tests/VixCalibrationTest.cs
```

### Test Coverage

1. **Real VIX Data Retrieval**: Verifies Polygon.io connection
2. **Synthetic VIX Calculation**: Tests ETF composite calculation
3. **Service Priority**: Confirms real VIX used as primary
4. **Calibration System**: Tests bias and scaling factor calculation
5. **Report Generation**: Validates calibration reporting
6. **Recommendations**: Tests calibration suggestions
7. **Calibration Application**: Verifies parameter updates
8. **Connection Tests**: Validates all service connections

## Monitoring

### Key Metrics to Monitor

- **VIX Difference**: Real vs Synthetic VIX percentage difference
- **Calibration Frequency**: How often recalibration occurs
- **Correlation Coefficient**: Strength of real/synthetic relationship
- **Error Metrics**: MAE, RMSE, maximum error
- **Service Availability**: Uptime of real VIX data source

### Alert Conditions

- **High Drift**: >20% difference between real and synthetic VIX
- **Low Correlation**: Correlation coefficient <0.7
- **Service Failure**: Real VIX data unavailable for >30 minutes
- **Calibration Failure**: Unable to apply calibration parameters

## Troubleshooting

### Common Issues

1. **Real VIX Data Unavailable**
   - Check Polygon.io API key and subscription
   - Verify network connectivity
   - Review API rate limits

2. **Poor Calibration Performance**
   - Increase calibration data collection period
   - Review ETF component selection
   - Check for market regime changes

3. **High Calibration Drift**
   - Investigate ETF component failures
   - Review market conditions for unusual volatility
   - Consider adjusting calibration thresholds

### Diagnostic Commands

```csharp
// Check calibration status
var status = await syntheticVixService.GetCalibrationStatusAsync();

// Get detailed metrics
var metrics = await vixCalibrationService.GetMetricsAsync();

// Test connections
var polygonTest = await polygonDataService.TestConnectionAsync();
var syntheticTest = await syntheticVixService.TestConnectionAsync();
```

## Future Enhancements

### Planned Features

1. **Machine Learning Integration**: Use ML models for calibration prediction
2. **Multi-timeframe Calibration**: Different calibration for various time horizons
3. **Component Weight Optimization**: Dynamic ETF weight adjustment
4. **Historical Backtesting**: Validate calibration effectiveness historically
5. **Advanced Analytics**: Regime-specific calibration parameters

### Configuration Expansion

- Market regime-specific calibration parameters
- Time-of-day calibration adjustments
- Volatility environment-based tuning
- Cross-validation and model selection

## Conclusion

The VIX Calibration System provides a robust, accurate, and adaptive approach to volatility measurement for the ZeroDateStrat trading system. By combining real VIX data with intelligently calibrated synthetic calculations, the system ensures optimal trading decisions across all market conditions.
