using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using ZeroDateStrat.Models;
using ZeroDateStrat.Services;

namespace ZeroDateStrat.Tests;

/// <summary>
/// Test class for the Discord guidance request functionality
/// </summary>
public static class GuidanceRequestTest
{
    /// <summary>
    /// Run a comprehensive test of the guidance request system
    /// </summary>
    public static async Task RunGuidanceRequestTestAsync()
    {
        Console.WriteLine("🤖 Starting Discord Guidance Request Test");
        Console.WriteLine("==========================================");

        try
        {
            // Build the host to get services
            var host = CreateTestHost();
            await host.StartAsync();

            var guidanceService = host.Services.GetRequiredService<IGuidanceRequestService>();
            var loggerFactory = host.Services.GetRequiredService<ILoggerFactory>();
            var logger = loggerFactory.CreateLogger("GuidanceRequestTest");

            Console.WriteLine("✅ Services initialized successfully");

            // Test 1: Simple plaintext guidance request
            await TestPlaintextGuidanceRequest(guidanceService, logger);

            // Test 2: Structured JSON guidance request
            await TestStructuredGuidanceRequest(guidanceService, logger);

            // Test 3: Custom guidance request with full control
            await TestCustomGuidanceRequest(guidanceService, logger);

            // Test 4: Test request status and storage
            await TestRequestStatusAndStorage(guidanceService, logger);

            Console.WriteLine("\n🎉 All guidance request tests completed successfully!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Test failed with error: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
    }

    private static async Task TestPlaintextGuidanceRequest(IGuidanceRequestService guidanceService, ILogger logger)
    {
        Console.WriteLine("\n📝 Test 1: Plaintext Guidance Request");
        Console.WriteLine("-------------------------------------");

        try
        {
            var response = await guidanceService.RequestGuidanceAsync(
                taskStatement: "I need instructions for building a C# class that scrapes the VIX index from Yahoo Finance and rebalances a SyntheticVIX model.",
                language: "C#",
                components: new List<string> { "Yahoo scraper", "REST client", "VIX model" },
                notes: "This is for use inside a 0DTE trading bot"
            );

            if (response != null)
            {
                Console.WriteLine($"✅ Received response from ChatGptBot:");
                Console.WriteLine($"   Content: {response.Content.Substring(0, Math.Min(100, response.Content.Length))}...");
                Console.WriteLine($"   Author: {response.AuthorUsername}");
                Console.WriteLine($"   Received at: {response.ReceivedAt}");
            }
            else
            {
                Console.WriteLine("⚠️ No response received (this is expected in test mode)");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Plaintext request failed: {ex.Message}");
        }
    }

    private static async Task TestStructuredGuidanceRequest(IGuidanceRequestService guidanceService, ILogger logger)
    {
        Console.WriteLine("\n🏗️ Test 2: Structured JSON Guidance Request");
        Console.WriteLine("--------------------------------------------");

        try
        {
            var structuredRequest = new StructuredGuidanceRequest
            {
                Intent = "request_instruction",
                Topic = "SyntheticVIX rebalance",
                Language = "C#",
                Components = new List<string> { "Yahoo scraper", "OpenAI function call", "REST client" },
                Notes = "This is for use inside a 0DTE trading bot",
                Requirements = new List<string> { "Error handling", "Async/await pattern", "Logging" }
            };

            var response = await guidanceService.RequestStructuredGuidanceAsync(structuredRequest);

            if (response != null)
            {
                Console.WriteLine($"✅ Received structured response from ChatGptBot:");
                Console.WriteLine($"   Content: {response.Content.Substring(0, Math.Min(100, response.Content.Length))}...");
                Console.WriteLine($"   Request ID: {response.RequestId}");
            }
            else
            {
                Console.WriteLine("⚠️ No response received (this is expected in test mode)");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Structured request failed: {ex.Message}");
        }
    }

    private static async Task TestCustomGuidanceRequest(IGuidanceRequestService guidanceService, ILogger logger)
    {
        Console.WriteLine("\n🎛️ Test 3: Custom Guidance Request");
        Console.WriteLine("----------------------------------");

        try
        {
            var customRequest = new GuidanceRequest
            {
                TaskStatement = "Create a risk management system for options trading",
                Language = "C#",
                TechnicalRequirements = new List<string> { "Real-time monitoring", "Circuit breakers", "Position sizing" },
                Components = new List<string> { "Risk calculator", "Alert system", "Portfolio tracker" },
                Notes = "Must integrate with Alpaca API and Discord notifications",
                Format = GuidanceRequestFormat.StructuredJson,
                ResponseTimeout = TimeSpan.FromSeconds(45),
                MaxRetries = 2
            };

            var response = await guidanceService.SendGuidanceRequestAsync(customRequest);

            if (response != null)
            {
                Console.WriteLine($"✅ Received custom response from ChatGptBot:");
                Console.WriteLine($"   Content: {response.Content.Substring(0, Math.Min(100, response.Content.Length))}...");
                Console.WriteLine($"   Message ID: {response.MessageId}");
            }
            else
            {
                Console.WriteLine("⚠️ No response received (this is expected in test mode)");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Custom request failed: {ex.Message}");
        }
    }

    private static async Task TestRequestStatusAndStorage(IGuidanceRequestService guidanceService, ILogger logger)
    {
        Console.WriteLine("\n📊 Test 4: Request Status and Storage");
        Console.WriteLine("-------------------------------------");

        try
        {
            // Get all stored responses
            var storedResponses = await guidanceService.GetAllStoredResponsesAsync();
            Console.WriteLine($"📁 Found {storedResponses.Count} stored responses");

            // Test cleanup (remove responses older than 1 hour for testing)
            var cleanedCount = await guidanceService.CleanupOldResponsesAsync(TimeSpan.FromHours(1));
            Console.WriteLine($"🧹 Cleaned up {cleanedCount} old responses");

            Console.WriteLine("✅ Status and storage tests completed");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Status and storage test failed: {ex.Message}");
        }
    }

    private static IHost CreateTestHost()
    {
        return Host.CreateDefaultBuilder()
            .ConfigureAppConfiguration((context, config) =>
            {
                config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
                config.AddEnvironmentVariables();
            })
            .ConfigureServices((context, services) =>
            {
                // Add logging
                services.AddLogging(builder =>
                {
                    builder.AddConsole();
                    builder.SetMinimumLevel(LogLevel.Information);
                });

                // Add required services for testing
                services.AddSingleton<IDiscordService, DiscordService>();
                services.AddSingleton<IGuidanceRequestService, GuidanceRequestService>();
                services.AddHostedService<DiscordService>();
            })
            .Build();
    }

    /// <summary>
    /// Demonstrate usage examples for the guidance request system
    /// </summary>
    public static void ShowUsageExamples()
    {
        Console.WriteLine("\n📚 Guidance Request Usage Examples");
        Console.WriteLine("==================================");

        Console.WriteLine("\n1. Simple Request:");
        Console.WriteLine("var response = await guidanceService.RequestGuidanceAsync(");
        Console.WriteLine("    \"Create a VIX volatility calculator\",");
        Console.WriteLine("    \"C#\",");
        Console.WriteLine("    new List<string> { \"Math library\", \"Financial formulas\" },");
        Console.WriteLine("    \"For 0DTE options trading\");");

        Console.WriteLine("\n2. Structured Request:");
        Console.WriteLine("var request = new StructuredGuidanceRequest {");
        Console.WriteLine("    Topic = \"Options pricing model\",");
        Console.WriteLine("    Language = \"C#\",");
        Console.WriteLine("    Components = new[] { \"Black-Scholes\", \"Greeks calculation\" },");
        Console.WriteLine("    Requirements = new[] { \"Real-time data\", \"High performance\" }");
        Console.WriteLine("};");
        Console.WriteLine("var response = await guidanceService.RequestStructuredGuidanceAsync(request);");

        Console.WriteLine("\n3. Custom Request with Events:");
        Console.WriteLine("guidanceService.OnGuidanceResponseReceived += async (response) => {");
        Console.WriteLine("    Console.WriteLine($\"Got response: {response.Content}\");");
        Console.WriteLine("};");
        Console.WriteLine("guidanceService.OnGuidanceRequestTimeout += async (request) => {");
        Console.WriteLine("    Console.WriteLine($\"Request {request.Id} timed out\");");
        Console.WriteLine("};");
    }
}
