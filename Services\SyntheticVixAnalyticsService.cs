using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using ZeroDateStrat.Models;
using System.Collections.Concurrent;

namespace ZeroDateStrat.Services;

/// <summary>
/// Advanced analytics service for SyntheticVIX monitoring, performance analysis, and alerting
/// </summary>
public interface ISyntheticVixAnalyticsService
{
    Task<SyntheticVixHealthReport> GetHealthReportAsync();
    Task<SyntheticVixPerformanceMetrics> GetPerformanceMetricsAsync();
    Task<List<SyntheticVixAlert>> GetActiveAlertsAsync();
    Task<SyntheticVixCorrelationAnalysis> GetCorrelationAnalysisAsync();
    Task<bool> ValidateComponentAccuracyAsync();
    Task<SyntheticVixCalibrationReport> GetCalibrationReportAsync();
    void RecordCalculation(SyntheticVixAnalysis analysis);
    void RecordComponentFailure(string symbol, Exception exception);
}

public class SyntheticVixAnalyticsService : ISyntheticVixAnalyticsService
{
    private readonly ILogger<SyntheticVixAnalyticsService> _logger;
    private readonly ISyntheticVixDataProvider _syntheticVixDataProvider;
    private readonly IAlpacaService _alpacaService;
    private readonly IPolygonDataService? _polygonDataService;
    private readonly IConfiguration _configuration;
    
    // Analytics data storage
    private readonly ConcurrentQueue<SyntheticVixAnalysis> _recentCalculations = new();
    private readonly ConcurrentDictionary<string, List<ComponentFailure>> _componentFailures = new();
    private readonly ConcurrentQueue<SyntheticVixAlert> _activeAlerts = new();
    private readonly int _maxStoredCalculations = 1000;
    
    // Performance tracking
    private readonly ConcurrentDictionary<string, ComponentPerformanceMetrics> _componentMetrics = new();
    private DateTime _lastCalibrationCheck = DateTime.MinValue;
    private readonly TimeSpan _calibrationCheckInterval = TimeSpan.FromHours(1);

    public SyntheticVixAnalyticsService(
        ILogger<SyntheticVixAnalyticsService> logger,
        ISyntheticVixDataProvider syntheticVixDataProvider,
        IAlpacaService alpacaService,
        IConfiguration configuration,
        IPolygonDataService? polygonDataService = null)
    {
        _logger = logger;
        _syntheticVixDataProvider = syntheticVixDataProvider;
        _alpacaService = alpacaService;
        _polygonDataService = polygonDataService;
        _configuration = configuration;
        
        _logger.LogInformation("SyntheticVixAnalyticsService initialized");
    }

    public async Task<SyntheticVixHealthReport> GetHealthReportAsync()
    {
        try
        {
            var report = new SyntheticVixHealthReport
            {
                Timestamp = DateTime.UtcNow,
                OverallHealth = HealthStatus.Healthy,
                Issues = new List<string>()
            };

            // Check component availability
            var componentHealth = await CheckComponentHealthAsync();
            report.ComponentHealth = componentHealth;
            
            // Check calculation accuracy
            var accuracyCheck = await ValidateComponentAccuracyAsync();
            report.AccuracyValidation = accuracyCheck;
            
            // Check recent calculation frequency
            var recentCalcs = _recentCalculations.Where(c => c.Timestamp > DateTime.UtcNow.AddMinutes(-10)).Count();
            if (recentCalcs < 5)
            {
                report.Issues.Add($"Low calculation frequency: {recentCalcs} calculations in last 10 minutes");
                report.OverallHealth = HealthStatus.Degraded;
            }
            
            // Check for component failures
            var recentFailures = _componentFailures.Values
                .SelectMany(failures => failures.Where(f => f.Timestamp > DateTime.UtcNow.AddMinutes(-30)))
                .Count();
            
            if (recentFailures > 10)
            {
                report.Issues.Add($"High component failure rate: {recentFailures} failures in last 30 minutes");
                report.OverallHealth = HealthStatus.Unhealthy;
            }
            else if (recentFailures > 5)
            {
                report.Issues.Add($"Elevated component failure rate: {recentFailures} failures in last 30 minutes");
                report.OverallHealth = HealthStatus.Degraded;
            }

            // Check z-score normalization stability
            var recentZScores = _recentCalculations
                .Where(c => c.Timestamp > DateTime.UtcNow.AddMinutes(-30))
                .Select(c => c.ZScore)
                .ToList();
            
            if (recentZScores.Any() && recentZScores.Any(z => Math.Abs(z) > 3))
            {
                report.Issues.Add("Extreme z-scores detected - normalization may need adjustment");
                report.OverallHealth = HealthStatus.Degraded;
            }

            report.Summary = report.OverallHealth switch
            {
                HealthStatus.Healthy => "SyntheticVIX operating normally",
                HealthStatus.Degraded => $"SyntheticVIX degraded: {string.Join(", ", report.Issues)}",
                _ => $"SyntheticVIX unhealthy: {string.Join(", ", report.Issues)}"
            };

            return report;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating SyntheticVIX health report");
            return new SyntheticVixHealthReport
            {
                Timestamp = DateTime.UtcNow,
                OverallHealth = HealthStatus.Unhealthy,
                Issues = new List<string> { $"Health check failed: {ex.Message}" },
                Summary = "Unable to determine SyntheticVIX health"
            };
        }
    }

    public async Task<SyntheticVixPerformanceMetrics> GetPerformanceMetricsAsync()
    {
        try
        {
            var metrics = new SyntheticVixPerformanceMetrics
            {
                Timestamp = DateTime.UtcNow,
                ComponentMetrics = new Dictionary<string, ComponentPerformanceMetrics>(_componentMetrics)
            };

            // Calculate overall metrics from recent calculations
            var recentCalcs = _recentCalculations.Where(c => c.Timestamp > DateTime.UtcNow.AddHours(-1)).ToList();
            
            if (recentCalcs.Any())
            {
                metrics.AverageCalculationTime = CalculateAverageCalculationTime();
                metrics.CalculationFrequency = recentCalcs.Count;
                metrics.AverageZScore = recentCalcs.Average(c => Math.Abs(c.ZScore));
                metrics.ValueStability = CalculateValueStability(recentCalcs);
                
                // Component contribution analysis
                var componentContributions = AnalyzeComponentContributions(recentCalcs);
                metrics.ComponentContributions = componentContributions;
            }

            return metrics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating SyntheticVIX performance metrics");
            return new SyntheticVixPerformanceMetrics { Timestamp = DateTime.UtcNow };
        }
    }

    public async Task<List<SyntheticVixAlert>> GetActiveAlertsAsync()
    {
        try
        {
            // Clean up old alerts (older than 24 hours)
            var cutoffTime = DateTime.UtcNow.AddHours(-24);
            var currentAlerts = new List<SyntheticVixAlert>();
            
            while (_activeAlerts.TryDequeue(out var alert))
            {
                if (alert.Timestamp > cutoffTime)
                {
                    currentAlerts.Add(alert);
                }
            }
            
            // Re-queue current alerts
            foreach (var alert in currentAlerts)
            {
                _activeAlerts.Enqueue(alert);
            }
            
            return currentAlerts.OrderByDescending(a => a.Severity).ThenByDescending(a => a.Timestamp).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving SyntheticVIX alerts");
            return new List<SyntheticVixAlert>();
        }
    }

    public async Task<SyntheticVixCorrelationAnalysis> GetCorrelationAnalysisAsync()
    {
        try
        {
            var analysis = new SyntheticVixCorrelationAnalysis
            {
                Timestamp = DateTime.UtcNow,
                ComponentCorrelations = new Dictionary<string, decimal>()
            };

            // Get recent historical data for correlation analysis
            var historicalData = await _syntheticVixDataProvider.GetHistoricalDataAsync(7); // Last 7 days
            
            if (historicalData.Count > 10)
            {
                // Calculate correlations between components
                foreach (var symbol in new[] { "VXX", "UVXY", "SVXY" })
                {
                    var correlation = CalculateComponentCorrelation(historicalData, symbol);
                    analysis.ComponentCorrelations[symbol] = correlation;
                }
                
                // Calculate overall correlation with expected VIX behavior
                analysis.OverallCorrelation = CalculateOverallCorrelation(historicalData);
                analysis.CorrelationStability = CalculateCorrelationStability(historicalData);
            }

            return analysis;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing SyntheticVIX correlation analysis");
            return new SyntheticVixCorrelationAnalysis { Timestamp = DateTime.UtcNow };
        }
    }

    public async Task<bool> ValidateComponentAccuracyAsync()
    {
        try
        {
            var validationResults = new List<bool>();
            
            // Test each component for reasonable price ranges and responsiveness
            foreach (var symbol in new[] { "VXX", "UVXY", "SVXY" })
            {
                try
                {
                    var price = await _alpacaService.GetCurrentPriceAsync(symbol);
                    var isValid = ValidateComponentPrice(symbol, price);
                    validationResults.Add(isValid);
                    
                    if (!isValid)
                    {
                        AddAlert(new SyntheticVixAlert
                        {
                            Id = Guid.NewGuid().ToString(),
                            Type = "ComponentValidation",
                            Severity = AlertSeverity.Medium,
                            Message = $"Component {symbol} price validation failed: ${price:F2}",
                            Timestamp = DateTime.UtcNow
                        });
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Component validation failed for {Symbol}", symbol);
                    validationResults.Add(false);
                }
            }
            
            return validationResults.Count > 0 && validationResults.Count(v => v) >= validationResults.Count * 0.67; // 67% success rate
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating component accuracy");
            return false;
        }
    }

    public async Task<SyntheticVixCalibrationReport> GetCalibrationReportAsync()
    {
        try
        {
            var report = new SyntheticVixCalibrationReport
            {
                Timestamp = DateTime.UtcNow,
                RecommendedAdjustments = new List<CalibrationAdjustment>()
            };

            // Analyze recent performance and suggest calibration adjustments
            var recentCalcs = _recentCalculations.Where(c => c.Timestamp > DateTime.UtcNow.AddDays(-7)).ToList();
            
            if (recentCalcs.Count > 50)
            {
                // Check z-score distribution
                var zScores = recentCalcs.Select(c => (double)c.ZScore).ToList();
                var avgZScore = zScores.Average();
                var stdDevZScore = Math.Sqrt(zScores.Sum(z => Math.Pow(z - avgZScore, 2)) / zScores.Count);

                report.ZScoreDistribution = new ZScoreDistribution
                {
                    Mean = (decimal)avgZScore,
                    StandardDeviation = (decimal)stdDevZScore,
                    SkewnessFactor = CalculateSkewness(zScores)
                };

                // Suggest adjustments if needed
                if (Math.Abs(avgZScore) > 0.5)
                {
                    report.RecommendedAdjustments.Add(new CalibrationAdjustment
                    {
                        Type = "ZScoreOffset",
                        Description = $"Z-score mean is {avgZScore:F2}, consider adjusting normalization baseline",
                        Severity = Math.Abs(avgZScore) > 1.0 ? "High" : "Medium"
                    });
                }

                if (stdDevZScore > 2.0)
                {
                    report.RecommendedAdjustments.Add(new CalibrationAdjustment
                    {
                        Type = "ZScoreVariability",
                        Description = $"High z-score variability ({stdDevZScore:F2}), consider adjusting window size",
                        Severity = "Medium"
                    });
                }
            }

            return report;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating calibration report");
            return new SyntheticVixCalibrationReport { Timestamp = DateTime.UtcNow };
        }
    }

    public void RecordCalculation(SyntheticVixAnalysis analysis)
    {
        try
        {
            _recentCalculations.Enqueue(analysis);
            
            // Maintain maximum stored calculations
            while (_recentCalculations.Count > _maxStoredCalculations)
            {
                _recentCalculations.TryDequeue(out _);
            }
            
            // Update component metrics
            foreach (var component in analysis.ComponentBreakdown)
            {
                UpdateComponentMetrics(component);
            }
            
            // Check for alerts
            CheckForAlerts(analysis);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error recording SyntheticVIX calculation");
        }
    }

    public void RecordComponentFailure(string symbol, Exception exception)
    {
        try
        {
            var failure = new ComponentFailure
            {
                Symbol = symbol,
                Timestamp = DateTime.UtcNow,
                ErrorMessage = exception.Message,
                ExceptionType = exception.GetType().Name
            };
            
            _componentFailures.AddOrUpdate(symbol, 
                new List<ComponentFailure> { failure },
                (key, existing) =>
                {
                    existing.Add(failure);
                    // Keep only last 100 failures per component
                    return existing.TakeLast(100).ToList();
                });
            
            // Generate alert for repeated failures
            var recentFailures = _componentFailures[symbol].Where(f => f.Timestamp > DateTime.UtcNow.AddMinutes(-15)).Count();
            if (recentFailures >= 5)
            {
                AddAlert(new SyntheticVixAlert
                {
                    Id = Guid.NewGuid().ToString(),
                    Type = "ComponentFailure",
                    Severity = AlertSeverity.High,
                    Message = $"Component {symbol} has failed {recentFailures} times in the last 15 minutes",
                    Timestamp = DateTime.UtcNow
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error recording component failure for {Symbol}", symbol);
        }
    }

    // Private helper methods
    private async Task<Dictionary<string, ComponentHealthStatus>> CheckComponentHealthAsync()
    {
        var health = new Dictionary<string, ComponentHealthStatus>();

        foreach (var symbol in new[] { "VXX", "UVXY", "SVXY" })
        {
            try
            {
                var price = await _alpacaService.GetCurrentPriceAsync(symbol);
                var recentFailures = _componentFailures.ContainsKey(symbol)
                    ? _componentFailures[symbol].Where(f => f.Timestamp > DateTime.UtcNow.AddMinutes(-30)).Count()
                    : 0;

                health[symbol] = new ComponentHealthStatus
                {
                    Symbol = symbol,
                    IsHealthy = price > 0 && recentFailures < 5,
                    LastPrice = price,
                    RecentFailureCount = recentFailures,
                    LastSuccessfulUpdate = DateTime.UtcNow
                };
            }
            catch (Exception ex)
            {
                health[symbol] = new ComponentHealthStatus
                {
                    Symbol = symbol,
                    IsHealthy = false,
                    LastPrice = 0,
                    RecentFailureCount = 999,
                    LastSuccessfulUpdate = DateTime.MinValue,
                    ErrorMessage = ex.Message
                };
            }
        }

        return health;
    }

    private double CalculateAverageCalculationTime()
    {
        // This would be enhanced with actual timing data from performance monitoring
        return 150.0; // Placeholder - would come from performance metrics
    }

    private decimal CalculateValueStability(List<SyntheticVixAnalysis> calculations)
    {
        if (calculations.Count < 2) return 1.0m;

        var values = calculations.Select(c => c.CurrentLevel).ToList();
        var mean = values.Average();
        var variance = values.Sum(v => (v - mean) * (v - mean)) / values.Count;
        var stdDev = (decimal)Math.Sqrt((double)variance);

        // Return stability as inverse of coefficient of variation (lower is more stable)
        return mean > 0 ? Math.Max(0, 1 - (stdDev / mean)) : 0;
    }

    private Dictionary<string, decimal> AnalyzeComponentContributions(List<SyntheticVixAnalysis> calculations)
    {
        var contributions = new Dictionary<string, decimal>();

        foreach (var symbol in new[] { "VXX", "UVXY", "SVXY" })
        {
            var componentData = calculations
                .SelectMany(c => c.ComponentBreakdown.Where(cb => cb.Symbol == symbol))
                .ToList();

            if (componentData.Any())
            {
                contributions[symbol] = componentData.Average(cd => Math.Abs(cd.NormalizedValue * cd.Weight));
            }
        }

        return contributions;
    }

    private decimal CalculateComponentCorrelation(List<SyntheticVixHistoricalData> historicalData, string symbol)
    {
        // Simplified correlation calculation
        var componentValues = historicalData
            .Where(h => h.ComponentValues.ContainsKey(symbol))
            .Select(h => h.ComponentValues[symbol])
            .ToList();

        var syntheticValues = historicalData.Select(h => h.NormalizedValue).ToList();

        if (componentValues.Count != syntheticValues.Count || componentValues.Count < 10)
            return 0m;

        return CalculatePearsonCorrelation(componentValues, syntheticValues);
    }

    private decimal CalculateOverallCorrelation(List<SyntheticVixHistoricalData> historicalData)
    {
        // Calculate how well the synthetic VIX correlates with expected volatility patterns
        if (historicalData.Count < 20) return 0m;

        var values = historicalData.Select(h => h.NormalizedValue).ToList();
        var changes = new List<decimal>();

        for (int i = 1; i < values.Count; i++)
        {
            changes.Add(Math.Abs(values[i] - values[i - 1]));
        }

        // Higher correlation means more consistent behavior
        var avgChange = changes.Average();
        var volatilityOfChanges = changes.Sum(c => (c - avgChange) * (c - avgChange)) / changes.Count;

        return Math.Max(0, 1 - (decimal)Math.Sqrt((double)volatilityOfChanges) / avgChange);
    }

    private decimal CalculateCorrelationStability(List<SyntheticVixHistoricalData> historicalData)
    {
        // Measure how stable correlations are over time
        return 0.85m; // Placeholder for more complex calculation
    }

    private decimal CalculatePearsonCorrelation(List<decimal> x, List<decimal> y)
    {
        if (x.Count != y.Count || x.Count == 0) return 0m;

        var meanX = x.Average();
        var meanY = y.Average();

        var numerator = x.Zip(y, (xi, yi) => (xi - meanX) * (yi - meanY)).Sum();
        var denomX = (decimal)Math.Sqrt((double)x.Sum(xi => (xi - meanX) * (xi - meanX)));
        var denomY = (decimal)Math.Sqrt((double)y.Sum(yi => (yi - meanY) * (yi - meanY)));

        return denomX * denomY != 0 ? numerator / (denomX * denomY) : 0m;
    }

    private bool ValidateComponentPrice(string symbol, decimal price)
    {
        return symbol switch
        {
            "VXX" => price > 5 && price < 200,
            "UVXY" => price > 2 && price < 100,
            "SVXY" => price > 10 && price < 300,
            _ => price > 0
        };
    }

    private decimal CalculateSkewness(List<double> values)
    {
        if (values.Count < 3) return 0m;

        var mean = values.Average();
        var stdDev = Math.Sqrt(values.Sum(v => Math.Pow(v - mean, 2)) / values.Count);

        if (stdDev == 0) return 0m;

        var skewness = values.Sum(v => Math.Pow((v - mean) / stdDev, 3)) / values.Count;
        return (decimal)skewness;
    }

    private void UpdateComponentMetrics(SyntheticVixCalculation component)
    {
        _componentMetrics.AddOrUpdate(component.Symbol,
            new ComponentPerformanceMetrics
            {
                Symbol = component.Symbol,
                TotalCalculations = 1,
                AveragePrice = component.Price,
                AverageConfidence = component.Confidence,
                LastUpdate = component.Timestamp
            },
            (key, existing) =>
            {
                existing.TotalCalculations++;
                existing.AveragePrice = (existing.AveragePrice * (existing.TotalCalculations - 1) + component.Price) / existing.TotalCalculations;
                existing.AverageConfidence = (existing.AverageConfidence * (existing.TotalCalculations - 1) + component.Confidence) / existing.TotalCalculations;
                existing.LastUpdate = component.Timestamp;
                return existing;
            });
    }

    private void CheckForAlerts(SyntheticVixAnalysis analysis)
    {
        // Check for extreme values
        if (analysis.CurrentLevel > 80 || analysis.CurrentLevel < 5)
        {
            AddAlert(new SyntheticVixAlert
            {
                Id = Guid.NewGuid().ToString(),
                Type = "ExtremeValue",
                Severity = AlertSeverity.High,
                Message = $"SyntheticVIX at extreme level: {analysis.CurrentLevel:F2}",
                Timestamp = DateTime.UtcNow
            });
        }

        // Check for extreme z-scores
        if (Math.Abs(analysis.ZScore) > 3)
        {
            AddAlert(new SyntheticVixAlert
            {
                Id = Guid.NewGuid().ToString(),
                Type = "ExtremeZScore",
                Severity = AlertSeverity.Medium,
                Message = $"Extreme z-score detected: {analysis.ZScore:F2}",
                Timestamp = DateTime.UtcNow
            });
        }
    }

    private void AddAlert(SyntheticVixAlert alert)
    {
        _activeAlerts.Enqueue(alert);
        _logger.LogWarning("SyntheticVIX Alert: {Type} - {Message}", alert.Type, alert.Message);
    }
}
