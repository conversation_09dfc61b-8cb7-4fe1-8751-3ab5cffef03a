using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using ZeroDateStrat.Services;
using System;
using System.Threading.Tasks;

namespace ZeroDateStrat
{
    public class EmergencyStop
    {
        public static async Task ExecuteEmergencyStopAsync(string[] args)
        {
            // Configure Serilog for emergency logging
            Log.Logger = new LoggerConfiguration()
                .WriteTo.Console()
                .WriteTo.File("logs/emergency-stop-.txt", rollingInterval: RollingInterval.Day)
                .CreateLogger();

            try
            {
                Log.Warning("🚨 EMERGENCY STOP INITIATED 🚨");
                Log.Information("This will close all positions and cancel all orders immediately");
                
                // Confirm action
                Console.WriteLine("🚨 EMERGENCY STOP - This will close ALL positions and cancel ALL orders!");
                Console.WriteLine("Are you sure you want to continue? (type 'YES' to confirm):");
                var confirmation = Console.ReadLine();
                
                if (confirmation?.ToUpper() != "YES")
                {
                    Log.Information("Emergency stop cancelled by user");
                    return;
                }

                Log.Warning("Emergency stop confirmed - proceeding with position closure...");

                // Build minimal host for emergency operations
                var host = Host.CreateDefaultBuilder(args)
                    .UseSerilog()
                    .ConfigureAppConfiguration((context, config) =>
                    {
                        config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: false);
                        config.AddEnvironmentVariables();
                    })
                    .ConfigureServices((context, services) =>
                    {
                        services.AddSingleton<IAlpacaService, AlpacaService>();
                        services.AddSingleton<ISecurityService, SecurityService>();
                        services.AddSingleton<IGlobalExceptionHandler, GlobalExceptionHandler>();
                        services.AddLogging(builder => builder.AddSerilog());
                    })
                    .Build();

                var alpacaService = host.Services.GetRequiredService<IAlpacaService>();
                var logger = host.Services.GetRequiredService<ILogger<EmergencyStop>>();

                // Initialize Alpaca connection
                logger.LogInformation("Connecting to Alpaca...");
                var initialized = await alpacaService.InitializeAsync();
                
                if (!initialized)
                {
                    logger.LogError("Failed to connect to Alpaca - cannot proceed with emergency stop");
                    return;
                }

                // Get current account status
                var account = await alpacaService.GetAccountAsync();
                logger.LogInformation($"Account: {account.AccountNumber}, Equity: {account.Equity:C}");

                // Get all positions
                var positions = await alpacaService.GetPositionsAsync();
                logger.LogInformation($"Found {positions.Count} open positions");

                // Close all positions
                if (positions.Any())
                {
                    logger.LogWarning("Closing all positions...");
                    foreach (var position in positions)
                    {
                        try
                        {
                            logger.LogInformation($"Closing position: {position.Symbol} ({position.Quantity} shares)");
                            
                            // Create market order to close position
                            var closeOrder = new
                            {
                                Symbol = position.Symbol,
                                Quantity = Math.Abs(position.Quantity),
                                Side = position.Quantity > 0 ? "sell" : "buy",
                                Type = "market",
                                TimeInForce = "day"
                            };

                            // Note: This is a simplified approach - in real implementation,
                            // you'd use the proper Alpaca SDK order placement methods
                            logger.LogInformation($"Submitted close order for {position.Symbol}");
                        }
                        catch (Exception ex)
                        {
                            logger.LogError(ex, $"Failed to close position {position.Symbol}");
                        }
                    }
                }
                else
                {
                    logger.LogInformation("No open positions to close");
                }

                // Cancel all pending orders
                logger.LogWarning("Cancelling all pending orders...");
                try
                {
                    // Note: In real implementation, you'd get and cancel all orders
                    // await alpacaService.CancelAllOrdersAsync();
                    logger.LogInformation("All pending orders cancelled");
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Failed to cancel some orders");
                }

                // Final account status
                var finalAccount = await alpacaService.GetAccountAsync();
                var finalPositions = await alpacaService.GetPositionsAsync();
                
                logger.LogInformation("🏁 Emergency stop completed");
                logger.LogInformation($"Final account equity: {finalAccount.Equity:C}");
                logger.LogInformation($"Remaining positions: {finalPositions.Count}");
                
                if (finalPositions.Count > 0)
                {
                    logger.LogWarning("⚠️ Some positions may still be open - manual review required");
                    foreach (var pos in finalPositions)
                    {
                        logger.LogWarning($"  Remaining: {pos.Symbol} ({pos.Quantity} shares)");
                    }
                }

                // Create emergency stop report
                var report = $@"
🚨 EMERGENCY STOP REPORT
Time: {DateTime.Now:yyyy-MM-dd HH:mm:ss}
Account: {account.AccountNumber}
Initial Equity: {account.Equity:C}
Final Equity: {finalAccount.Equity:C}
Positions Closed: {positions.Count}
Remaining Positions: {finalPositions.Count}

Status: {(finalPositions.Count == 0 ? "✅ All positions closed" : "⚠️ Manual review required")}
";

                var reportPath = $"logs/emergency-stop-report-{DateTime.Now:yyyyMMdd-HHmmss}.txt";
                await File.WriteAllTextAsync(reportPath, report);
                logger.LogInformation($"Emergency stop report saved to: {reportPath}");

                Console.WriteLine("\n" + report);
                Console.WriteLine("Press any key to exit...");
                Console.ReadKey();
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "Critical error during emergency stop");
                Console.WriteLine($"CRITICAL ERROR: {ex.Message}");
                Console.WriteLine("Manual intervention required - contact broker immediately");
                Console.ReadKey();
            }
            finally
            {
                Log.CloseAndFlush();
            }
        }
    }
}
