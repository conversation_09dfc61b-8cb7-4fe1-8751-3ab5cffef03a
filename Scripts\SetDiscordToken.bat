@echo off
R<PERSON> Batch script to set Discord bot token as environment variable
REM Usage: <PERSON><PERSON>ts\SetDiscordToken.bat "YOUR_BOT_TOKEN_HERE"

if "%~1"=="" (
    echo ❌ Error: Bot token is required
    echo Usage: Scripts\SetDiscordToken.bat "MTM4MjU0NDg4MzI4NTQ5NTgwOA.G2atad.JHbCXK2yhoH4ENJC58u262lPh241Q4Vvf4xI9c"
    echo.
    echo This sets the DISCORD_BOT_TOKEN for AugmentBot (used for guidance requests)
    exit /b 1
)

echo 🔐 Setting Discord Environment Variables...

REM Set Discord Bot Token for current user
setx DISCORD_BOT_TOKEN "%~1" >nul
if %errorlevel% equ 0 (
    echo ✅ DISCORD_BOT_TOKEN set successfully
) else (
    echo ❌ Failed to set DISCORD_BOT_TOKEN
    exit /b 1
)

REM Set Discord Webhook URL if provided as second parameter
if not "%~2"=="" (
    setx DISCORD_WEBHOOK_URL "%~2" >nul
    if %errorlevel% equ 0 (
        echo ✅ DISCORD_WEBHOOK_URL set successfully
    ) else (
        echo ❌ Failed to set DISCORD_WEBHOOK_URL
    )
)

echo.
echo ⚠️  Important Notes:
echo    • Restart your IDE/terminal to pick up new environment variables
echo    • Environment variables take precedence over appsettings.json
echo    • Variables are set for current user only
echo.
echo 🧪 Test the configuration with:
echo    dotnet run discord
echo.
pause
