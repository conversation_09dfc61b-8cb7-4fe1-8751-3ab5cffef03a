# Discord Guidance Request Implementation

## Overview

This document describes the implementation of the Discord guidance request system for the ZeroDateStrat trading application. This system allows the trading bot to request external guidance from @ChatGptBot in Discord when it encounters tasks that require detailed instructions, coding logic, or design decisions.

## 🚀 Features Implemented

### 1. Core Components

- **GuidanceRequestService**: Main service for handling guidance requests
- **GuidanceRequest Models**: Comprehensive data models for requests and responses
- **Discord Integration**: Enhanced Discord service with ChatGptBot message handling
- **Response Storage**: Automatic storage and retrieval of guidance responses
- **Retry Logic**: Robust error handling with configurable retry attempts

### 2. Request Formats

#### Plaintext Format
Simple, human-readable requests:
```
@ChatGptBot I need instructions for building a C# class that scrapes the VIX index from Yahoo Finance and rebalances a SyntheticVIX model.
```

#### Structured JSON Format
Detailed, machine-readable requests:
```json
@ChatGptBot
{
  "intent": "request_instruction",
  "topic": "SyntheticVIX rebalance",
  "language": "C#",
  "components": ["Yahoo scraper", "OpenAI function call", "REST client"],
  "notes": "This is for use inside a 0DTE trading bot"
}
```

### 3. Key Features

- **Timeout Handling**: Configurable timeout periods (default: 30 seconds)
- **Retry Logic**: Up to 3 retry attempts for failed requests
- **Response Matching**: Intelligent matching of responses to pending requests
- **Storage System**: Automatic saving of responses to disk for reproducibility
- **Event System**: Events for response received, timeout, and failure scenarios
- **Configuration**: Flexible configuration through appsettings.json

## 📁 Files Created/Modified

### New Files
1. **Models/GuidanceRequestModels.cs** - Data models for guidance requests and responses
2. **Services/IGuidanceRequestService.cs** - Interface for guidance request functionality
3. **Services/GuidanceRequestService.cs** - Implementation of guidance request service
4. **Tests/GuidanceRequestTest.cs** - Comprehensive test suite
5. **DISCORD_GUIDANCE_REQUEST_IMPLEMENTATION.md** - This documentation

### Modified Files
1. **Services/DiscordService.cs** - Added ChatGptBot message handling
2. **Program.cs** - Added service registration and test command
3. **appsettings.json** - Added guidance request configuration

## 🔧 Configuration

### appsettings.json Configuration
```json
{
  "GuidanceRequest": {
    "Enabled": true,
    "ChatGptBotMention": "@ChatGptBot",
    "ChatGptBotUserId": "",
    "DefaultTimeout": "00:00:30",
    "DefaultMaxRetries": 3,
    "StoreResponses": true,
    "ResponseStoragePath": "GuidanceResponses",
    "LogRequests": true,
    "EnableRetryLogic": true
  }
}
```

### Configuration Options
- **Enabled**: Enable/disable the guidance request system
- **ChatGptBotMention**: The mention string to use for ChatGptBot
- **ChatGptBotUserId**: Discord user ID of ChatGptBot (optional)
- **DefaultTimeout**: Default timeout for waiting for responses
- **DefaultMaxRetries**: Maximum number of retry attempts
- **StoreResponses**: Whether to store responses to disk
- **ResponseStoragePath**: Directory path for storing responses
- **LogRequests**: Enable detailed logging of requests
- **EnableRetryLogic**: Enable automatic retry on timeout/failure

## 💻 Usage Examples

### 1. Simple Plaintext Request
```csharp
var guidanceService = serviceProvider.GetRequiredService<IGuidanceRequestService>();

var response = await guidanceService.RequestGuidanceAsync(
    taskStatement: "Create a VIX volatility calculator",
    language: "C#",
    components: new List<string> { "Math library", "Financial formulas" },
    notes: "For 0DTE options trading"
);

if (response != null)
{
    Console.WriteLine($"Received guidance: {response.Content}");
}
```

### 2. Structured JSON Request
```csharp
var structuredRequest = new StructuredGuidanceRequest
{
    Topic = "Options pricing model",
    Language = "C#",
    Components = new List<string> { "Black-Scholes", "Greeks calculation" },
    Requirements = new List<string> { "Real-time data", "High performance" },
    Notes = "Must integrate with Alpaca API"
};

var response = await guidanceService.RequestStructuredGuidanceAsync(structuredRequest);
```

### 3. Custom Request with Events
```csharp
// Subscribe to events
guidanceService.OnGuidanceResponseReceived += async (response) =>
{
    Console.WriteLine($"Got response: {response.Content}");
    // Process the guidance response
};

guidanceService.OnGuidanceRequestTimeout += async (request) =>
{
    Console.WriteLine($"Request {request.Id} timed out");
    // Handle timeout scenario
};

guidanceService.OnGuidanceRequestFailed += async (request, exception) =>
{
    Console.WriteLine($"Request {request.Id} failed: {exception.Message}");
    // Handle failure scenario
};

// Send custom request
var customRequest = new GuidanceRequest
{
    TaskStatement = "Create a risk management system",
    Language = "C#",
    Format = GuidanceRequestFormat.StructuredJson,
    ResponseTimeout = TimeSpan.FromSeconds(45),
    MaxRetries = 2
};

var response = await guidanceService.SendGuidanceRequestAsync(customRequest);
```

## 🧪 Testing

### Run Tests
```bash
# Run the guidance request test suite
dotnet run guidance-test
```

### Test Coverage
The test suite covers:
- Plaintext guidance requests
- Structured JSON guidance requests
- Custom guidance requests with full control
- Request status and storage functionality
- Error handling and timeout scenarios

## 🔄 Workflow

1. **Request Creation**: Create a guidance request with task details
2. **Message Formatting**: Format the request as plaintext or JSON
3. **Discord Sending**: Send the formatted message to Discord with @ChatGptBot mention
4. **Response Waiting**: Wait for ChatGptBot to respond (with timeout)
5. **Response Matching**: Match incoming messages to pending requests
6. **Response Storage**: Store the response for reproducibility
7. **Event Notification**: Trigger appropriate events for success/failure

## 🛡️ Error Handling

### Timeout Handling
- Configurable timeout periods (default: 30 seconds)
- Automatic retry logic with exponential backoff
- Timeout events for custom handling

### Failure Scenarios
- Network connectivity issues
- Discord service unavailable
- ChatGptBot not responding
- Invalid response format

### Retry Logic
- Up to 3 retry attempts by default
- Configurable retry count per request
- Exponential backoff between retries
- Detailed logging of retry attempts

## 📊 Monitoring and Logging

### Request Logging
- Detailed logging of all guidance requests
- Response matching and timing information
- Error and retry attempt logging
- Performance metrics tracking

### Storage and Retrieval
- Automatic storage of responses to disk
- JSON format for easy parsing
- Cleanup functionality for old responses
- Retrieval by request ID or date range

## 🔮 Future Enhancements

### Potential Improvements
1. **Advanced Response Matching**: Use message context and timing for better matching
2. **ChatGptBot User ID Verification**: Verify responses are actually from ChatGptBot
3. **Response Caching**: Cache similar requests to avoid duplicate queries
4. **Priority Queuing**: Priority system for urgent vs. routine requests
5. **Response Validation**: Validate response format and content quality
6. **Integration with Trading Logic**: Automatic execution of guidance responses
7. **Multi-Channel Support**: Support for multiple Discord channels
8. **Response Templates**: Pre-defined templates for common request types

### Integration Opportunities
- **Risk Management**: Request guidance for complex risk scenarios
- **Strategy Development**: Get help with new trading strategy implementation
- **Error Resolution**: Automatic guidance requests for critical errors
- **Performance Optimization**: Request optimization suggestions
- **Market Analysis**: Get external perspective on market conditions

## 📝 Notes

- The system is designed to be non-blocking and asynchronous
- All guidance requests are logged for audit purposes
- Response storage enables reproducibility and learning
- The system gracefully handles ChatGptBot unavailability
- Configuration allows for easy customization per environment
- Event-driven architecture enables flexible response handling

## 🚨 Important Considerations

1. **Rate Limiting**: Be mindful of Discord rate limits when sending requests
2. **Response Quality**: ChatGptBot responses should be validated before execution
3. **Security**: Never execute code directly from guidance responses without review
4. **Privacy**: Avoid sending sensitive trading data in guidance requests
5. **Dependency**: The system depends on ChatGptBot availability and responsiveness
