using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using ZeroDateStrat.Models;
using ZeroDateStrat.Services;

namespace ZeroDateStrat.Tests;

public class DiscordNotificationTest
{
    private readonly ILogger<DiscordNotificationTest> _logger;
    private readonly IServiceProvider _serviceProvider;

    public DiscordNotificationTest()
    {
        // Configure Serilog
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Information()
            .WriteTo.Console()
            .CreateLogger();

        // Build service provider
        var services = new ServiceCollection();
        
        // Add configuration
        var configuration = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: false)
            .Build();
        
        services.AddSingleton<IConfiguration>(configuration);
        services.AddLogging(builder => builder.AddSerilog());
        
        // Add Discord services
        services.AddSingleton<IDiscordService, DiscordService>();
        services.AddSingleton<ITradingNotificationService, TradingNotificationService>();
        
        // Add mock services for dependencies
        services.AddSingleton<IAlpacaService, MockAlpacaService>();
        services.AddSingleton<IPositionManager, MockPositionManager>();
        services.AddSingleton<IAdvancedRiskManager, MockAdvancedRiskManager>();
        
        _serviceProvider = services.BuildServiceProvider();
        _logger = _serviceProvider.GetRequiredService<ILogger<DiscordNotificationTest>>();
    }

    public async Task TestDiscordConnectionAsync()
    {
        _logger.LogInformation("🧪 Testing Discord connection...");

        try
        {
            var discordService = _serviceProvider.GetRequiredService<IDiscordService>();
            
            // Start Discord service
            await discordService.StartAsync();
            
            // Wait a moment for connection
            await Task.Delay(3000);
            
            // Check if connected
            var isConnected = await discordService.IsConnectedAsync();
            
            if (isConnected)
            {
                _logger.LogInformation("✅ Discord connection successful!");
                
                // Send test message
                await discordService.SendMessageAsync("🧪 **Discord Test Message**\n\nDiscord integration is working correctly!");
                
                _logger.LogInformation("✅ Test message sent successfully!");
            }
            else
            {
                _logger.LogWarning("❌ Discord connection failed");
            }
            
            await discordService.StopAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Discord test failed");
        }
    }

    public async Task TestTradingNotificationsAsync()
    {
        _logger.LogInformation("🧪 Testing trading notifications...");

        try
        {
            var notificationService = _serviceProvider.GetRequiredService<ITradingNotificationService>();
            var discordService = _serviceProvider.GetRequiredService<IDiscordService>();
            
            // Start Discord service
            await discordService.StartAsync();
            await Task.Delay(3000);
            
            if (await discordService.IsConnectedAsync())
            {
                _logger.LogInformation("✅ Discord connected, testing notifications...");
                
                // Test startup notification
                await notificationService.SendSystemStartupNotificationAsync();
                await Task.Delay(1000);
                
                // Test morning report
                await notificationService.SendMorningReportAsync();
                await Task.Delay(1000);
                
                // Test trade notification
                var testSignal = new TradingSignal
                {
                    Id = Guid.NewGuid().ToString(),
                    Strategy = "Test Strategy",
                    UnderlyingSymbol = "SPX",
                    MaxLoss = -150.00m,
                    Legs = new List<OptionLeg>
                    {
                        new OptionLeg
                        {
                            Symbol = "SPX241211C4700",
                            Side = Models.OrderSide.Sell,
                            Quantity = 1,
                            Price = 2.50m
                        }
                    }
                };
                
                await notificationService.SendTradeExecutionNotificationAsync(testSignal, null, true);
                await Task.Delay(1000);
                
                // Test market status update
                await notificationService.SendMarketStatusUpdateAsync("Open", 25.5m, "Normal Volatility");
                await Task.Delay(1000);
                
                // Test end of day report
                await notificationService.SendEndOfDayReportAsync();
                await Task.Delay(1000);
                
                // Test shutdown notification
                await notificationService.SendSystemShutdownNotificationAsync();
                
                _logger.LogInformation("✅ All notification tests completed successfully!");
            }
            else
            {
                _logger.LogWarning("❌ Discord not connected, skipping notification tests");
            }
            
            await discordService.StopAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Trading notification test failed");
        }
    }

    public static async Task Main(string[] args)
    {
        var test = new DiscordNotificationTest();
        
        Console.WriteLine("🚀 Starting Discord Notification Tests...\n");
        
        await test.TestDiscordConnectionAsync();
        await Task.Delay(2000);
        
        await test.TestTradingNotificationsAsync();
        
        Console.WriteLine("\n✅ All tests completed!");
        Console.WriteLine("Press any key to exit...");
        Console.ReadKey();
    }
}

// Mock services for testing
public class MockAlpacaService : IAlpacaService
{
    public Task<bool> InitializeAsync() => Task.FromResult(true);
    
    public Task<Alpaca.Markets.IAccount?> GetAccountAsync()
    {
        // Return mock account data
        return Task.FromResult<Alpaca.Markets.IAccount?>(new MockAccount());
    }
    
    public Task<List<OptionContract>> GetOptionChainAsync(string symbol, DateTime expirationDate) => 
        Task.FromResult(new List<OptionContract>());
    
    public Task<decimal> GetCurrentPriceAsync(string symbol) => Task.FromResult(4700m);
    
    public Task<Alpaca.Markets.IOrder?> PlaceOrderAsync(TradingSignal signal) => 
        Task.FromResult<Alpaca.Markets.IOrder?>(null);
    
    public Task<List<Alpaca.Markets.IPosition>> GetPositionsAsync() => 
        Task.FromResult(new List<Alpaca.Markets.IPosition>());
    
    public Task<bool> ClosePositionAsync(string positionId) => Task.FromResult(true);
    
    public Task<List<Alpaca.Markets.IOrder>> GetOrdersAsync() => 
        Task.FromResult(new List<Alpaca.Markets.IOrder>());
    
    public Task<List<AlpacaHistoricalBar>> GetHistoricalDataAsync(string symbol, DateTime startDate, DateTime endDate) => 
        Task.FromResult(new List<AlpacaHistoricalBar>());
    
    public Task<Alpaca.Markets.IOrder?> PlaceBracketOrderAsync(TradingSignal signal, decimal profitTarget, decimal stopLoss) => 
        Task.FromResult<Alpaca.Markets.IOrder?>(null);
    
    public Task<bool> ModifyOrderAsync(Guid orderId, decimal newPrice) => Task.FromResult(true);
    
    public Task<bool> CancelOrderAsync(Guid orderId) => Task.FromResult(true);
    
    public Task<List<Alpaca.Markets.IOrder>> GetActiveOrdersAsync() => 
        Task.FromResult(new List<Alpaca.Markets.IOrder>());
    
    public Task<decimal> GetPositionValueAsync(string symbol) => Task.FromResult(0m);
    
    public Task<Dictionary<string, decimal>> GetPortfolioGreeksAsync() => 
        Task.FromResult(new Dictionary<string, decimal>());
    
    public Task<bool> CloseAllPositionsAsync() => Task.FromResult(true);
    
    public Task<List<OptionContract>> GetRealTimeOptionChainAsync(string symbol, DateTime expirationDate) => 
        Task.FromResult(new List<OptionContract>());
}

public class MockAccount : Alpaca.Markets.IAccount
{
    public Guid AccountId { get; } = Guid.NewGuid();
    public string AccountNumber { get; } = "*********";
    public decimal Equity { get; } = 12035m;
    public decimal LastEquity { get; } = 12000m;
    public decimal BuyingPower { get; } = 10000m;
    public decimal DayTradingBuyingPower { get; } = 10000m;
    public int DayTradeCount { get; } = 0;
    public bool IsDayTradingAccount { get; } = true;
    public Alpaca.Markets.AccountStatus Status { get; } = Alpaca.Markets.AccountStatus.Active;
    public bool IsTradingBlocked { get; } = false;
    public bool IsTransferBlocked { get; } = false;
    public bool IsAccountBlocked { get; } = false;
    public DateTime CreatedAt { get; } = DateTime.Now.AddYears(-1);
    public string Currency { get; } = "USD";
    public decimal Cash { get; } = 2000m;
    public decimal LongMarketValue { get; } = 10035m;
    public decimal ShortMarketValue { get; } = 0m;
    public decimal PortfolioValue { get; } = 12035m;
    public decimal InitialMargin { get; } = 0m;
    public decimal MaintenanceMargin { get; } = 0m;
    public decimal RegTBuyingPower { get; } = 10000m;
    public decimal DayTradingExcess { get; } = 0m;
    public decimal SmaValue { get; } = 12035m;
    public bool IsPatternDayTrader { get; } = false;
    public decimal TradingBlocked { get; } = 0m;
    public decimal TransferBlocked { get; } = 0m;
    public decimal AccountBlocked { get; } = 0m;
    public decimal PendingTransferOut { get; } = 0m;
    public decimal PendingTransferIn { get; } = 0m;
    public decimal AccruedFees { get; } = 0m;
    public decimal UnsettledFunds { get; } = 0m;
}

public class MockPositionManager : IPositionManager
{
    public Task<PortfolioSnapshot> GetPortfolioSnapshotAsync()
    {
        return Task.FromResult(new PortfolioSnapshot
        {
            Timestamp = DateTime.Now,
            TotalValue = 12035m,
            DayPnL = 35m,
            TotalPnL = 2035m,
            BuyingPower = 10000m,
            ActivePositions = 0,
            GreeksBySymbol = new Dictionary<string, decimal>(),
            TopPositions = new List<PositionSummary>()
        });
    }
    
    public Task<List<Models.ManagedPosition>> GetActivePositionsAsync() => 
        Task.FromResult(new List<Models.ManagedPosition>());
    
    public Task<bool> OpenPositionAsync(TradingSignal signal) => Task.FromResult(true);
    
    public Task<bool> ClosePositionAsync(string positionId) => Task.FromResult(true);
    
    public Task<bool> AdjustPositionAsync(string positionId, decimal newStopLoss, decimal newProfitTarget) => 
        Task.FromResult(true);
    
    public Task<Models.ManagedPosition?> GetPositionAsync(string positionId) => 
        Task.FromResult<Models.ManagedPosition?>(null);
    
    public Task UpdatePositionValuesAsync() => Task.CompletedTask;
    
    public Task<bool> ShouldClosePositionAsync(Models.ManagedPosition position) => Task.FromResult(false);
    
    public Task<decimal> CalculatePositionValueAsync(Models.ManagedPosition position) => Task.FromResult(0m);
    
    public Task<decimal> CalculatePositionPnLAsync(Models.ManagedPosition position) => Task.FromResult(0m);
}

public class MockAdvancedRiskManager : IAdvancedRiskManager
{
    public Task<RiskAssessment> AssessPortfolioRiskAsync() => 
        Task.FromResult(new RiskAssessment());
    
    public Task<bool> ValidateTradeRiskAsync(TradingSignal signal) => Task.FromResult(true);
    
    public Task<decimal> CalculateOptimalPositionSizeAsync(TradingSignal signal) => Task.FromResult(1500m);
    
    public Task<Models.RiskMetrics> GetRealTimeRiskMetricsAsync()
    {
        return Task.FromResult(new Models.RiskMetrics
        {
            Timestamp = DateTime.UtcNow,
            PortfolioValue = 12035m,
            AvailableBuyingPower = 10000m,
            MaxDrawdown = 180m,
            LargestPositionSize = 1500m
        });
    }
    
    public Task<bool> CheckDrawdownLimitsAsync() => Task.FromResult(true);
    
    public Task<List<RiskAlert>> GetActiveRiskAlertsAsync() => 
        Task.FromResult(new List<RiskAlert>());
    
    public Task<bool> ShouldHaltTradingAsync() => Task.FromResult(false);
    
    public Task<GreeksRiskAssessment> AssessGreeksRiskAsync() => 
        Task.FromResult(new GreeksRiskAssessment());
    
    public Task<ConcentrationRisk> AssessConcentrationRiskAsync() => 
        Task.FromResult(new ConcentrationRisk());
    
    public Task<LiquidityRisk> AssessLiquidityRiskAsync() => 
        Task.FromResult(new LiquidityRisk());
    
    public Task<decimal> GetDailyPnLAsync() => Task.FromResult(35m);
}
