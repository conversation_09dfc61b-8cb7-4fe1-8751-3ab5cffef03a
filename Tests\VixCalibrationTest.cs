using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using ZeroDateStrat.Services;
using ZeroDateStrat.Models;

namespace ZeroDateStrat.Tests;

/// <summary>
/// Test program to verify VIX calibration system functionality
/// Tests real Polygon VIX data as primary with synthetic VIX fallback and calibration tuning
/// </summary>
public class VixCalibrationTest
{
    public static async Task Main(string[] args)
    {
        Console.WriteLine("🔧 VIX Calibration System Test");
        Console.WriteLine("===============================");
        Console.WriteLine();

        try
        {
            // Build host with all required services
            var host = CreateHost();
            
            // Get services
            var alpacaVixService = host.Services.GetRequiredService<IAlpacaVixService>();
            var polygonDataService = host.Services.GetRequiredService<IPolygonDataService>();
            var syntheticVixService = host.Services.GetRequiredService<ISyntheticVixService>();
            var calibrationService = host.Services.GetRequiredService<IVixCalibrationService>();
            var logger = host.Services.GetRequiredService<ILogger<VixCalibrationTest>>();

            Console.WriteLine("✅ Services initialized successfully");
            Console.WriteLine();

            // Test 1: Real VIX Data Retrieval
            Console.WriteLine("📊 Test 1: Real VIX Data Retrieval");
            Console.WriteLine("----------------------------------");
            
            var realVix = await polygonDataService.GetCurrentVixAsync();
            Console.WriteLine($"Real VIX from Polygon: {realVix:F2}");
            
            if (realVix > 0)
            {
                Console.WriteLine("✅ Real VIX data retrieval successful");
            }
            else
            {
                Console.WriteLine("❌ Real VIX data retrieval failed");
            }
            Console.WriteLine();

            // Test 2: Synthetic VIX Calculation
            Console.WriteLine("🧮 Test 2: Synthetic VIX Calculation");
            Console.WriteLine("------------------------------------");
            
            var syntheticVix = await syntheticVixService.GetCurrentSyntheticVixAsync();
            Console.WriteLine($"Synthetic VIX: {syntheticVix:F2}");
            
            if (syntheticVix > 0)
            {
                Console.WriteLine("✅ Synthetic VIX calculation successful");
            }
            else
            {
                Console.WriteLine("❌ Synthetic VIX calculation failed");
            }
            Console.WriteLine();

            // Test 3: Primary VIX Service (should use real VIX as primary)
            Console.WriteLine("🎯 Test 3: Primary VIX Service Priority");
            Console.WriteLine("---------------------------------------");
            
            var primaryVix = await alpacaVixService.GetCurrentVixAsync();
            Console.WriteLine($"Primary VIX Service Result: {primaryVix:F2}");
            
            if (realVix > 0 && Math.Abs(primaryVix - realVix) < 0.01m)
            {
                Console.WriteLine("✅ Primary service correctly using real VIX data");
            }
            else if (syntheticVix > 0 && Math.Abs(primaryVix - syntheticVix) < 0.01m)
            {
                Console.WriteLine("⚠️ Primary service using synthetic VIX (real VIX may be unavailable)");
            }
            else
            {
                Console.WriteLine("❌ Primary service result unexpected");
            }
            Console.WriteLine();

            // Test 4: Calibration System
            if (realVix > 0 && syntheticVix > 0)
            {
                Console.WriteLine("⚙️ Test 4: VIX Calibration System");
                Console.WriteLine("---------------------------------");
                
                // Perform calibration
                var calibrationResult = await calibrationService.CalibrateAsync(realVix, syntheticVix);
                
                Console.WriteLine($"Calibration Results:");
                Console.WriteLine($"  Real VIX: {calibrationResult.RealVix:F2}");
                Console.WriteLine($"  Synthetic VIX: {calibrationResult.SyntheticVix:F2}");
                Console.WriteLine($"  Calibrated VIX: {calibrationResult.CalibratedVix:F2}");
                Console.WriteLine($"  Bias: {calibrationResult.Bias:F3}");
                Console.WriteLine($"  Scaling Factor: {calibrationResult.ScalingFactor:F3}");
                Console.WriteLine($"  Correlation: {calibrationResult.Correlation:F3}");
                Console.WriteLine($"  Significant Drift: {calibrationResult.IsSignificantDrift}");
                
                if (Math.Abs(calibrationResult.CalibratedVix - realVix) < Math.Abs(syntheticVix - realVix))
                {
                    Console.WriteLine("✅ Calibration improved synthetic VIX accuracy");
                }
                else
                {
                    Console.WriteLine("⚠️ Calibration did not improve accuracy (may need more data)");
                }
                Console.WriteLine();

                // Test 5: Calibration Report
                Console.WriteLine("📋 Test 5: Calibration Report");
                Console.WriteLine("-----------------------------");
                
                var report = await calibrationService.GetCalibrationReportAsync();
                Console.WriteLine($"Calibration Status: {report.Status}");
                Console.WriteLine($"Data Points: {report.DataPoints}");
                Console.WriteLine($"Recent Data Points: {report.RecentDataPoints}");
                Console.WriteLine($"Average Bias: {report.AverageBias:F3}");
                Console.WriteLine($"Scaling Factor: {report.CurrentScalingFactor:F3}");
                Console.WriteLine($"Correlation: {report.CorrelationCoefficient:F3}");
                Console.WriteLine($"Average Error: {report.AverageAbsoluteError:F2}");
                Console.WriteLine($"Recommend Recalibration: {report.RecommendRecalibration}");
                Console.WriteLine();

                // Test 6: Calibration Recommendations
                Console.WriteLine("💡 Test 6: Calibration Recommendations");
                Console.WriteLine("--------------------------------------");
                
                var recommendations = await calibrationService.GetRecommendationsAsync();
                Console.WriteLine("Recommendations:");
                foreach (var recommendation in recommendations.Recommendations)
                {
                    Console.WriteLine($"  • {recommendation}");
                }
                Console.WriteLine();

                // Test 7: Apply Calibration to Synthetic VIX
                Console.WriteLine("🔧 Test 7: Apply Calibration");
                Console.WriteLine("----------------------------");
                
                var statusBefore = await syntheticVixService.GetCalibrationStatusAsync();
                Console.WriteLine($"Before Calibration - Bias: {statusBefore.CurrentBias:F3}, Scaling: {statusBefore.CurrentScalingFactor:F3}");
                
                await syntheticVixService.ApplyCalibrationAsync(calibrationResult.Bias, calibrationResult.ScalingFactor);
                
                var statusAfter = await syntheticVixService.GetCalibrationStatusAsync();
                Console.WriteLine($"After Calibration - Bias: {statusAfter.CurrentBias:F3}, Scaling: {statusAfter.CurrentScalingFactor:F3}");
                
                var calibratedSyntheticVix = await syntheticVixService.GetCurrentSyntheticVixAsync();
                Console.WriteLine($"New Calibrated Synthetic VIX: {calibratedSyntheticVix:F2}");
                
                if (statusAfter.IsCalibrated)
                {
                    Console.WriteLine("✅ Calibration applied successfully");
                }
                else
                {
                    Console.WriteLine("❌ Calibration application failed");
                }
                Console.WriteLine();
            }
            else
            {
                Console.WriteLine("⚠️ Skipping calibration tests - insufficient VIX data");
                Console.WriteLine();
            }

            // Test 8: Connection Tests
            Console.WriteLine("🔗 Test 8: Connection Tests");
            Console.WriteLine("---------------------------");
            
            var alpacaTest = await alpacaVixService.TestConnectionAsync();
            var polygonTest = await polygonDataService.TestConnectionAsync();
            var syntheticTest = await syntheticVixService.TestConnectionAsync();
            
            Console.WriteLine($"Alpaca VIX Service: {(alpacaTest ? "✅ Connected" : "❌ Failed")}");
            Console.WriteLine($"Polygon Data Service: {(polygonTest ? "✅ Connected" : "❌ Failed")}");
            Console.WriteLine($"Synthetic VIX Service: {(syntheticTest ? "✅ Connected" : "❌ Failed")}");
            Console.WriteLine();

            Console.WriteLine("🎉 VIX Calibration System Test Complete!");
            Console.WriteLine();
            Console.WriteLine("Summary:");
            Console.WriteLine($"  • Real VIX: {(realVix > 0 ? $"{realVix:F2}" : "Unavailable")}");
            Console.WriteLine($"  • Synthetic VIX: {(syntheticVix > 0 ? $"{syntheticVix:F2}" : "Unavailable")}");
            Console.WriteLine($"  • Primary Service: {(primaryVix > 0 ? $"{primaryVix:F2}" : "Unavailable")}");
            Console.WriteLine($"  • Calibration: {(realVix > 0 && syntheticVix > 0 ? "Tested" : "Skipped")}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Test failed with error: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }

        Console.WriteLine();
        Console.WriteLine("Press any key to exit...");
        Console.ReadKey();
    }

    private static IHost CreateHost()
    {
        return Host.CreateDefaultBuilder()
            .ConfigureAppConfiguration((context, config) =>
            {
                config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
            })
            .ConfigureServices((context, services) =>
            {
                // Add logging
                services.AddLogging(builder =>
                {
                    builder.AddConsole();
                    builder.SetMinimumLevel(LogLevel.Information);
                });

                // Add configuration
                services.AddSingleton<IConfiguration>(context.Configuration);

                // Add core services
                services.AddSingleton<IPolygonDataService, PolygonDataService>();
                services.AddSingleton<IAlpacaService, AlpacaService>();
                
                // Add VIX services
                services.AddSingleton<SyntheticVixService>();
                services.AddSingleton<ISyntheticVixService>(provider => provider.GetRequiredService<SyntheticVixService>());
                services.AddSingleton<ISyntheticVixDataProvider>(provider => provider.GetRequiredService<SyntheticVixService>());
                services.AddSingleton<IVixCalibrationService, VixCalibrationService>();
                services.AddSingleton<IAlpacaVixService, AlpacaVixService>();

                // Add HttpClient for external services
                services.AddHttpClient<IPolygonDataService, PolygonDataService>();
            })
            .Build();
    }
}
