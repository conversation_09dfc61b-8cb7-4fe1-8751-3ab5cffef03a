using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using System.Text.Json;
using ILogger = Serilog.ILogger;

namespace ZeroDateStrat.Tests;

public static class PolygonOptionsEndpointTest
{
    public static async Task RunPolygonOptionsEndpointTest()
    {
        // Configure Serilog for testing
        Log.Logger = new LoggerConfiguration()
            .WriteTo.Console()
            .WriteTo.File($"logs/polygon-options-endpoint-test-{DateTime.Now:yyyyMMdd-HHmmss}.txt")
            .CreateLogger();

        var logger = Log.Logger;

        try
        {
            logger.Information("🔍 Starting Comprehensive Polygon.io Options Endpoint Test");
            logger.Information("Testing all available options endpoints based on official documentation");

            // Build host with HTTP client
            var host = Host.CreateDefaultBuilder()
                .UseSerilog()
                .ConfigureAppConfiguration((context, config) =>
                {
                    config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
                    config.AddEnvironmentVariables();
                })
                .ConfigureServices((context, services) =>
                {
                    services.AddHttpClient();
                    services.AddLogging(builder => builder.AddSerilog());
                })
                .Build();

            var httpClientFactory = host.Services.GetRequiredService<IHttpClientFactory>();
            var httpClient = httpClientFactory.CreateClient();
            var configuration = host.Services.GetRequiredService<IConfiguration>();
            
            var apiKey = configuration["Polygon:ApiKey"];
            if (string.IsNullOrEmpty(apiKey))
            {
                logger.Error("❌ Polygon API key not found in configuration");
                logger.Error("Expected configuration key: Polygon:ApiKey");
                return;
            }

            logger.Information($"Using Polygon API Key: {apiKey[..4]}...{apiKey[^4..]}");

            // Test 1: Option Chain Snapshot (NEW - Most Important!)
            logger.Information("\n🔍 Testing: Option Chain Snapshot (COMPREHENSIVE)");
            await TestOptionChainSnapshot(httpClient, apiKey, "SPY", logger);

            // Test 2: Individual Option Contract Snapshot
            logger.Information("\n🔍 Testing: Option Contract Snapshot");
            await TestOptionContractSnapshot(httpClient, apiKey, "SPY", logger);

            // Test 3: Options Aggregates (OHLC Bars)
            logger.Information("\n🔍 Testing: Options Aggregates (OHLC Bars)");
            await TestOptionsAggregates(httpClient, apiKey, logger);

            // Test 4: Options Trades (Historical)
            logger.Information("\n🔍 Testing: Options Trades");
            await TestOptionsTrades(httpClient, apiKey, logger);

            // Test 5: Options Quotes (Historical)
            logger.Information("\n🔍 Testing: Options Quotes");
            await TestOptionsQuotes(httpClient, apiKey, logger);

            // Test 6: Options Contracts with Filters
            logger.Information("\n🔍 Testing: Options Contracts with Advanced Filters");
            await TestOptionsContractsAdvanced(httpClient, apiKey, logger);

            logger.Information("\n✅ Comprehensive Polygon.io Options Endpoint Test Completed!");
            logger.Information("📊 Check results above to see which endpoints provide the data we need");

        }
        catch (Exception ex)
        {
            logger.Error(ex, "❌ Error during Polygon.io options endpoint test");
        }
        finally
        {
            Log.CloseAndFlush();
        }
    }

    private static async Task TestOptionChainSnapshot(HttpClient httpClient, string apiKey, string symbol, ILogger logger)
    {
        try
        {
            // Test the most comprehensive endpoint - Option Chain Snapshot
            var url = $"https://api.polygon.io/v3/snapshot/options/{symbol}?apikey={apiKey}";
            logger.Information($"📡 Endpoint: /v3/snapshot/options/{symbol}");
            
            var response = await httpClient.GetAsync(url);
            var content = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
            {
                logger.Information($"✅ Status: {response.StatusCode} - SUCCESS");
                
                // Parse and analyze the response
                var jsonDoc = JsonDocument.Parse(content);
                if (jsonDoc.RootElement.TryGetProperty("results", out var results) && results.ValueKind == JsonValueKind.Array)
                {
                    var contractCount = results.GetArrayLength();
                    logger.Information($"📊 Found {contractCount} option contracts in chain");
                    
                    if (contractCount > 0)
                    {
                        var firstContract = results[0];
                        logger.Information("📄 Sample Contract Data:");
                        
                        if (firstContract.TryGetProperty("details", out var details))
                        {
                            if (details.TryGetProperty("ticker", out var ticker))
                                logger.Information($"  Ticker: {ticker.GetString()}");
                            if (details.TryGetProperty("strike_price", out var strike))
                                logger.Information($"  Strike: ${strike.GetDecimal()}");
                            if (details.TryGetProperty("contract_type", out var type))
                                logger.Information($"  Type: {type.GetString()}");
                        }
                        
                        if (firstContract.TryGetProperty("last_quote", out var lastQuote))
                        {
                            if (lastQuote.TryGetProperty("bid", out var bid))
                                logger.Information($"  Bid: ${bid.GetDecimal()}");
                            if (lastQuote.TryGetProperty("ask", out var ask))
                                logger.Information($"  Ask: ${ask.GetDecimal()}");
                        }
                        
                        if (firstContract.TryGetProperty("implied_volatility", out var iv))
                            logger.Information($"  Implied Vol: {iv.GetDecimal():P2}");
                        
                        if (firstContract.TryGetProperty("greeks", out var greeks))
                        {
                            if (greeks.TryGetProperty("delta", out var delta))
                                logger.Information($"  Delta: {delta.GetDecimal():F3}");
                        }
                    }
                }
                
                logger.Information($"📄 Response Preview: {content[..Math.Min(200, content.Length)]}...");
            }
            else
            {
                logger.Warning($"❌ Status: {response.StatusCode} - {response.ReasonPhrase}");
                logger.Warning($"📄 Error Details: {content[..Math.Min(200, content.Length)]}...");
            }
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error testing option chain snapshot");
        }
    }

    private static async Task TestOptionContractSnapshot(HttpClient httpClient, string apiKey, string symbol, ILogger logger)
    {
        try
        {
            // Test individual contract snapshot - need a specific contract ticker
            var contractTicker = "O:SPY250611C00600000"; // SPY June 11, 2025 $600 Call
            var url = $"https://api.polygon.io/v3/snapshot/options/{symbol}/{contractTicker}?apikey={apiKey}";
            logger.Information($"📡 Endpoint: /v3/snapshot/options/{symbol}/{contractTicker}");
            
            var response = await httpClient.GetAsync(url);
            var content = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
            {
                logger.Information($"✅ Status: {response.StatusCode} - SUCCESS");
                logger.Information($"📄 Response Preview: {content[..Math.Min(300, content.Length)]}...");
            }
            else
            {
                logger.Warning($"❌ Status: {response.StatusCode} - {response.ReasonPhrase}");
                logger.Warning($"📄 Error Details: {content[..Math.Min(200, content.Length)]}...");
            }
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error testing option contract snapshot");
        }
    }

    private static async Task TestOptionsAggregates(HttpClient httpClient, string apiKey, ILogger logger)
    {
        try
        {
            // Test options aggregates (OHLC bars)
            var contractTicker = "O:SPY250611C00600000";
            var fromDate = DateTime.Today.AddDays(-5).ToString("yyyy-MM-dd");
            var toDate = DateTime.Today.ToString("yyyy-MM-dd");
            var url = $"https://api.polygon.io/v2/aggs/ticker/{contractTicker}/range/1/day/{fromDate}/{toDate}?apikey={apiKey}";
            logger.Information($"📡 Endpoint: /v2/aggs/ticker/{contractTicker}/range/1/day/{fromDate}/{toDate}");
            
            var response = await httpClient.GetAsync(url);
            var content = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
            {
                logger.Information($"✅ Status: {response.StatusCode} - SUCCESS");
                logger.Information($"📄 Response Preview: {content[..Math.Min(300, content.Length)]}...");
            }
            else
            {
                logger.Warning($"❌ Status: {response.StatusCode} - {response.ReasonPhrase}");
                logger.Warning($"📄 Error Details: {content[..Math.Min(200, content.Length)]}...");
            }
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error testing options aggregates");
        }
    }

    private static async Task TestOptionsTrades(HttpClient httpClient, string apiKey, ILogger logger)
    {
        try
        {
            // Test options trades
            var contractTicker = "O:SPY250611C00600000";
            var url = $"https://api.polygon.io/v3/trades/{contractTicker}?limit=10&apikey={apiKey}";
            logger.Information($"📡 Endpoint: /v3/trades/{contractTicker}");
            
            var response = await httpClient.GetAsync(url);
            var content = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
            {
                logger.Information($"✅ Status: {response.StatusCode} - SUCCESS");
                logger.Information($"📄 Response Preview: {content[..Math.Min(300, content.Length)]}...");
            }
            else
            {
                logger.Warning($"❌ Status: {response.StatusCode} - {response.ReasonPhrase}");
                logger.Warning($"📄 Error Details: {content[..Math.Min(200, content.Length)]}...");
            }
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error testing options trades");
        }
    }

    private static async Task TestOptionsQuotes(HttpClient httpClient, string apiKey, ILogger logger)
    {
        try
        {
            // Test options quotes
            var contractTicker = "O:SPY250611C00600000";
            var url = $"https://api.polygon.io/v3/quotes/{contractTicker}?limit=10&apikey={apiKey}";
            logger.Information($"📡 Endpoint: /v3/quotes/{contractTicker}");
            
            var response = await httpClient.GetAsync(url);
            var content = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
            {
                logger.Information($"✅ Status: {response.StatusCode} - SUCCESS");
                logger.Information($"📄 Response Preview: {content[..Math.Min(300, content.Length)]}...");
            }
            else
            {
                logger.Warning($"❌ Status: {response.StatusCode} - {response.ReasonPhrase}");
                logger.Warning($"📄 Error Details: {content[..Math.Min(200, content.Length)]}...");
            }
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error testing options quotes");
        }
    }

    private static async Task TestOptionsContractsAdvanced(HttpClient httpClient, string apiKey, ILogger logger)
    {
        try
        {
            // Test options contracts with advanced filters
            var today = DateTime.Today.ToString("yyyy-MM-dd");
            var url = $"https://api.polygon.io/v3/reference/options/contracts?underlying_ticker=SPY&expiration_date={today}&limit=50&apikey={apiKey}";
            logger.Information($"📡 Endpoint: /v3/reference/options/contracts (0 DTE Filter)");
            
            var response = await httpClient.GetAsync(url);
            var content = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
            {
                logger.Information($"✅ Status: {response.StatusCode} - SUCCESS");
                
                var jsonDoc = JsonDocument.Parse(content);
                if (jsonDoc.RootElement.TryGetProperty("results", out var results) && results.ValueKind == JsonValueKind.Array)
                {
                    var contractCount = results.GetArrayLength();
                    logger.Information($"📊 Found {contractCount} 0 DTE contracts for today ({today})");
                }
                
                logger.Information($"📄 Response Preview: {content[..Math.Min(300, content.Length)]}...");
            }
            else
            {
                logger.Warning($"❌ Status: {response.StatusCode} - {response.ReasonPhrase}");
                logger.Warning($"📄 Error Details: {content[..Math.Min(200, content.Length)]}...");
            }
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error testing advanced options contracts");
        }
    }
}
