using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using ZeroDateStrat.Models;
using ZeroDateStrat.Services;

namespace ZeroDateStrat.Tests;

public class VixTermStructureServiceTests
{
    private readonly Mock<IPolygonDataService> _mockPolygonService;
    private readonly Mock<ILogger<VixTermStructureService>> _mockLogger;
    private readonly Mock<IConfiguration> _mockConfiguration;
    private readonly VixTermStructureService _service;

    public VixTermStructureServiceTests()
    {
        _mockPolygonService = new Mock<IPolygonDataService>();
        _mockLogger = new Mock<ILogger<VixTermStructureService>>();
        _mockConfiguration = new Mock<IConfiguration>();

        _service = new VixTermStructureService(
            _mockPolygonService.Object,
            _mockLogger.Object,
            _mockConfiguration.Object);
    }

    [Fact]
    public async Task GetCurrentTermStructureAsync_WithValidData_ReturnsCompleteStructure()
    {
        // Arrange
        var mockVixSnapshot = new PolygonIndexSnapshot { Symbol = "I:VIX", Value = 18.5m };
        var mockVix9DSnapshot = new PolygonIndexSnapshot { Symbol = "I:VIX9D", Value = 17.2m };
        var mockVix3MSnapshot = new PolygonIndexSnapshot { Symbol = "I:VIX3M", Value = 20.1m };

        _mockPolygonService.Setup(x => x.GetIndexSnapshotAsync("I:VIX"))
            .ReturnsAsync(mockVixSnapshot);
        _mockPolygonService.Setup(x => x.GetIndexSnapshotAsync("I:VIX9D"))
            .ReturnsAsync(mockVix9DSnapshot);
        _mockPolygonService.Setup(x => x.GetIndexSnapshotAsync("I:VIX3M"))
            .ReturnsAsync(mockVix3MSnapshot);

        // Act
        var result = await _service.GetCurrentTermStructureAsync();

        // Assert
        Assert.True(result.IsDataComplete);
        Assert.Equal(18.5m, result.Vix);
        Assert.Equal(17.2m, result.Vix9D);
        Assert.Equal(20.1m, result.Vix3M);
        Assert.Equal(VolatilityRegimeType.NormalContango, result.RegimeType);
        Assert.True(result.Contango > 0);
        Assert.Equal(0, result.Backwardation);
    }

    [Fact]
    public async Task GetCurrentTermStructureAsync_WithBackwardation_ReturnsBackwardationRegime()
    {
        // Arrange - VIX9D > VIX (short-term stress)
        var mockVixSnapshot = new PolygonIndexSnapshot { Symbol = "I:VIX", Value = 22.0m };
        var mockVix9DSnapshot = new PolygonIndexSnapshot { Symbol = "I:VIX9D", Value = 25.0m };
        var mockVix3MSnapshot = new PolygonIndexSnapshot { Symbol = "I:VIX3M", Value = 21.0m };

        _mockPolygonService.Setup(x => x.GetIndexSnapshotAsync("I:VIX"))
            .ReturnsAsync(mockVixSnapshot);
        _mockPolygonService.Setup(x => x.GetIndexSnapshotAsync("I:VIX9D"))
            .ReturnsAsync(mockVix9DSnapshot);
        _mockPolygonService.Setup(x => x.GetIndexSnapshotAsync("I:VIX3M"))
            .ReturnsAsync(mockVix3MSnapshot);

        // Act
        var result = await _service.GetCurrentTermStructureAsync();

        // Assert
        Assert.True(result.IsDataComplete);
        Assert.Equal(VolatilityRegimeType.Backwardation, result.RegimeType);
        Assert.True(result.Backwardation > 0);
    }

    [Fact]
    public async Task GetCurrentTermStructureAsync_WithInvertedStructure_ReturnsInvertedRegime()
    {
        // Arrange - VIX9D > VIX > VIX3M (extreme stress)
        var mockVixSnapshot = new PolygonIndexSnapshot { Symbol = "I:VIX", Value = 30.0m };
        var mockVix9DSnapshot = new PolygonIndexSnapshot { Symbol = "I:VIX9D", Value = 35.0m };
        var mockVix3MSnapshot = new PolygonIndexSnapshot { Symbol = "I:VIX3M", Value = 25.0m };

        _mockPolygonService.Setup(x => x.GetIndexSnapshotAsync("I:VIX"))
            .ReturnsAsync(mockVixSnapshot);
        _mockPolygonService.Setup(x => x.GetIndexSnapshotAsync("I:VIX9D"))
            .ReturnsAsync(mockVix9DSnapshot);
        _mockPolygonService.Setup(x => x.GetIndexSnapshotAsync("I:VIX3M"))
            .ReturnsAsync(mockVix3MSnapshot);

        // Act
        var result = await _service.GetCurrentTermStructureAsync();

        // Assert
        Assert.True(result.IsDataComplete);
        Assert.Equal(VolatilityRegimeType.InvertedStructure, result.RegimeType);
        Assert.True(result.Backwardation > 0);
    }

    [Fact]
    public async Task GetTermStructureAnalysisAsync_WithValidData_ReturnsAnalysis()
    {
        // Arrange
        var mockVixSnapshot = new PolygonIndexSnapshot { Symbol = "I:VIX", Value = 19.0m };
        var mockVix9DSnapshot = new PolygonIndexSnapshot { Symbol = "I:VIX9D", Value = 18.0m };
        var mockVix3MSnapshot = new PolygonIndexSnapshot { Symbol = "I:VIX3M", Value = 21.0m };

        _mockPolygonService.Setup(x => x.GetIndexSnapshotAsync("I:VIX"))
            .ReturnsAsync(mockVixSnapshot);
        _mockPolygonService.Setup(x => x.GetIndexSnapshotAsync("I:VIX9D"))
            .ReturnsAsync(mockVix9DSnapshot);
        _mockPolygonService.Setup(x => x.GetIndexSnapshotAsync("I:VIX3M"))
            .ReturnsAsync(mockVix3MSnapshot);

        // Act
        var result = await _service.GetTermStructureAnalysisAsync();

        // Assert
        Assert.True(result.CurrentStructure.IsDataComplete);
        Assert.Equal(2.0m, result.VolatilityRiskPremium); // VIX3M - VIX
        Assert.Equal(1.0m, result.ShortTermStress); // VIX - VIX9D
        Assert.Contains("Normal market conditions", result.MarketImplication);
        Assert.True(result.Confidence > 0);
    }

    [Fact]
    public async Task GetVolatilityRiskPremiumAsync_WithValidData_ReturnsCorrectPremium()
    {
        // Arrange
        var mockVixSnapshot = new PolygonIndexSnapshot { Symbol = "I:VIX", Value = 18.0m };
        var mockVix9DSnapshot = new PolygonIndexSnapshot { Symbol = "I:VIX9D", Value = 17.0m };
        var mockVix3MSnapshot = new PolygonIndexSnapshot { Symbol = "I:VIX3M", Value = 22.0m };

        _mockPolygonService.Setup(x => x.GetIndexSnapshotAsync("I:VIX"))
            .ReturnsAsync(mockVixSnapshot);
        _mockPolygonService.Setup(x => x.GetIndexSnapshotAsync("I:VIX9D"))
            .ReturnsAsync(mockVix9DSnapshot);
        _mockPolygonService.Setup(x => x.GetIndexSnapshotAsync("I:VIX3M"))
            .ReturnsAsync(mockVix3MSnapshot);

        // Act
        var result = await _service.GetVolatilityRiskPremiumAsync();

        // Assert
        Assert.Equal(4.0m, result); // VIX3M (22) - VIX (18)
    }

    [Fact]
    public async Task IsTermStructureInvertedAsync_WithBackwardation_ReturnsTrue()
    {
        // Arrange
        var mockVixSnapshot = new PolygonIndexSnapshot { Symbol = "I:VIX", Value = 25.0m };
        var mockVix9DSnapshot = new PolygonIndexSnapshot { Symbol = "I:VIX9D", Value = 28.0m };
        var mockVix3MSnapshot = new PolygonIndexSnapshot { Symbol = "I:VIX3M", Value = 23.0m };

        _mockPolygonService.Setup(x => x.GetIndexSnapshotAsync("I:VIX"))
            .ReturnsAsync(mockVixSnapshot);
        _mockPolygonService.Setup(x => x.GetIndexSnapshotAsync("I:VIX9D"))
            .ReturnsAsync(mockVix9DSnapshot);
        _mockPolygonService.Setup(x => x.GetIndexSnapshotAsync("I:VIX3M"))
            .ReturnsAsync(mockVix3MSnapshot);

        // Act
        var result = await _service.IsTermStructureInvertedAsync();

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsTermStructureInvertedAsync_WithNormalContango_ReturnsFalse()
    {
        // Arrange
        var mockVixSnapshot = new PolygonIndexSnapshot { Symbol = "I:VIX", Value = 18.0m };
        var mockVix9DSnapshot = new PolygonIndexSnapshot { Symbol = "I:VIX9D", Value = 17.0m };
        var mockVix3MSnapshot = new PolygonIndexSnapshot { Symbol = "I:VIX3M", Value = 20.0m };

        _mockPolygonService.Setup(x => x.GetIndexSnapshotAsync("I:VIX"))
            .ReturnsAsync(mockVixSnapshot);
        _mockPolygonService.Setup(x => x.GetIndexSnapshotAsync("I:VIX9D"))
            .ReturnsAsync(mockVix9DSnapshot);
        _mockPolygonService.Setup(x => x.GetIndexSnapshotAsync("I:VIX3M"))
            .ReturnsAsync(mockVix3MSnapshot);

        // Act
        var result = await _service.IsTermStructureInvertedAsync();

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task TestConnectionAsync_WithValidPolygonService_ReturnsTrue()
    {
        // Arrange
        var mockVixSnapshot = new PolygonIndexSnapshot { Symbol = "I:VIX", Value = 18.0m };
        var mockVix9DSnapshot = new PolygonIndexSnapshot { Symbol = "I:VIX9D", Value = 17.0m };
        var mockVix3MSnapshot = new PolygonIndexSnapshot { Symbol = "I:VIX3M", Value = 20.0m };

        _mockPolygonService.Setup(x => x.GetIndexSnapshotAsync("I:VIX"))
            .ReturnsAsync(mockVixSnapshot);
        _mockPolygonService.Setup(x => x.GetIndexSnapshotAsync("I:VIX9D"))
            .ReturnsAsync(mockVix9DSnapshot);
        _mockPolygonService.Setup(x => x.GetIndexSnapshotAsync("I:VIX3M"))
            .ReturnsAsync(mockVix3MSnapshot);

        // Act
        var result = await _service.TestConnectionAsync();

        // Assert
        Assert.True(result);
    }
}
