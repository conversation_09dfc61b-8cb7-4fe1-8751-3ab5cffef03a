using Microsoft.Extensions.Logging;
using ZeroDateStrat.Models;

namespace ZeroDateStrat.Services;

public interface ITradingCalendarService
{
    bool IsZeroDteAvailable(string symbol, DateTime date);
    DateTime? GetNextZeroDteDate(string symbol, DateTime fromDate);
    List<DateTime> GetZeroDteDates(string symbol, DateTime startDate, DateTime endDate);
    bool IsMarketOpen(DateTime dateTime);
    bool IsMarketHoliday(DateTime date);
}

public class TradingCalendarService : ITradingCalendarService
{
    private readonly ILogger<TradingCalendarService> _logger;
    private readonly HashSet<DateTime> _marketHolidays;

    public TradingCalendarService(ILogger<TradingCalendarService> logger)
    {
        _logger = logger;
        _marketHolidays = InitializeMarketHolidays();
    }

    public bool IsZeroDteAvailable(string symbol, DateTime date)
    {
        // Check if market is open
        if (!IsMarketOpen(date) || IsMarketHoliday(date))
            return false;

        var dayOfWeek = date.DayOfWeek;

        return symbol.ToUpper() switch
        {
            "SPY" => IsSpyZeroDteDay(dayOfWeek),
            "SPX" => IsSpxZeroDteDay(dayOfWeek),
            "QQQ" => IsQqqZeroDteDay(dayOfWeek),
            "IWM" => IsIwmZeroDteDay(dayOfWeek),
            _ => false
        };
    }

    public DateTime? GetNextZeroDteDate(string symbol, DateTime fromDate)
    {
        var currentDate = fromDate.Date;
        var maxDaysToCheck = 14; // Check up to 2 weeks ahead
        
        for (int i = 0; i < maxDaysToCheck; i++)
        {
            var checkDate = currentDate.AddDays(i);
            if (IsZeroDteAvailable(symbol, checkDate))
            {
                return checkDate;
            }
        }

        return null;
    }

    public List<DateTime> GetZeroDteDates(string symbol, DateTime startDate, DateTime endDate)
    {
        var dates = new List<DateTime>();
        var currentDate = startDate.Date;

        while (currentDate <= endDate.Date)
        {
            if (IsZeroDteAvailable(symbol, currentDate))
            {
                dates.Add(currentDate);
            }
            currentDate = currentDate.AddDays(1);
        }

        return dates;
    }

    public bool IsMarketOpen(DateTime dateTime)
    {
        var dayOfWeek = dateTime.DayOfWeek;
        
        // Market is closed on weekends
        if (dayOfWeek == DayOfWeek.Saturday || dayOfWeek == DayOfWeek.Sunday)
            return false;

        // Check if it's a holiday
        if (IsMarketHoliday(dateTime.Date))
            return false;

        // Check market hours (9:30 AM - 4:00 PM ET)
        var marketOpen = new TimeSpan(9, 30, 0);
        var marketClose = new TimeSpan(16, 0, 0);
        var currentTime = dateTime.TimeOfDay;

        return currentTime >= marketOpen && currentTime <= marketClose;
    }

    public bool IsMarketHoliday(DateTime date)
    {
        return _marketHolidays.Contains(date.Date);
    }

    private bool IsSpyZeroDteDay(DayOfWeek dayOfWeek)
    {
        // SPY 0 DTE: Monday, Wednesday, Friday
        return dayOfWeek == DayOfWeek.Monday || 
               dayOfWeek == DayOfWeek.Wednesday || 
               dayOfWeek == DayOfWeek.Friday;
    }

    private bool IsSpxZeroDteDay(DayOfWeek dayOfWeek)
    {
        // SPX 0 DTE: Tuesday, Thursday (and some Mondays/Wednesdays)
        return dayOfWeek == DayOfWeek.Tuesday || 
               dayOfWeek == DayOfWeek.Thursday;
    }

    private bool IsQqqZeroDteDay(DayOfWeek dayOfWeek)
    {
        // QQQ 0 DTE: Monday, Wednesday, Friday (same as SPY)
        return dayOfWeek == DayOfWeek.Monday || 
               dayOfWeek == DayOfWeek.Wednesday || 
               dayOfWeek == DayOfWeek.Friday;
    }

    private bool IsIwmZeroDteDay(DayOfWeek dayOfWeek)
    {
        // IWM 0 DTE: Monday, Wednesday, Friday (same as SPY)
        return dayOfWeek == DayOfWeek.Monday || 
               dayOfWeek == DayOfWeek.Wednesday || 
               dayOfWeek == DayOfWeek.Friday;
    }

    private HashSet<DateTime> InitializeMarketHolidays()
    {
        var holidays = new HashSet<DateTime>();
        
        // 2025 Market Holidays
        holidays.Add(new DateTime(2025, 1, 1));   // New Year's Day
        holidays.Add(new DateTime(2025, 1, 20));  // Martin Luther King Jr. Day
        holidays.Add(new DateTime(2025, 2, 17));  // Presidents' Day
        holidays.Add(new DateTime(2025, 4, 18));  // Good Friday
        holidays.Add(new DateTime(2025, 5, 26));  // Memorial Day
        holidays.Add(new DateTime(2025, 6, 19));  // Juneteenth
        holidays.Add(new DateTime(2025, 7, 4));   // Independence Day
        holidays.Add(new DateTime(2025, 9, 1));   // Labor Day
        holidays.Add(new DateTime(2025, 11, 27)); // Thanksgiving
        holidays.Add(new DateTime(2025, 12, 25)); // Christmas

        // 2026 Market Holidays (for forward planning)
        holidays.Add(new DateTime(2026, 1, 1));   // New Year's Day
        holidays.Add(new DateTime(2026, 1, 19));  // Martin Luther King Jr. Day
        holidays.Add(new DateTime(2026, 2, 16));  // Presidents' Day
        holidays.Add(new DateTime(2026, 4, 3));   // Good Friday
        holidays.Add(new DateTime(2026, 5, 25));  // Memorial Day
        holidays.Add(new DateTime(2026, 6, 19));  // Juneteenth
        holidays.Add(new DateTime(2026, 7, 3));   // Independence Day (observed)
        holidays.Add(new DateTime(2026, 9, 7));   // Labor Day
        holidays.Add(new DateTime(2026, 11, 26)); // Thanksgiving
        holidays.Add(new DateTime(2026, 12, 25)); // Christmas

        return holidays;
    }
}
