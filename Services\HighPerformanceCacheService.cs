using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace ZeroDateStrat.Services;

public interface IHighPerformanceCacheService
{
    Task<T?> GetAsync<T>(string key);
    Task SetAsync<T>(string key, T value, TimeSpan? expiration = null);
    Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiration = null);
    void Remove(string key);
    void Clear();
    CacheStatistics GetStatistics();
}

public class CacheStatistics
{
    public long HitCount { get; set; }
    public long MissCount { get; set; }
    public long SetCount { get; set; }
    public double HitRatio => HitCount + MissCount > 0 ? (double)HitCount / (HitCount + MissCount) : 0;
    public long TotalMemoryUsage { get; set; }
    public int EntryCount { get; set; }
}

public class HighPerformanceCacheService : IHighPerformanceCacheService
{
    private readonly IMemoryCache _cache;
    private readonly ILogger<HighPerformanceCacheService> _logger;
    private readonly ConcurrentDictionary<string, object> _lockObjects;
    private readonly CacheStatistics _statistics;
    private long _hitCount;
    private long _missCount;
    private long _setCount;
    private readonly PerformanceCounter? _memoryCounter;
    private ICacheAnalyticsService? _analyticsService;
    private IPredictiveCachingService? _predictiveService;

    public HighPerformanceCacheService(IMemoryCache cache, ILogger<HighPerformanceCacheService> logger)
    {
        _cache = cache;
        _logger = logger;
        _lockObjects = new ConcurrentDictionary<string, object>();
        _statistics = new CacheStatistics();

        // Initialize performance counter for memory monitoring (Windows only)
        try
        {
            _memoryCounter = new PerformanceCounter("Process", "Working Set - Private", Process.GetCurrentProcess().ProcessName);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Could not initialize memory performance counter");
        }
    }

    // Method to inject analytics and predictive services after DI container is built
    public void SetAnalyticsService(ICacheAnalyticsService analyticsService)
    {
        _analyticsService = analyticsService;
    }

    public void SetPredictiveService(IPredictiveCachingService predictiveService)
    {
        _predictiveService = predictiveService;
    }

    public async Task<T?> GetAsync<T>(string key)
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        try
        {
            var dataType = typeof(T).Name;

            if (_cache.TryGetValue(key, out var cachedValue))
            {
                Interlocked.Increment(ref _hitCount);
                stopwatch.Stop();

                _analyticsService?.RecordCacheHit(key, dataType, stopwatch.ElapsedMilliseconds);
                _predictiveService?.RegisterAccessPattern(key, DateTime.UtcNow);

                _logger.LogDebug("Cache HIT for key: {Key} in {Ms}ms", key, stopwatch.ElapsedMilliseconds);
                return (T?)cachedValue;
            }

            Interlocked.Increment(ref _missCount);
            stopwatch.Stop();

            _analyticsService?.RecordCacheMiss(key, dataType, stopwatch.ElapsedMilliseconds);

            _logger.LogDebug("Cache MISS for key: {Key} in {Ms}ms", key, stopwatch.ElapsedMilliseconds);
            return default(T);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error retrieving from cache for key: {Key}", key);
            return default(T);
        }
    }

    public async Task SetAsync<T>(string key, T value, TimeSpan? expiration = null)
    {
        try
        {
            var options = new MemoryCacheEntryOptions
            {
                Size = EstimateObjectSize(value),
                Priority = GetCachePriority<T>()
            };

            if (expiration.HasValue)
            {
                options.AbsoluteExpirationRelativeToNow = expiration.Value;
            }
            else
            {
                // Default expiration based on data type
                options.AbsoluteExpirationRelativeToNow = GetDefaultExpiration<T>();
            }

            // Add eviction callback for statistics
            options.PostEvictionCallbacks.Add(new PostEvictionCallbackRegistration
            {
                EvictionCallback = (evictedKey, evictedValue, reason, state) =>
                {
                    _analyticsService?.RecordCacheEviction(evictedKey.ToString() ?? "", reason.ToString());
                    _logger.LogDebug("Cache entry evicted: {Key}, Reason: {Reason}", evictedKey, reason);
                }
            });

            _cache.Set(key, value, options);
            Interlocked.Increment(ref _setCount);

            var dataType = typeof(T).Name;
            _analyticsService?.RecordCacheSet(key, dataType, options.Size ?? 0);

            _logger.LogDebug("Cache SET for key: {Key}, Size: {Size} bytes", key, options.Size);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting cache for key: {Key}", key);
        }
    }

    public async Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiration = null)
    {
        // First try to get from cache
        var cachedValue = await GetAsync<T>(key);
        if (cachedValue != null)
        {
            return cachedValue;
        }

        // Use lock to prevent multiple threads from executing the factory for the same key
        var lockObject = _lockObjects.GetOrAdd(key, _ => new object());
        
        try
        {
            // Double-check pattern
            cachedValue = await GetAsync<T>(key);
            if (cachedValue != null)
            {
                return cachedValue;
            }

            // Execute factory and cache result
            var stopwatch = Stopwatch.StartNew();
            var value = await factory();
            stopwatch.Stop();

            _logger.LogDebug("Factory execution for key {Key} took {ElapsedMs}ms", key, stopwatch.ElapsedMilliseconds);

            await SetAsync(key, value, expiration);
            return value;
        }
        finally
        {
            _lockObjects.TryRemove(key, out _);
        }
    }

    public void Remove(string key)
    {
        try
        {
            _cache.Remove(key);
            _logger.LogDebug("Cache REMOVE for key: {Key}", key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing from cache for key: {Key}", key);
        }
    }

    public void Clear()
    {
        try
        {
            if (_cache is MemoryCache memoryCache)
            {
                memoryCache.Compact(1.0); // Remove all entries
            }
            _logger.LogInformation("Cache cleared");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing cache");
        }
    }

    public CacheStatistics GetStatistics()
    {
        try
        {
            _statistics.TotalMemoryUsage = (long)(_memoryCounter?.NextValue() ?? 0);
            _statistics.HitCount = _hitCount;
            _statistics.MissCount = _missCount;
            _statistics.SetCount = _setCount;
            
            // Estimate entry count (this is approximate)
            if (_cache is MemoryCache memoryCache)
            {
                var field = typeof(MemoryCache).GetField("_coherentState", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                if (field?.GetValue(memoryCache) is object coherentState)
                {
                    var entriesCollection = coherentState.GetType()
                        .GetProperty("EntriesCollection", System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);
                    if (entriesCollection?.GetValue(coherentState) is System.Collections.IDictionary entries)
                    {
                        _statistics.EntryCount = entries.Count;
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error getting cache statistics");
        }

        return _statistics;
    }

    private static long EstimateObjectSize<T>(T obj)
    {
        // Enhanced size estimation for better cache management with 6GB limit
        return obj switch
        {
            string str => str.Length * 2, // Unicode characters
            decimal => 16,
            double => 8,
            float => 4,
            int => 4,
            long => 8,
            DateTime => 8,
            byte[] bytes => bytes.Length,
            // Trading-specific object size estimates
            _ when typeof(T).Name.Contains("OptionChain") => 50 * 1024, // 50KB for options chains
            _ when typeof(T).Name.Contains("MarketData") => 2 * 1024,   // 2KB for market data
            _ when typeof(T).Name.Contains("Position") => 1024,         // 1KB for positions
            _ when typeof(T).Name.Contains("Signal") => 512,            // 512B for signals
            _ when typeof(T).Name.Contains("Quote") => 256,             // 256B for quotes
            _ => 1024 // Default estimate for complex objects
        };
    }

    private static TimeSpan GetDefaultExpiration<T>()
    {
        // Enhanced expiration times for larger cache - can afford longer retention
        return typeof(T).Name switch
        {
            // Real-time data - short expiration
            "VixData" => TimeSpan.FromMinutes(1),
            "MarketData" => TimeSpan.FromSeconds(30),
            "Quote" => TimeSpan.FromSeconds(15),

            // Semi-static data - medium expiration
            "OptionsChain" => TimeSpan.FromMinutes(10), // Increased from 5 to 10
            "VolatilityData" => TimeSpan.FromMinutes(15),
            "MarketRegime" => TimeSpan.FromMinutes(5),

            // Static/slow-changing data - long expiration
            "AccountInfo" => TimeSpan.FromMinutes(30), // Increased from 10 to 30
            "Position" => TimeSpan.FromMinutes(20),
            "TradingSignal" => TimeSpan.FromMinutes(15),
            "HistoricalData" => TimeSpan.FromHours(1), // Historical data can be cached for 1 hour

            // Configuration and reference data - very long expiration
            "SymbolInfo" => TimeSpan.FromHours(4),
            "ExchangeInfo" => TimeSpan.FromHours(6),

            _ => TimeSpan.FromMinutes(10) // Increased default from 5 to 10 minutes
        };
    }

    private static CacheItemPriority GetCachePriority<T>()
    {
        // Assign cache priorities based on data importance and access frequency
        return typeof(T).Name switch
        {
            // High priority - critical trading data
            "VixData" => CacheItemPriority.High,
            "MarketData" => CacheItemPriority.High,
            "Quote" => CacheItemPriority.High,
            "Position" => CacheItemPriority.High,
            "AccountInfo" => CacheItemPriority.High,

            // Normal priority - frequently accessed data
            "OptionsChain" => CacheItemPriority.Normal,
            "TradingSignal" => CacheItemPriority.Normal,
            "MarketRegime" => CacheItemPriority.Normal,
            "VolatilityData" => CacheItemPriority.Normal,

            // Low priority - reference data and historical data
            "HistoricalData" => CacheItemPriority.Low,
            "SymbolInfo" => CacheItemPriority.Low,
            "ExchangeInfo" => CacheItemPriority.Low,

            // Never remove - critical system data
            "Configuration" => CacheItemPriority.NeverRemove,

            _ => CacheItemPriority.Normal
        };
    }
}
