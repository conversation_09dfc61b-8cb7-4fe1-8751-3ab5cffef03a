using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Serilog;
using ZeroDateStrat.Services;

namespace ZeroDateStrat.Tests;

/// <summary>
/// Test suite for the separate ChatGPT Discord bot
/// </summary>
public static class ChatGPTDiscordBotTest
{
    public static async Task RunAsync()
    {
        Console.WriteLine("🤖 Starting ChatGPT Discord Bot Test...\n");

        // Configure Serilog for testing
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Information()
            .WriteTo.Console()
            .CreateLogger();

        try
        {
            Console.WriteLine("Test is running...");

            // Load configuration
            var configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json", optional: false)
                .AddEnvironmentVariables()
                .Build();

            // Setup DI container
            var services = new ServiceCollection();
            services.AddSingleton<IConfiguration>(configuration);
            services.AddLogging(builder => builder.AddSerilog());
            services.AddHttpClient<IOpenAIService, OpenAIService>();
            services.AddSingleton<IChatGPTDiscordBot, ChatGPTDiscordBot>();

            var serviceProvider = services.BuildServiceProvider();

            Console.WriteLine("✅ Services initialized successfully\n");

            // Test 1: Check configuration
            Console.WriteLine("🔍 Test 1: Checking ChatGPT Discord bot configuration...");
            var chatGPTBotConfig = configuration.GetSection("ChatGPTBot");
            var enabled = chatGPTBotConfig.GetValue<bool>("Enabled", false);
            var botToken = Environment.GetEnvironmentVariable("CHATGPT_BOT_TOKEN") 
                          ?? chatGPTBotConfig.GetValue<string>("BotToken");
            var channelId = chatGPTBotConfig.GetValue<ulong>("ChannelId", 0);

            Console.WriteLine($"   ChatGPT Bot Enabled: {(enabled ? "✅ Yes" : "❌ No")}");
            Console.WriteLine($"   Bot Token Configured: {(!string.IsNullOrEmpty(botToken) ? "✅ Yes" : "❌ No")}");
            Console.WriteLine($"   Channel ID Configured: {(channelId > 0 ? "✅ Yes" : "❌ No")}");

            if (!enabled)
            {
                Console.WriteLine("⚠️ ChatGPT Discord bot is disabled in configuration");
                return;
            }

            if (string.IsNullOrEmpty(botToken))
            {
                Console.WriteLine("⚠️ ChatGPT bot token not configured. Set CHATGPT_BOT_TOKEN environment variable or configure in appsettings.json");
                return;
            }

            if (channelId == 0)
            {
                Console.WriteLine("⚠️ Channel ID not configured in ChatGPTBot section");
                return;
            }

            // Test 2: Check OpenAI service availability
            Console.WriteLine("\n🔍 Test 2: Checking OpenAI service availability...");
            var openAIService = serviceProvider.GetRequiredService<IOpenAIService>();
            var isAvailable = await openAIService.IsAvailableAsync();
            Console.WriteLine($"   OpenAI Service Available: {(isAvailable ? "✅ Yes" : "❌ No")}");

            if (!isAvailable)
            {
                Console.WriteLine("⚠️ OpenAI service is not available. Please check your API key configuration.");
                return;
            }

            // Test 3: Test bot initialization (without actually connecting)
            Console.WriteLine("\n🔍 Test 3: Testing bot initialization...");
            var chatGPTBot = serviceProvider.GetRequiredService<IChatGPTDiscordBot>();
            Console.WriteLine("   ✅ ChatGPT Discord bot service created successfully");

            // Test 4: Test configuration values
            Console.WriteLine("\n🔍 Test 4: Testing configuration values...");
            var username = chatGPTBotConfig.GetValue<string>("Username", "ChatGPTBot");
            var enableMentionTrigger = chatGPTBotConfig.GetValue<bool>("EnableMentionTrigger", true);
            var enableKeywordTrigger = chatGPTBotConfig.GetValue<bool>("EnableKeywordTrigger", true);
            var enablePriorityTagging = chatGPTBotConfig.GetValue<bool>("EnablePriorityTagging", true);
            var enableResponsePagination = chatGPTBotConfig.GetValue<bool>("EnableResponsePagination", true);

            Console.WriteLine($"   Username: {username}");
            Console.WriteLine($"   Mention Trigger: {(enableMentionTrigger ? "✅ Enabled" : "❌ Disabled")}");
            Console.WriteLine($"   Keyword Trigger: {(enableKeywordTrigger ? "✅ Enabled" : "❌ Disabled")}");
            Console.WriteLine($"   Priority Tagging: {(enablePriorityTagging ? "✅ Enabled" : "❌ Disabled")}");
            Console.WriteLine($"   Response Pagination: {(enableResponsePagination ? "✅ Enabled" : "❌ Disabled")}");

            // Test 5: Test trigger keywords
            Console.WriteLine("\n🔍 Test 5: Testing trigger keywords...");
            var triggerKeywords = chatGPTBotConfig.GetSection("TriggerKeywords").Get<List<string>>() ?? new List<string>();
            Console.WriteLine($"   Trigger Keywords: {string.Join(", ", triggerKeywords)}");

            // Test 6: Test OpenAI integration
            Console.WriteLine("\n🔍 Test 6: Testing OpenAI integration...");
            var testPrompt = "What is 0 DTE options trading in one sentence?";
            var response = await openAIService.GetChatCompletionAsync(testPrompt);

            if (response?.Success == true)
            {
                Console.WriteLine("   ✅ OpenAI integration test successful!");
                Console.WriteLine($"   Response preview: {response.Content.Substring(0, Math.Min(100, response.Content.Length))}...");
                Console.WriteLine($"   Tokens used: {response.TokensUsed}");
                Console.WriteLine($"   Model: {response.Model}");
            }
            else
            {
                Console.WriteLine("   ❌ OpenAI integration test failed");
                Console.WriteLine($"   Error: {response?.ErrorMessage}");
            }

            Console.WriteLine("\n🎉 ChatGPT Discord Bot Test completed!");
            Console.WriteLine("\n📋 Next Steps:");
            Console.WriteLine("1. Create a separate Discord bot application at https://discord.com/developers/applications");
            Console.WriteLine("2. Get the bot token and set CHATGPT_BOT_TOKEN environment variable");
            Console.WriteLine("3. Invite the bot to your Discord server with appropriate permissions");
            Console.WriteLine("4. Start the application to run both bots simultaneously");
            Console.WriteLine("\n💡 Usage Examples:");
            Console.WriteLine("   @ChatGptBot How do I create a Yahoo Finance VIX scraper in C#?");
            Console.WriteLine("   !askchatgpt What is the best way to handle options data?");
            Console.WriteLine("   !chatgpt [urgent] Explain the Black-Scholes model");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Test failed with error: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
        finally
        {
            Log.CloseAndFlush();
        }
    }
}
