using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using ZeroDateStrat.Services;

namespace ZeroDateStrat.Tests;

/// <summary>
/// Test for ChatGPT bot functionality
/// </summary>
public class ChatGPTBotTest
{
    public static async Task RunAsync()
    {
        Console.WriteLine("🤖 Starting ChatGPT Bot Test...\n");
        Console.WriteLine("Test is running...");

        // Setup logging
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Information()
            .WriteTo.Console()
            .CreateLogger();

        try
        {
            // Build configuration
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false)
                .AddEnvironmentVariables()
                .Build();

            // Setup DI container for OpenAI service only
            var services = new ServiceCollection();
            services.AddSingleton<IConfiguration>(configuration);
            services.AddLogging(builder => builder.AddSerilog());
            services.AddHttpClient<IOpenAIService, OpenAIService>();

            var serviceProvider = services.BuildServiceProvider();

            // Get OpenAI service
            var openAIService = serviceProvider.GetRequiredService<IOpenAIService>();

            Console.WriteLine("✅ Services initialized successfully\n");

            // Test 1: Check OpenAI service availability
            Console.WriteLine("🔍 Test 1: Checking OpenAI service availability...");
            var isAvailable = await openAIService.IsAvailableAsync();
            Console.WriteLine($"   OpenAI Service Available: {(isAvailable ? "✅ Yes" : "❌ No")}");

            if (!isAvailable)
            {
                Console.WriteLine("⚠️ OpenAI service is not available. Please check your API key configuration.");
                Console.WriteLine("   Set OPENAI_API_KEY environment variable or configure it in appsettings.json");
                return;
            }

            // Test 2: Test prompt extraction (without full DI)
            Console.WriteLine("\n🔍 Test 2: Testing prompt extraction...");

            var testMessages = new[]
            {
                "@ChatGptBot How can I implement a VIX scraper in C#?",
                "!askchatgpt What is the best way to handle options data?",
                "@ChatGPT\n{\n  \"intent\": \"request_instruction\",\n  \"topic\": \"SyntheticVIX rebalance\",\n  \"language\": \"C#\"\n}",
                "!chatgpt Explain the Black-Scholes model"
            };

            foreach (var testMessage in testMessages)
            {
                var extractedPrompt2 = ExtractPromptSimple(testMessage);
                Console.WriteLine($"   Input: {testMessage.Replace("\n", "\\n")}");
                Console.WriteLine($"   Extracted: {extractedPrompt2 ?? "null"}");
                Console.WriteLine();
            }

            // Test 3: Test actual OpenAI API call
            Console.WriteLine("🔍 Test 3: Testing OpenAI API call...");
            var testPrompt = "Explain what 0 DTE options trading means in one sentence.";

            var response = await openAIService.GetChatCompletionAsync(testPrompt);

            if (response?.Success == true)
            {
                Console.WriteLine("✅ OpenAI API call successful!");
                Console.WriteLine($"   Response: {response.Content.Substring(0, Math.Min(100, response.Content.Length))}...");
                Console.WriteLine($"   Tokens used: {response.TokensUsed}");
                Console.WriteLine($"   Model: {response.Model}");
            }
            else
            {
                Console.WriteLine($"❌ OpenAI API call failed: {response?.ErrorMessage}");
            }

            // Test 4: Test response formatting
            Console.WriteLine("\n🔍 Test 4: Testing response formatting...");
            var longResponse = new string('A', 2500); // Create a response longer than Discord limit
            var formattedResponse4 = FormatResponseSimple(longResponse, 2000);

            Console.WriteLine($"   Original length: {longResponse.Length}");
            Console.WriteLine($"   Formatted length: {formattedResponse4.Length}");
            Console.WriteLine($"   Contains truncation notice: {formattedResponse4.Contains("[Response truncated")}");

            // Test 5: Test end-to-end flow
            Console.WriteLine("\n🔍 Test 5: Testing end-to-end flow...");
            var testRequest = "!chatgpt What is the difference between put and call options?";
            var extractedPrompt5 = ExtractPromptSimple(testRequest);

            if (!string.IsNullOrWhiteSpace(extractedPrompt5))
            {
                var botResponse = await openAIService.GetChatCompletionAsync(extractedPrompt5);
                if (botResponse?.Success == true)
                {
                    var formattedResponse5 = FormatResponseSimple(botResponse.Content);
                    Console.WriteLine("✅ End-to-end flow test successful!");
                    Console.WriteLine($"   Request: {testRequest}");
                    Console.WriteLine($"   Extracted prompt: {extractedPrompt5}");
                    Console.WriteLine($"   Response length: {formattedResponse5.Length} characters");
                    Console.WriteLine($"   Response preview: {formattedResponse5.Substring(0, Math.Min(150, formattedResponse5.Length))}...");
                }
                else
                {
                    Console.WriteLine($"❌ OpenAI API call failed: {botResponse?.ErrorMessage}");
                }
            }
            else
            {
                Console.WriteLine("❌ End-to-end flow test failed - could not extract prompt");
            }

            Console.WriteLine("\n🎉 ChatGPT Bot Test completed successfully!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"\n❌ Test failed with error: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
        finally
        {
            Log.CloseAndFlush();
        }
    }

    private static string? ExtractPromptSimple(string messageContent)
    {
        if (string.IsNullOrWhiteSpace(messageContent))
        {
            return null;
        }

        var content = messageContent.Trim();

        // Handle mention triggers
        content = content.Replace("@ChatGptBot", "").Replace("@ChatGPT", "").Trim();

        // Handle keyword triggers
        var keywords = new[] { "!askchatgpt", "!chatgpt", "!gpt" };
        foreach (var keyword in keywords)
        {
            if (content.StartsWith(keyword, StringComparison.OrdinalIgnoreCase))
            {
                content = content.Substring(keyword.Length).Trim();
                break;
            }
        }

        return string.IsNullOrWhiteSpace(content) ? null : content;
    }

    private static string FormatResponseSimple(string response, int maxLength = 2000)
    {
        if (string.IsNullOrWhiteSpace(response))
        {
            return "❌ Something went wrong while fetching the response. Please try again.";
        }

        var prefix = "🤖 **ChatGPT Response:**\n";
        var formattedResponse = prefix + response;

        // Truncate if too long for Discord
        if (formattedResponse.Length > maxLength)
        {
            var availableLength = maxLength - prefix.Length - 50; // Leave room for truncation message
            var truncated = response.Substring(0, Math.Max(0, availableLength));

            // Try to truncate at a word boundary
            var lastSpace = truncated.LastIndexOf(' ');
            if (lastSpace > truncated.Length * 0.8) // Only if we're not cutting too much
            {
                truncated = truncated.Substring(0, lastSpace);
            }

            formattedResponse = prefix + truncated + "\n\n*[Response truncated due to length]*";
        }

        return formattedResponse;
    }
}


