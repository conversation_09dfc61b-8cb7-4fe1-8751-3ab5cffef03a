using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using ZeroDateStrat.Models;
using System.Text.Json;

namespace ZeroDateStrat.Services;

/// <summary>
/// Dashboard service for SyntheticVIX monitoring and visualization
/// Integrates with existing monitoring infrastructure to provide comprehensive VIX analytics
/// </summary>
public interface ISyntheticVixDashboardService
{
    Task<SyntheticVixDashboardData> GetDashboardDataAsync();
    Task<string> GenerateHealthReportJsonAsync();
    Task<string> GeneratePerformanceReportJsonAsync();
    Task<SyntheticVixSystemStatus> GetSystemStatusAsync();
    Task<List<SyntheticVixTrendData>> GetTrendDataAsync(int hours = 24);
    Task<bool> ExportAnalyticsDataAsync(string filePath);
}

public class SyntheticVixDashboardService : ISyntheticVixDashboardService
{
    private readonly ILogger<SyntheticVixDashboardService> _logger;
    private readonly ISyntheticVixService _syntheticVixService;
    private readonly ISyntheticVixAnalyticsService _analyticsService;
    private readonly IPerformanceMonitoringService _performanceMonitor;
    private readonly IConfiguration _configuration;

    public SyntheticVixDashboardService(
        ILogger<SyntheticVixDashboardService> logger,
        ISyntheticVixService syntheticVixService,
        ISyntheticVixAnalyticsService analyticsService,
        IPerformanceMonitoringService performanceMonitor,
        IConfiguration configuration)
    {
        _logger = logger;
        _syntheticVixService = syntheticVixService;
        _analyticsService = analyticsService;
        _performanceMonitor = performanceMonitor;
        _configuration = configuration;
        
        _logger.LogInformation("SyntheticVixDashboardService initialized");
    }

    public async Task<SyntheticVixDashboardData> GetDashboardDataAsync()
    {
        try
        {
            var dashboardData = new SyntheticVixDashboardData
            {
                Timestamp = DateTime.UtcNow
            };

            // Get current SyntheticVIX data
            var currentAnalysis = await _syntheticVixService.GetSyntheticVixAnalysisAsync();
            dashboardData.CurrentSyntheticVix = currentAnalysis;

            // Get health status
            var healthReport = await _analyticsService.GetHealthReportAsync();
            dashboardData.HealthStatus = healthReport;

            // Get performance metrics
            var performanceMetrics = await _analyticsService.GetPerformanceMetricsAsync();
            dashboardData.PerformanceMetrics = performanceMetrics;

            // Get active alerts
            var alerts = await _analyticsService.GetActiveAlertsAsync();
            dashboardData.ActiveAlerts = alerts.Take(10).ToList(); // Top 10 alerts

            // Get correlation analysis
            var correlationAnalysis = await _analyticsService.GetCorrelationAnalysisAsync();
            dashboardData.CorrelationData = correlationAnalysis;

            // Get recent trend data
            var trendData = await GetTrendDataAsync(6); // Last 6 hours
            dashboardData.TrendData = trendData;

            // Get system performance data
            var systemMetrics = _performanceMonitor.GetMetrics();
            dashboardData.SystemMetrics = systemMetrics;

            return dashboardData;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating SyntheticVIX dashboard data");
            return new SyntheticVixDashboardData
            {
                Timestamp = DateTime.UtcNow,
                ErrorMessage = $"Dashboard data unavailable: {ex.Message}"
            };
        }
    }

    public async Task<string> GenerateHealthReportJsonAsync()
    {
        try
        {
            var healthReport = await _analyticsService.GetHealthReportAsync();
            var options = new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };
            
            return JsonSerializer.Serialize(healthReport, options);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating health report JSON");
            return JsonSerializer.Serialize(new { error = ex.Message });
        }
    }

    public async Task<string> GeneratePerformanceReportJsonAsync()
    {
        try
        {
            var performanceMetrics = await _analyticsService.GetPerformanceMetricsAsync();
            var systemMetrics = _performanceMonitor.GetMetrics();
            
            var combinedReport = new
            {
                SyntheticVixMetrics = performanceMetrics,
                SystemMetrics = systemMetrics,
                Timestamp = DateTime.UtcNow
            };
            
            var options = new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };
            
            return JsonSerializer.Serialize(combinedReport, options);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating performance report JSON");
            return JsonSerializer.Serialize(new { error = ex.Message });
        }
    }

    public async Task<SyntheticVixSystemStatus> GetSystemStatusAsync()
    {
        try
        {
            var status = new SyntheticVixSystemStatus
            {
                Timestamp = DateTime.UtcNow
            };

            // Get current SyntheticVIX value
            var currentVix = await _syntheticVixService.GetCurrentSyntheticVixAsync();
            status.CurrentValue = currentVix;

            // Test connectivity
            var connectionTest = await _syntheticVixService.TestConnectionAsync();
            status.IsConnected = connectionTest;

            // Get health status
            var healthReport = await _analyticsService.GetHealthReportAsync();
            status.OverallHealth = healthReport.OverallHealth;
            status.HealthSummary = healthReport.Summary;

            // Component status
            status.ComponentStatus = healthReport.ComponentHealth.ToDictionary(
                kvp => kvp.Key,
                kvp => new ComponentStatus
                {
                    IsHealthy = kvp.Value.IsHealthy,
                    LastPrice = kvp.Value.LastPrice,
                    LastUpdate = kvp.Value.LastSuccessfulUpdate,
                    ErrorCount = kvp.Value.RecentFailureCount
                });

            // Performance indicators
            var performanceMetrics = await _analyticsService.GetPerformanceMetricsAsync();
            status.CalculationFrequency = performanceMetrics.CalculationFrequency;
            status.AverageResponseTime = performanceMetrics.AverageCalculationTime;

            // Alert summary
            var alerts = await _analyticsService.GetActiveAlertsAsync();
            status.CriticalAlerts = alerts.Count(a => a.Severity == AlertSeverity.Critical);
            status.WarningAlerts = alerts.Count(a => a.Severity == AlertSeverity.Medium);

            return status;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting SyntheticVIX system status");
            return new SyntheticVixSystemStatus
            {
                Timestamp = DateTime.UtcNow,
                IsConnected = false,
                OverallHealth = HealthStatus.Unhealthy,
                HealthSummary = $"System status check failed: {ex.Message}"
            };
        }
    }

    public async Task<List<SyntheticVixTrendData>> GetTrendDataAsync(int hours = 24)
    {
        try
        {
            var trendData = new List<SyntheticVixTrendData>();
            var historicalData = await _syntheticVixService.GetHistoricalDataAsync(Math.Max(1, hours / 24));

            // Filter to requested time range
            var cutoffTime = DateTime.UtcNow.AddHours(-hours);
            var filteredData = historicalData.Where(h => h.Timestamp >= cutoffTime).OrderBy(h => h.Timestamp);

            foreach (var dataPoint in filteredData)
            {
                trendData.Add(new SyntheticVixTrendData
                {
                    Timestamp = dataPoint.Timestamp,
                    Value = dataPoint.NormalizedValue,
                    RawValue = dataPoint.RawValue,
                    ComponentValues = dataPoint.ComponentValues
                });
            }

            return trendData;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting SyntheticVIX trend data for {Hours} hours", hours);
            return new List<SyntheticVixTrendData>();
        }
    }

    public async Task<bool> ExportAnalyticsDataAsync(string filePath)
    {
        try
        {
            var exportData = new
            {
                ExportTimestamp = DateTime.UtcNow,
                DashboardData = await GetDashboardDataAsync(),
                HealthReport = await _analyticsService.GetHealthReportAsync(),
                PerformanceMetrics = await _analyticsService.GetPerformanceMetricsAsync(),
                CorrelationAnalysis = await _analyticsService.GetCorrelationAnalysisAsync(),
                CalibrationReport = await _analyticsService.GetCalibrationReportAsync(),
                TrendData = await GetTrendDataAsync(168), // Last week
                SystemStatus = await GetSystemStatusAsync()
            };

            var options = new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };

            var json = JsonSerializer.Serialize(exportData, options);
            await File.WriteAllTextAsync(filePath, json);

            _logger.LogInformation("SyntheticVIX analytics data exported to {FilePath}", filePath);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting SyntheticVIX analytics data to {FilePath}", filePath);
            return false;
        }
    }
}

// Dashboard Data Models
public class SyntheticVixDashboardData
{
    public DateTime Timestamp { get; set; }
    public SyntheticVixAnalysis? CurrentSyntheticVix { get; set; }
    public SyntheticVixHealthReport? HealthStatus { get; set; }
    public SyntheticVixPerformanceMetrics? PerformanceMetrics { get; set; }
    public List<SyntheticVixAlert> ActiveAlerts { get; set; } = new();
    public SyntheticVixCorrelationAnalysis? CorrelationData { get; set; }
    public List<SyntheticVixTrendData> TrendData { get; set; } = new();
    public PerformanceMetrics? SystemMetrics { get; set; }
    public string? ErrorMessage { get; set; }
}

public class SyntheticVixSystemStatus
{
    public DateTime Timestamp { get; set; }
    public decimal CurrentValue { get; set; }
    public bool IsConnected { get; set; }
    public HealthStatus OverallHealth { get; set; }
    public string HealthSummary { get; set; } = string.Empty;
    public Dictionary<string, ComponentStatus> ComponentStatus { get; set; } = new();
    public int CalculationFrequency { get; set; }
    public double AverageResponseTime { get; set; }
    public int CriticalAlerts { get; set; }
    public int WarningAlerts { get; set; }
}

public class ComponentStatus
{
    public bool IsHealthy { get; set; }
    public decimal LastPrice { get; set; }
    public DateTime LastUpdate { get; set; }
    public int ErrorCount { get; set; }
}

public class SyntheticVixTrendData
{
    public DateTime Timestamp { get; set; }
    public decimal Value { get; set; }
    public decimal RawValue { get; set; }
    public Dictionary<string, decimal> ComponentValues { get; set; } = new();
}
