# SyntheticVIX Analytics System - Usage Guide

## Quick Start

### Running Tests

The SyntheticVIX Analytics System includes several test modes to validate functionality:

```bash
# Quick validation test
dotnet run quickvix

# Comprehensive analytics test
dotnet run comprehensive-vix

# Full SyntheticVIX service test
dotnet run syntheticvix

# Complete SyntheticVIX analytics test
dotnet run syntheticvix-analytics
```

### Basic Usage in Code

```csharp
// Inject the analytics service
var analyticsService = serviceProvider.GetRequiredService<ISyntheticVixAnalyticsService>();
var dashboardService = serviceProvider.GetRequiredService<ISyntheticVixDashboardService>();

// Get health status
var healthReport = await analyticsService.GetHealthReportAsync();
Console.WriteLine($"System Health: {healthReport.OverallHealth}");

// Get performance metrics
var metrics = await analyticsService.GetPerformanceMetricsAsync();
Console.WriteLine($"Avg Calculation Time: {metrics.AverageCalculationTime}ms");

// Get dashboard data
var dashboard = await dashboardService.GetDashboardDataAsync();
```

## Core Features

### 1. Health Monitoring

**Purpose**: Monitor the health of VIX components (VXX, UVXY, SVXY) and overall system status.

```csharp
var healthReport = await analyticsService.GetHealthReportAsync();

// Check overall health
if (healthReport.OverallHealth == HealthStatus.Healthy)
{
    Console.WriteLine("✅ System is healthy");
}
else
{
    Console.WriteLine($"⚠️ System health: {healthReport.OverallHealth}");
    foreach (var issue in healthReport.Issues)
    {
        Console.WriteLine($"   Issue: {issue}");
    }
}

// Check individual components
foreach (var component in healthReport.ComponentHealth)
{
    var status = component.Value.IsHealthy ? "✅" : "❌";
    Console.WriteLine($"{status} {component.Key}: ${component.Value.LastPrice:F2}");
}
```

### 2. Performance Analytics

**Purpose**: Track calculation performance, frequency, and system efficiency.

```csharp
var metrics = await analyticsService.GetPerformanceMetricsAsync();

Console.WriteLine($"Performance Metrics:");
Console.WriteLine($"  Calculation Time: {metrics.AverageCalculationTime:F1}ms");
Console.WriteLine($"  Frequency: {metrics.CalculationFrequency} calculations/hour");
Console.WriteLine($"  Value Stability: {metrics.ValueStability:F2}");
Console.WriteLine($"  Average Z-Score: {metrics.AverageZScore:F2}");

// Component-specific metrics
foreach (var component in metrics.ComponentMetrics)
{
    Console.WriteLine($"  {component.Key}:");
    Console.WriteLine($"    Total Calculations: {component.Value.TotalCalculations}");
    Console.WriteLine($"    Average Price: ${component.Value.AveragePrice:F2}");
    Console.WriteLine($"    Confidence: {component.Value.AverageConfidence:F2}");
}
```

### 3. Alert System

**Purpose**: Monitor for anomalies, failures, and extreme conditions.

```csharp
var alerts = await analyticsService.GetActiveAlertsAsync();

Console.WriteLine($"Active Alerts: {alerts.Count}");

// Group by severity
var alertsBySeverity = alerts.GroupBy(a => a.Severity);
foreach (var group in alertsBySeverity)
{
    Console.WriteLine($"  {group.Key}: {group.Count()} alerts");
}

// Show recent alerts
foreach (var alert in alerts.Take(5))
{
    var icon = alert.Severity switch
    {
        AlertSeverity.Critical => "🚨",
        AlertSeverity.High => "⚠️",
        AlertSeverity.Medium => "⚡",
        _ => "ℹ️"
    };
    
    Console.WriteLine($"{icon} [{alert.Severity}] {alert.Type}: {alert.Message}");
    Console.WriteLine($"    Time: {alert.Timestamp:HH:mm:ss}");
}
```

### 4. Correlation Analysis

**Purpose**: Analyze relationships between VIX components and overall correlation stability.

```csharp
var correlation = await analyticsService.GetCorrelationAnalysisAsync();

Console.WriteLine($"Correlation Analysis:");
Console.WriteLine($"  Overall Correlation: {correlation.OverallCorrelation:F3}");
Console.WriteLine($"  Correlation Stability: {correlation.CorrelationStability:F3}");

Console.WriteLine($"  Component Correlations:");
foreach (var comp in correlation.ComponentCorrelations)
{
    var status = Math.Abs(comp.Value) > 0.7 ? "✅ Strong" : 
                 Math.Abs(comp.Value) > 0.3 ? "⚡ Moderate" : "⚠️ Weak";
    Console.WriteLine($"    {comp.Key}: {comp.Value:F3} ({status})");
}
```

### 5. Dashboard Integration

**Purpose**: Unified view of all analytics data for monitoring and visualization.

```csharp
var dashboard = await dashboardService.GetDashboardDataAsync();

if (dashboard.ErrorMessage == null)
{
    Console.WriteLine($"Dashboard Data:");
    Console.WriteLine($"  Current SyntheticVIX: {dashboard.CurrentSyntheticVix?.CurrentLevel:F2}");
    Console.WriteLine($"  Risk Level: {dashboard.CurrentSyntheticVix?.RiskLevel}");
    Console.WriteLine($"  Health: {dashboard.HealthStatus?.OverallHealth}");
    Console.WriteLine($"  Active Alerts: {dashboard.ActiveAlerts.Count}");
    Console.WriteLine($"  Trend Points: {dashboard.TrendData.Count}");
}

// Get system status
var status = await dashboardService.GetSystemStatusAsync();
Console.WriteLine($"System Status:");
Console.WriteLine($"  Connected: {status.IsConnected}");
Console.WriteLine($"  Current Value: {status.CurrentValue:F2}");
Console.WriteLine($"  Response Time: {status.AverageResponseTime:F1}ms");
Console.WriteLine($"  Critical Alerts: {status.CriticalAlerts}");
```

### 6. Data Export

**Purpose**: Export analytics data for external analysis or reporting.

```csharp
// Export complete analytics data
var exportPath = $"analytics-{DateTime.Now:yyyyMMdd-HHmmss}.json";
var success = await dashboardService.ExportAnalyticsDataAsync(exportPath);

if (success)
{
    Console.WriteLine($"✅ Analytics data exported to: {exportPath}");
}

// Generate JSON reports
var healthJson = await dashboardService.GenerateHealthReportJsonAsync();
var performanceJson = await dashboardService.GeneratePerformanceReportJsonAsync();

// Save to files or send to external systems
await File.WriteAllTextAsync("health-report.json", healthJson);
await File.WriteAllTextAsync("performance-report.json", performanceJson);
```

## Integration with Trading System

### Automatic Recording

The analytics service automatically records all SyntheticVIX calculations:

```csharp
// This happens automatically in SyntheticVixService
var analysis = await syntheticVixService.GetSyntheticVixAnalysisAsync();
// Analytics service automatically records this calculation
```

### Failure Tracking

Component failures are automatically tracked:

```csharp
// This happens automatically when component data retrieval fails
try
{
    var price = await alpacaService.GetCurrentPriceAsync("VXX");
}
catch (Exception ex)
{
    // Analytics service automatically records this failure
    analyticsService.RecordComponentFailure("VXX", ex);
}
```

### Real-Time Monitoring

Integrate with your trading loop:

```csharp
while (tradingActive)
{
    // Get current market analysis
    var vixAnalysis = await syntheticVixService.GetSyntheticVixAnalysisAsync();
    
    // Check system health before trading
    var health = await analyticsService.GetHealthReportAsync();
    if (health.OverallHealth != HealthStatus.Healthy)
    {
        logger.LogWarning("SyntheticVIX system health degraded, reducing position sizes");
        // Implement risk reduction logic
    }
    
    // Check for critical alerts
    var alerts = await analyticsService.GetActiveAlertsAsync();
    var criticalAlerts = alerts.Where(a => a.Severity == AlertSeverity.Critical);
    if (criticalAlerts.Any())
    {
        logger.LogError("Critical SyntheticVIX alerts detected, halting trading");
        // Implement emergency stop logic
    }
    
    // Continue with trading logic...
}
```

## Configuration

### Service Registration

Ensure services are registered in your DI container:

```csharp
services.AddSingleton<ISyntheticVixAnalyticsService, SyntheticVixAnalyticsService>();
services.AddSingleton<ISyntheticVixDashboardService, SyntheticVixDashboardService>();
```

### Dependencies

The analytics system requires these services:
- `IAlpacaService` - For market data
- `IPerformanceMonitoringService` - For system metrics
- `ISyntheticVixService` - For VIX calculations
- `IConfiguration` - For settings
- `ILogger` - For logging

## Best Practices

### 1. Regular Health Checks

```csharp
// Check health every 5 minutes during trading hours
var timer = new Timer(async _ =>
{
    var health = await analyticsService.GetHealthReportAsync();
    if (health.OverallHealth != HealthStatus.Healthy)
    {
        await notificationService.SendAlertAsync($"SyntheticVIX Health: {health.OverallHealth}");
    }
}, null, TimeSpan.Zero, TimeSpan.FromMinutes(5));
```

### 2. Performance Monitoring

```csharp
// Monitor performance hourly
var performanceTimer = new Timer(async _ =>
{
    var metrics = await analyticsService.GetPerformanceMetricsAsync();
    if (metrics.AverageCalculationTime > 1000) // > 1 second
    {
        logger.LogWarning("SyntheticVIX calculation time degraded: {Time}ms", metrics.AverageCalculationTime);
    }
}, null, TimeSpan.Zero, TimeSpan.FromHours(1));
```

### 3. Alert Management

```csharp
// Process alerts regularly
var alertTimer = new Timer(async _ =>
{
    var alerts = await analyticsService.GetActiveAlertsAsync();
    var newAlerts = alerts.Where(a => !a.IsAcknowledged);
    
    foreach (var alert in newAlerts)
    {
        await ProcessAlert(alert);
    }
}, null, TimeSpan.Zero, TimeSpan.FromMinutes(1));
```

## Troubleshooting

### Common Issues

1. **No Health Data**: Ensure Alpaca service is properly configured
2. **Missing Performance Metrics**: Check that calculations are being recorded
3. **No Alerts**: Verify alert thresholds are appropriate for your data
4. **Export Failures**: Check file permissions and disk space

### Debugging

Enable detailed logging:

```csharp
services.AddLogging(builder =>
{
    builder.SetMinimumLevel(LogLevel.Debug);
    builder.AddConsole();
    builder.AddFile("logs/syntheticvix-analytics.log");
});
```

This comprehensive analytics system provides deep insights into your SyntheticVIX calculations, helping ensure reliable and optimal performance for your 0 DTE trading strategies.
