using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Collections.Concurrent;
using System.Text.Json;

namespace ZeroDateStrat.Services;

public interface ICacheAnalyticsService
{
    void RecordCacheHit(string key, string dataType, long responseTimeMs);
    void RecordCacheMiss(string key, string dataType, long responseTimeMs);
    void RecordCacheSet(string key, string dataType, long sizeBytes);
    void RecordCacheEviction(string key, string reason);
    CacheAnalyticsReport GenerateReport();
    Task LogPerformanceReportAsync();
}

public class CacheAnalyticsReport
{
    public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
    public TimeSpan ReportPeriod { get; set; }
    public CacheOverallMetrics Overall { get; set; } = new();
    public Dictionary<string, CacheDataTypeMetrics> ByDataType { get; set; } = new();
    public Dictionary<string, CacheKeyMetrics> TopKeys { get; set; } = new();
    public List<CacheRecommendation> Recommendations { get; set; } = new();
}

public class CacheOverallMetrics
{
    public long TotalHits { get; set; }
    public long TotalMisses { get; set; }
    public long TotalSets { get; set; }
    public long TotalEvictions { get; set; }
    public double HitRatio => TotalHits + TotalMisses > 0 ? (double)TotalHits / (TotalHits + TotalMisses) : 0;
    public double AverageHitResponseTimeMs { get; set; }
    public double AverageMissResponseTimeMs { get; set; }
    public long TotalCacheSize { get; set; }
    public double MemoryEfficiency { get; set; }
}

public class CacheDataTypeMetrics
{
    public string DataType { get; set; } = string.Empty;
    public long Hits { get; set; }
    public long Misses { get; set; }
    public long Sets { get; set; }
    public long Evictions { get; set; }
    public double HitRatio => Hits + Misses > 0 ? (double)Hits / (Hits + Misses) : 0;
    public double AverageResponseTimeMs { get; set; }
    public long AverageSizeBytes { get; set; }
    public double PerformanceScore { get; set; }
}

public class CacheKeyMetrics
{
    public string Key { get; set; } = string.Empty;
    public long AccessCount { get; set; }
    public double HitRatio { get; set; }
    public double AverageResponseTimeMs { get; set; }
    public long SizeBytes { get; set; }
    public DateTime LastAccessed { get; set; }
    public string DataType { get; set; } = string.Empty;
}

public class CacheRecommendation
{
    public string Type { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Impact { get; set; } = string.Empty;
    public int Priority { get; set; }
}

public class CacheAnalyticsService : ICacheAnalyticsService, IHostedService
{
    private readonly IHighPerformanceCacheService _cache;
    private readonly ILogger<CacheAnalyticsService> _logger;
    private readonly IConfiguration _configuration;
    private readonly IPerformanceMonitoringService _performanceMonitor;
    
    private readonly ConcurrentDictionary<string, CacheKeyMetrics> _keyMetrics;
    private readonly ConcurrentDictionary<string, CacheDataTypeMetrics> _dataTypeMetrics;
    private readonly ConcurrentQueue<CacheEvent> _recentEvents;
    private readonly Timer _reportTimer;
    private readonly DateTime _startTime;

    private long _totalHits;
    private long _totalMisses;
    private long _totalSets;
    private long _totalEvictions;
    private long _totalHitResponseTime;
    private long _totalMissResponseTime;

    public CacheAnalyticsService(
        IHighPerformanceCacheService cache,
        ILogger<CacheAnalyticsService> logger,
        IConfiguration configuration,
        IPerformanceMonitoringService performanceMonitor)
    {
        _cache = cache;
        _logger = logger;
        _configuration = configuration;
        _performanceMonitor = performanceMonitor;
        
        _keyMetrics = new ConcurrentDictionary<string, CacheKeyMetrics>();
        _dataTypeMetrics = new ConcurrentDictionary<string, CacheDataTypeMetrics>();
        _recentEvents = new ConcurrentQueue<CacheEvent>();
        _startTime = DateTime.UtcNow;
        
        // Generate reports every 15 minutes
        _reportTimer = new Timer(async _ => await LogPerformanceReportAsync(), 
            null, TimeSpan.FromMinutes(15), TimeSpan.FromMinutes(15));
    }

    public Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("📊 Starting cache analytics service...");
        return Task.CompletedTask;
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        _reportTimer?.Dispose();
        _logger.LogInformation("Cache analytics service stopped");
        return Task.CompletedTask;
    }

    public void RecordCacheHit(string key, string dataType, long responseTimeMs)
    {
        Interlocked.Increment(ref _totalHits);
        Interlocked.Add(ref _totalHitResponseTime, responseTimeMs);

        UpdateKeyMetrics(key, dataType, true, responseTimeMs);
        UpdateDataTypeMetrics(dataType, true, responseTimeMs);
        
        _recentEvents.Enqueue(new CacheEvent 
        { 
            Type = "Hit", 
            Key = key, 
            DataType = dataType, 
            ResponseTimeMs = responseTimeMs,
            Timestamp = DateTime.UtcNow 
        });

        // Keep only recent events (last 1000)
        while (_recentEvents.Count > 1000)
        {
            _recentEvents.TryDequeue(out _);
        }
    }

    public void RecordCacheMiss(string key, string dataType, long responseTimeMs)
    {
        Interlocked.Increment(ref _totalMisses);
        Interlocked.Add(ref _totalMissResponseTime, responseTimeMs);

        UpdateKeyMetrics(key, dataType, false, responseTimeMs);
        UpdateDataTypeMetrics(dataType, false, responseTimeMs);
        
        _recentEvents.Enqueue(new CacheEvent 
        { 
            Type = "Miss", 
            Key = key, 
            DataType = dataType, 
            ResponseTimeMs = responseTimeMs,
            Timestamp = DateTime.UtcNow 
        });
    }

    public void RecordCacheSet(string key, string dataType, long sizeBytes)
    {
        Interlocked.Increment(ref _totalSets);
        
        _keyMetrics.AddOrUpdate(key, 
            new CacheKeyMetrics { Key = key, DataType = dataType, SizeBytes = sizeBytes },
            (k, existing) => { existing.SizeBytes = sizeBytes; return existing; });
            
        _dataTypeMetrics.AddOrUpdate(dataType,
            new CacheDataTypeMetrics { DataType = dataType, Sets = 1, AverageSizeBytes = sizeBytes },
            (dt, existing) => 
            { 
                existing.Sets++; 
                existing.AverageSizeBytes = (existing.AverageSizeBytes + sizeBytes) / 2;
                return existing; 
            });
    }

    public void RecordCacheEviction(string key, string reason)
    {
        Interlocked.Increment(ref _totalEvictions);
        
        if (_keyMetrics.TryGetValue(key, out var keyMetric))
        {
            var dataType = keyMetric.DataType;
            _dataTypeMetrics.AddOrUpdate(dataType,
                new CacheDataTypeMetrics { DataType = dataType, Evictions = 1 },
                (dt, existing) => { existing.Evictions++; return existing; });
        }
    }

    public CacheAnalyticsReport GenerateReport()
    {
        var report = new CacheAnalyticsReport
        {
            ReportPeriod = DateTime.UtcNow - _startTime,
            Overall = new CacheOverallMetrics
            {
                TotalHits = _totalHits,
                TotalMisses = _totalMisses,
                TotalSets = _totalSets,
                TotalEvictions = _totalEvictions,
                AverageHitResponseTimeMs = _totalHits > 0 ? (double)_totalHitResponseTime / _totalHits : 0,
                AverageMissResponseTimeMs = _totalMisses > 0 ? (double)_totalMissResponseTime / _totalMisses : 0,
                TotalCacheSize = _cache.GetStatistics().TotalMemoryUsage
            },
            ByDataType = new Dictionary<string, CacheDataTypeMetrics>(_dataTypeMetrics),
            TopKeys = _keyMetrics.Values
                .OrderByDescending(k => k.AccessCount)
                .Take(20)
                .ToDictionary(k => k.Key, k => k)
        };

        // Calculate performance scores
        foreach (var dataType in report.ByDataType.Values)
        {
            dataType.PerformanceScore = CalculatePerformanceScore(dataType);
        }

        // Generate recommendations
        report.Recommendations = GenerateRecommendations(report);

        return report;
    }

    public async Task LogPerformanceReportAsync()
    {
        try
        {
            await _performanceMonitor.MonitorAsync("cache_analytics_report", async () =>
            {
                var report = GenerateReport();
                
                _logger.LogInformation("📊 === CACHE ANALYTICS REPORT ===");
                _logger.LogInformation("📈 Overall Performance:");
                _logger.LogInformation("   Hit Ratio: {HitRatio:P2}", report.Overall.HitRatio);
                _logger.LogInformation("   Total Operations: {Total} (Hits: {Hits}, Misses: {Misses})", 
                    report.Overall.TotalHits + report.Overall.TotalMisses, 
                    report.Overall.TotalHits, 
                    report.Overall.TotalMisses);
                _logger.LogInformation("   Avg Hit Response: {HitTime:F1}ms, Avg Miss Response: {MissTime:F1}ms",
                    report.Overall.AverageHitResponseTimeMs,
                    report.Overall.AverageMissResponseTimeMs);
                _logger.LogInformation("   Cache Size: {Size:F1}MB", report.Overall.TotalCacheSize / 1024.0 / 1024.0);

                _logger.LogInformation("📊 Performance by Data Type:");
                foreach (var dataType in report.ByDataType.Values.OrderByDescending(d => d.PerformanceScore).Take(10))
                {
                    _logger.LogInformation("   {DataType}: {HitRatio:P1} hit ratio, {Score:F1} score, {Size:F0}KB avg",
                        dataType.DataType, dataType.HitRatio, dataType.PerformanceScore, dataType.AverageSizeBytes / 1024.0);
                }

                _logger.LogInformation("🔥 Top Cache Keys:");
                foreach (var key in report.TopKeys.Values.Take(5))
                {
                    _logger.LogInformation("   {Key}: {AccessCount} accesses, {HitRatio:P1} hit ratio",
                        key.Key, key.AccessCount, key.HitRatio);
                }

                if (report.Recommendations.Any())
                {
                    _logger.LogInformation("💡 Recommendations:");
                    foreach (var rec in report.Recommendations.OrderByDescending(r => r.Priority).Take(3))
                    {
                        _logger.LogInformation("   {Type}: {Description} (Impact: {Impact})",
                            rec.Type, rec.Description, rec.Impact);
                    }
                }

                _logger.LogInformation("📊 === END CACHE ANALYTICS REPORT ===");
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating cache analytics report");
        }
    }

    private void UpdateKeyMetrics(string key, string dataType, bool isHit, long responseTimeMs)
    {
        _keyMetrics.AddOrUpdate(key,
            new CacheKeyMetrics 
            { 
                Key = key, 
                DataType = dataType,
                AccessCount = 1,
                HitRatio = isHit ? 1.0 : 0.0,
                AverageResponseTimeMs = responseTimeMs,
                LastAccessed = DateTime.UtcNow
            },
            (k, existing) =>
            {
                existing.AccessCount++;
                var totalHits = existing.HitRatio * (existing.AccessCount - 1) + (isHit ? 1 : 0);
                existing.HitRatio = totalHits / existing.AccessCount;
                existing.AverageResponseTimeMs = (existing.AverageResponseTimeMs + responseTimeMs) / 2;
                existing.LastAccessed = DateTime.UtcNow;
                return existing;
            });
    }

    private void UpdateDataTypeMetrics(string dataType, bool isHit, long responseTimeMs)
    {
        _dataTypeMetrics.AddOrUpdate(dataType,
            new CacheDataTypeMetrics 
            { 
                DataType = dataType,
                Hits = isHit ? 1L : 0L,
                Misses = isHit ? 0L : 1L,
                AverageResponseTimeMs = responseTimeMs
            },
            (dt, existing) =>
            {
                if (isHit) existing.Hits++; else existing.Misses++;
                existing.AverageResponseTimeMs = (existing.AverageResponseTimeMs + responseTimeMs) / 2;
                return existing;
            });
    }

    private double CalculatePerformanceScore(CacheDataTypeMetrics metrics)
    {
        // Performance score based on hit ratio, response time, and usage
        var hitRatioScore = metrics.HitRatio * 40; // 40 points max
        var responseTimeScore = Math.Max(0, 30 - (metrics.AverageResponseTimeMs / 10)); // 30 points max
        var usageScore = Math.Min(30, (metrics.Hits + metrics.Misses) / 10); // 30 points max
        
        return hitRatioScore + responseTimeScore + usageScore;
    }

    private List<CacheRecommendation> GenerateRecommendations(CacheAnalyticsReport report)
    {
        var recommendations = new List<CacheRecommendation>();

        // Low hit ratio recommendation
        if (report.Overall.HitRatio < 0.7)
        {
            recommendations.Add(new CacheRecommendation
            {
                Type = "Hit Ratio",
                Description = $"Overall hit ratio is {report.Overall.HitRatio:P1}. Consider increasing cache expiration times or cache size.",
                Impact = "High",
                Priority = 9
            });
        }

        // High miss response time
        if (report.Overall.AverageMissResponseTimeMs > 100)
        {
            recommendations.Add(new CacheRecommendation
            {
                Type = "Response Time",
                Description = $"Cache misses take {report.Overall.AverageMissResponseTimeMs:F0}ms on average. Consider predictive caching.",
                Impact = "Medium",
                Priority = 7
            });
        }

        // Data type specific recommendations
        foreach (var dataType in report.ByDataType.Values.Where(d => d.HitRatio < 0.5 && d.Hits + d.Misses > 10))
        {
            recommendations.Add(new CacheRecommendation
            {
                Type = "Data Type",
                Description = $"{dataType.DataType} has low hit ratio ({dataType.HitRatio:P1}). Consider longer expiration or pre-caching.",
                Impact = "Medium",
                Priority = 6
            });
        }

        return recommendations;
    }
}

public class CacheEvent
{
    public string Type { get; set; } = string.Empty;
    public string Key { get; set; } = string.Empty;
    public string DataType { get; set; } = string.Empty;
    public long ResponseTimeMs { get; set; }
    public DateTime Timestamp { get; set; }
}
