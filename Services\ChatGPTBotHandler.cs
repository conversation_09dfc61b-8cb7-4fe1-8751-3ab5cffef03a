using Discord.WebSocket;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Text.RegularExpressions;
using ZeroDateStrat.Models;

namespace ZeroDateStrat.Services;

/// <summary>
/// Handler for ChatGPT bot functionality in Discord
/// </summary>
public class ChatGPTBotHandler : IChatGPTBotHandler
{
    private readonly IOpenAIService _openAIService;
    private readonly IDiscordService _discordService;
    private readonly ILogger<ChatGPTBotHandler> _logger;
    private readonly OpenAIConfiguration _config;

    public ChatGPTBotHandler(
        IOpenAIService openAIService,
        IDiscordService discordService,
        IConfiguration configuration,
        ILogger<ChatGPTBotHandler> logger)
    {
        _openAIService = openAIService;
        _discordService = discordService;
        _logger = logger;
        
        _config = new OpenAIConfiguration();
        configuration.GetSection("OpenAI").Bind(_config);
    }

    public async Task<bool> HandleMessageAsync(SocketMessage message)
    {
        if (!ShouldTriggerChatGPT(message))
        {
            return false;
        }

        try
        {
            var prompt = ExtractPrompt(message.Content, message.MentionedUsers?.FirstOrDefault()?.Id);
            
            if (string.IsNullOrWhiteSpace(prompt))
            {
                await message.Channel.SendMessageAsync(_config.EmptyRequestMessage);
                return true;
            }

            _logger.LogInformation($"Processing ChatGPT request from {message.Author.Username}: {prompt.Substring(0, Math.Min(100, prompt.Length))}...");

            // Send typing indicator
            using var typing = message.Channel.EnterTypingState();

            var response = await _openAIService.GetChatCompletionAsync(prompt);

            if (response?.Success == true && !string.IsNullOrWhiteSpace(response.Content))
            {
                var formattedResponse = FormatResponse(response.Content, _config.MaxMessageLength);
                await message.Channel.SendMessageAsync(formattedResponse);
                
                _logger.LogInformation($"ChatGPT response sent to {message.Author.Username}, tokens used: {response.TokensUsed}");
            }
            else
            {
                await message.Channel.SendMessageAsync(_config.ErrorMessage);
                _logger.LogWarning($"Failed to get valid response from OpenAI: {response?.ErrorMessage}");
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error handling ChatGPT request from {message.Author.Username}");
            await message.Channel.SendMessageAsync(_config.ErrorMessage);
            return true;
        }
    }

    public string? ExtractPrompt(string messageContent, ulong? botUserId = null)
    {
        if (string.IsNullOrWhiteSpace(messageContent))
        {
            return null;
        }

        var content = messageContent.Trim();

        // Handle mention triggers
        if (_config.EnableMentionTrigger)
        {
            // Remove @ChatGptBot or @BotName mentions
            content = Regex.Replace(content, @"<@!?\d+>", "", RegexOptions.IgnoreCase).Trim();
            content = Regex.Replace(content, @"@ChatGptBot\b", "", RegexOptions.IgnoreCase).Trim();
            content = Regex.Replace(content, @"@ChatGPT\b", "", RegexOptions.IgnoreCase).Trim();
        }

        // Handle keyword triggers
        if (_config.EnableKeywordTrigger)
        {
            foreach (var keyword in _config.TriggerKeywords)
            {
                if (content.StartsWith(keyword, StringComparison.OrdinalIgnoreCase))
                {
                    content = content.Substring(keyword.Length).Trim();
                    break;
                }
            }
        }

        // Try to parse as JSON for structured requests
        if (content.StartsWith("{") && content.EndsWith("}"))
        {
            try
            {
                var structuredRequest = JsonConvert.DeserializeObject<StructuredGuidanceRequest>(content);
                if (structuredRequest != null && !string.IsNullOrWhiteSpace(structuredRequest.Topic))
                {
                    var prompt = $"Topic: {structuredRequest.Topic}";
                    if (!string.IsNullOrWhiteSpace(structuredRequest.Language))
                        prompt += $"\nLanguage: {structuredRequest.Language}";
                    if (structuredRequest.Components?.Any() == true)
                        prompt += $"\nComponents: {string.Join(", ", structuredRequest.Components)}";
                    if (structuredRequest.Requirements?.Any() == true)
                        prompt += $"\nRequirements: {string.Join(", ", structuredRequest.Requirements)}";
                    if (!string.IsNullOrWhiteSpace(structuredRequest.Notes))
                        prompt += $"\nNotes: {structuredRequest.Notes}";
                    
                    return prompt;
                }
            }
            catch (JsonException)
            {
                // If JSON parsing fails, treat as regular text
            }
        }

        return string.IsNullOrWhiteSpace(content) ? null : content;
    }

    public bool ShouldTriggerChatGPT(SocketMessage message)
    {
        if (message.Author.IsBot || string.IsNullOrWhiteSpace(message.Content))
        {
            return false;
        }

        var content = message.Content.Trim();

        // Check for mention triggers
        if (_config.EnableMentionTrigger)
        {
            if (content.Contains("@ChatGptBot", StringComparison.OrdinalIgnoreCase) ||
                content.Contains("@ChatGPT", StringComparison.OrdinalIgnoreCase) ||
                message.MentionedUsers?.Any() == true)
            {
                return true;
            }
        }

        // Check for keyword triggers
        if (_config.EnableKeywordTrigger)
        {
            foreach (var keyword in _config.TriggerKeywords)
            {
                if (content.StartsWith(keyword, StringComparison.OrdinalIgnoreCase))
                {
                    return true;
                }
            }
        }

        return false;
    }

    public string FormatResponse(string response, int maxLength = 2000)
    {
        if (string.IsNullOrWhiteSpace(response))
        {
            return _config.ErrorMessage;
        }

        var formattedResponse = _config.ResponsePrefix + response;

        // Truncate if too long for Discord
        if (formattedResponse.Length > maxLength)
        {
            var availableLength = maxLength - _config.ResponsePrefix.Length - 50; // Leave room for truncation message
            var truncated = response.Substring(0, Math.Max(0, availableLength));
            
            // Try to truncate at a word boundary
            var lastSpace = truncated.LastIndexOf(' ');
            if (lastSpace > truncated.Length * 0.8) // Only if we're not cutting too much
            {
                truncated = truncated.Substring(0, lastSpace);
            }
            
            formattedResponse = _config.ResponsePrefix + truncated + "\n\n*[Response truncated due to length]*";
        }

        return formattedResponse;
    }

    public async Task<string?> ProcessRequestAsync(string request)
    {
        try
        {
            // Extract the prompt from the request
            var prompt = ExtractPrompt(request);
            if (string.IsNullOrWhiteSpace(prompt))
            {
                return _config.EmptyRequestMessage;
            }

            // Get response from OpenAI
            var response = await _openAIService.GetChatCompletionAsync(prompt);
            if (response?.Success == true && !string.IsNullOrWhiteSpace(response.Content))
            {
                return FormatResponse(response.Content);
            }
            else
            {
                _logger.LogWarning("OpenAI API call failed: {ErrorMessage}", response?.ErrorMessage);
                return _config.ErrorMessage;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing ChatGPT request: {Request}", request);
            return _config.ErrorMessage;
        }
    }
}
