using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using ZeroDateStrat.Services;

namespace ZeroDateStrat.Tests;

/// <summary>
/// Simple test to verify that Polygon index data handling is working correctly
/// Tests that indices use last trade price instead of bid/ask quotes
/// </summary>
public static class PolygonIndexHandlingVerificationTest
{
    public static async Task RunPolygonIndexHandlingVerificationTest()
    {
        var logger = LoggerFactory.Create(builder => builder.AddConsole()).CreateLogger<Program>();
        
        try
        {
            logger.LogInformation("=== Polygon Index Data Handling Verification Test ===");
            logger.LogInformation("Testing proper handling of indices vs tradeable securities");

            // Build configuration
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false)
                .Build();

            // Check if Polygon API key is configured
            var polygonApiKey = configuration["Polygon:ApiKey"];
            if (string.IsNullOrEmpty(polygonApiKey) || polygonApiKey == "YOUR_POLYGON_API_KEY_HERE")
            {
                logger.LogError("❌ Polygon API key not configured");
                return;
            }

            // Build service provider with minimal dependencies
            var services = new ServiceCollection();
            services.AddSingleton<IConfiguration>(configuration);
            services.AddLogging(builder => builder.AddConsole());
            services.AddHttpClient();
            services.AddSingleton<ISecurityService, SecurityService>();
            services.AddSingleton<IPolygonDataService, PolygonDataService>();

            var serviceProvider = services.BuildServiceProvider();

            // Test connection first
            logger.LogInformation("\n1. Testing Polygon connection...");
            var polygonService = serviceProvider.GetRequiredService<IPolygonDataService>();
            var connectionTest = await polygonService.TestConnectionAsync();
            
            if (!connectionTest)
            {
                logger.LogError("❌ Polygon connection failed - cannot proceed with tests");
                return;
            }
            logger.LogInformation("✅ Polygon connection successful");

            // Test index symbols (should use last trade, not bid/ask)
            logger.LogInformation("\n2. Testing Index Symbols (should use last trade price)...");
            await TestIndexSymbols(polygonService, logger);

            // Test tradeable securities (should use real bid/ask)
            logger.LogInformation("\n3. Testing Tradeable Securities (should use real bid/ask)...");
            await TestTradeableSecurities(polygonService, logger);

            // Test VIX specifically
            logger.LogInformation("\n4. Testing VIX Index Handling...");
            await TestVixHandling(polygonService, logger);

            logger.LogInformation("\n=== Test Complete ===");
            logger.LogInformation("✅ Index data handling verification completed successfully");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "❌ Test failed with exception");
        }
    }

    private static async Task TestIndexSymbols(IPolygonDataService polygonService, ILogger logger)
    {
        var indexSymbols = new[] { "I:SPX", "I:VIX", "I:NDX", "I:RUT" };

        foreach (var symbol in indexSymbols)
        {
            try
            {
                logger.LogInformation($"Testing index symbol: {symbol}");
                var quote = await polygonService.GetCurrentQuoteAsync(symbol);
                
                if (quote != null && quote.Bid > 0)
                {
                    if (quote.Bid == quote.Ask)
                    {
                        logger.LogInformation($"✅ {symbol} - Correctly handled as index: Price={quote.Bid:F2} (last trade)");
                    }
                    else
                    {
                        logger.LogWarning($"⚠️ {symbol} - Unexpected: Bid={quote.Bid:F2}, Ask={quote.Ask:F2} (should be same for index)");
                    }
                }
                else
                {
                    logger.LogWarning($"⚠️ {symbol} - No data returned");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"❌ {symbol} - Error: {ex.Message}");
            }
        }
    }

    private static async Task TestTradeableSecurities(IPolygonDataService polygonService, ILogger logger)
    {
        var tradeableSymbols = new[] { "SPY", "QQQ", "IWM", "AAPL" };

        foreach (var symbol in tradeableSymbols)
        {
            try
            {
                logger.LogInformation($"Testing tradeable symbol: {symbol}");
                var quote = await polygonService.GetCurrentQuoteAsync(symbol);
                
                if (quote != null && quote.Bid > 0 && quote.Ask > 0)
                {
                    var spread = quote.Ask - quote.Bid;
                    var spreadPct = quote.SpreadPercentage;
                    
                    if (spread > 0)
                    {
                        logger.LogInformation($"✅ {symbol} - Real bid/ask: Bid={quote.Bid:F2}, Ask={quote.Ask:F2}, Spread={spreadPct:F3}%");
                    }
                    else
                    {
                        logger.LogWarning($"⚠️ {symbol} - Zero spread (unusual): Bid={quote.Bid:F2}, Ask={quote.Ask:F2}");
                    }
                }
                else
                {
                    logger.LogWarning($"⚠️ {symbol} - No valid quote data");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"❌ {symbol} - Error: {ex.Message}");
            }
        }
    }

    private static async Task TestVixHandling(IPolygonDataService polygonService, ILogger logger)
    {
        try
        {
            // Test VIX through dedicated method
            logger.LogInformation("Testing VIX through GetCurrentVixAsync()...");
            var vixValue = await polygonService.GetCurrentVixAsync();
            logger.LogInformation($"VIX via dedicated method: {vixValue:F2}");

            // Test VIX through quote method
            logger.LogInformation("Testing VIX through GetCurrentQuoteAsync('I:VIX')...");
            var vixQuote = await polygonService.GetCurrentQuoteAsync("I:VIX");
            if (vixQuote != null && vixQuote.Bid > 0)
            {
                logger.LogInformation($"VIX via quote method: {vixQuote.Bid:F2} (should match dedicated method)");
                
                if (Math.Abs(vixValue - vixQuote.Bid) < 0.1m)
                {
                    logger.LogInformation("✅ VIX values match between methods");
                }
                else
                {
                    logger.LogWarning($"⚠️ VIX values differ: Dedicated={vixValue:F2}, Quote={vixQuote.Bid:F2}");
                }
            }
            else
            {
                logger.LogWarning("⚠️ No VIX quote data returned");
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "❌ Error testing VIX handling");
        }
    }
}
