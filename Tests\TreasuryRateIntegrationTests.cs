using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Xunit;
using ZeroDateStrat.Models;
using ZeroDateStrat.Services;

namespace ZeroDateStrat.Tests;

public class TreasuryRateIntegrationTests
{
    private readonly ServiceProvider _serviceProvider;
    private readonly ITreasuryRateService _treasuryRateService;
    private readonly IOptionsPricingService _optionsPricingService;

    public TreasuryRateIntegrationTests()
    {
        var services = new ServiceCollection();
        
        // Setup configuration
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string>
            {
                ["FRED:ApiKey"] = "test_api_key",
                ["FRED:BaseUrl"] = "https://api.stlouisfed.org/fred",
                ["FRED:CacheExpiryMinutes"] = "60",
                ["FRED:DefaultRiskFreeRate"] = "0.0525",
                ["FRED:TreasuryRateSeries:3Month"] = "DGS3MO",
                ["FRED:TreasuryRateSeries:1Year"] = "DGS1",
                ["FRED:TreasuryRateSeries:10Year"] = "DGS10",
                ["FRED:FallbackRates:3Month"] = "0.0525",
                ["FRED:FallbackRates:1Year"] = "0.0535",
                ["FRED:FallbackRates:10Year"] = "0.0550"
            })
            .Build();

        services.AddSingleton<IConfiguration>(configuration);
        services.AddLogging(builder => builder.AddConsole());
        services.AddHttpClient<ITreasuryRateService, TreasuryRateService>();
        services.AddSingleton<ITreasuryRateService, TreasuryRateService>();
        services.AddSingleton<IOptionsPricingService, OptionsPricingService>();

        _serviceProvider = services.BuildServiceProvider();
        _treasuryRateService = _serviceProvider.GetRequiredService<ITreasuryRateService>();
        _optionsPricingService = _serviceProvider.GetRequiredService<IOptionsPricingService>();
    }

    [Fact]
    public async Task TreasuryRateService_GetRiskFreeRate_ReturnsReasonableRate()
    {
        // Act
        var rate = await _treasuryRateService.GetRiskFreeRateAsync(0.25m); // 3 months

        // Assert
        Assert.True(rate > 0);
        Assert.True(rate < 0.2m); // Should be less than 20%
        Assert.True(rate > 0.01m); // Should be greater than 1%
    }

    [Fact]
    public async Task OptionsPricingService_CalculateGreeks_UsesRealTreasuryRates()
    {
        // Arrange
        var contract = new OptionContract
        {
            Symbol = "SPX241220C05000000",
            StrikePrice = 5000m,
            ExpirationDate = DateTime.UtcNow.AddDays(30),
            OptionType = OptionType.Call,
            DaysToExpiration = 30
        };

        var underlyingPrice = 5100m;
        var volatility = 0.20m;

        // Act
        var greeks = await _optionsPricingService.CalculateGreeksAsync(contract, underlyingPrice, volatility);

        // Assert
        Assert.NotNull(greeks);
        Assert.True(greeks.Delta > 0); // Call option should have positive delta
        Assert.True(greeks.Gamma > 0); // Gamma should be positive
        Assert.True(greeks.Theta < 0); // Theta should be negative (time decay)
        Assert.True(greeks.Vega > 0); // Vega should be positive
    }

    [Fact]
    public async Task OptionsPricingService_BlackScholes_WithTreasuryRates_ReturnsReasonablePrice()
    {
        // Arrange
        var underlyingPrice = 5100m;
        var strikePrice = 5000m;
        var timeToExpiry = 30m / 365m; // 30 days
        var volatility = 0.20m;
        
        // Get current risk-free rate
        var riskFreeRate = await _treasuryRateService.GetRiskFreeRateAsync(timeToExpiry);

        // Act
        var price = await _optionsPricingService.CalculateBlackScholesPrice(
            underlyingPrice, strikePrice, timeToExpiry, riskFreeRate, volatility, true);

        // Assert
        Assert.True(price > 0);
        Assert.True(price < underlyingPrice); // Option price should be less than underlying
        Assert.True(price > underlyingPrice - strikePrice); // Should be greater than intrinsic value
    }

    [Fact]
    public async Task TreasuryRateService_GetYieldCurve_ReturnsValidCurve()
    {
        // Act
        var yieldCurve = await _treasuryRateService.GetCurrentYieldCurveAsync();

        // Assert
        Assert.NotNull(yieldCurve);
        Assert.True(yieldCurve.Rates.Count > 0);
        Assert.Equal(DateTime.UtcNow.Date, yieldCurve.Date);
        
        // Verify rates are in reasonable range
        foreach (var rate in yieldCurve.Rates.Values)
        {
            Assert.True(rate > 0);
            Assert.True(rate < 0.2m); // Less than 20%
        }
    }

    [Fact]
    public async Task TreasuryRateService_InterpolateRate_WorksCorrectly()
    {
        // Arrange
        var yieldCurve = await _treasuryRateService.GetCurrentYieldCurveAsync();

        // Act - Test various time to maturities
        var rate3Month = yieldCurve.InterpolateRate(0.25m);
        var rate6Month = yieldCurve.InterpolateRate(0.5m);
        var rate1Year = yieldCurve.InterpolateRate(1.0m);

        // Assert
        Assert.True(rate3Month > 0);
        Assert.True(rate6Month > 0);
        Assert.True(rate1Year > 0);
        
        // Generally, longer maturities should have higher rates (normal yield curve)
        // But we won't enforce this as yield curves can be inverted
        Assert.True(rate3Month > 0.01m && rate3Month < 0.2m);
        Assert.True(rate6Month > 0.01m && rate6Month < 0.2m);
        Assert.True(rate1Year > 0.01m && rate1Year < 0.2m);
    }

    [Fact]
    public async Task TreasuryRateService_GetServiceStatus_ReturnsHealthyStatus()
    {
        // Arrange - Populate cache first
        await _treasuryRateService.GetCurrentYieldCurveAsync();

        // Act
        var status = await _treasuryRateService.GetServiceStatusAsync();

        // Assert
        Assert.NotNull(status);
        Assert.Equal("FRED", status.PrimarySource);
        Assert.Equal("Fallback", status.SecondarySource);
        Assert.True(status.CachedRatesCount >= 0);
    }

    [Fact]
    public async Task OptionsPricingService_CompareWithAndWithoutTreasuryRates_ShowsDifference()
    {
        // Arrange
        var contract = new OptionContract
        {
            Symbol = "SPX241220C05000000",
            StrikePrice = 5000m,
            ExpirationDate = DateTime.UtcNow.AddDays(30),
            OptionType = OptionType.Call
        };

        var underlyingPrice = 5100m;
        var volatility = 0.20m;
        var timeToExpiry = 30m / 365m;

        // Act
        // Calculate with Treasury rate
        var realRate = await _treasuryRateService.GetRiskFreeRateAsync(timeToExpiry);
        var priceWithTreasuryRate = await _optionsPricingService.CalculateBlackScholesPrice(
            underlyingPrice, contract.StrikePrice, timeToExpiry, realRate, volatility, true);

        // Calculate with fixed rate
        var fixedRate = 0.05m;
        var priceWithFixedRate = await _optionsPricingService.CalculateBlackScholesPrice(
            underlyingPrice, contract.StrikePrice, timeToExpiry, fixedRate, volatility, true);

        // Assert
        Assert.True(priceWithTreasuryRate > 0);
        Assert.True(priceWithFixedRate > 0);
        
        // Prices should be different (unless Treasury rate happens to be exactly 5%)
        if (Math.Abs(realRate - fixedRate) > 0.001m)
        {
            Assert.NotEqual(priceWithTreasuryRate, priceWithFixedRate);
        }
    }

    protected virtual void Dispose(bool disposing)
    {
        if (disposing)
        {
            _serviceProvider?.Dispose();
        }
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }
}

/// <summary>
/// Performance tests for Treasury Rate service
/// </summary>
public class TreasuryRatePerformanceTests
{
    [Fact]
    public async Task TreasuryRateService_CachingPerformance_IsFast()
    {
        // This test would verify that cached requests are significantly faster
        // than fresh API calls, ensuring the caching mechanism works properly
        
        var services = new ServiceCollection();
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string>
            {
                ["FRED:ApiKey"] = "test_api_key",
                ["FRED:CacheExpiryMinutes"] = "60",
                ["FRED:FallbackRates:3Month"] = "0.0525"
            })
            .Build();

        services.AddSingleton<IConfiguration>(configuration);
        services.AddLogging();
        services.AddHttpClient<ITreasuryRateService, TreasuryRateService>();
        services.AddSingleton<ITreasuryRateService, TreasuryRateService>();

        using var serviceProvider = services.BuildServiceProvider();
        var treasuryService = serviceProvider.GetRequiredService<ITreasuryRateService>();

        // First call (should populate cache)
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        await treasuryService.GetRiskFreeRateAsync(0.25m);
        stopwatch.Stop();
        var firstCallTime = stopwatch.ElapsedMilliseconds;

        // Second call (should use cache)
        stopwatch.Restart();
        await treasuryService.GetRiskFreeRateAsync(0.25m);
        stopwatch.Stop();
        var secondCallTime = stopwatch.ElapsedMilliseconds;

        // Cache should make subsequent calls faster
        Assert.True(secondCallTime <= firstCallTime);
    }
}
