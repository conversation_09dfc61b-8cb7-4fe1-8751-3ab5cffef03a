using Alpaca.Markets;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using ZeroDateStrat.Models;

namespace ZeroDateStrat.Services;

public interface IAlpacaService
{
    Task<bool> InitializeAsync();
    Task<IAccount?> GetAccountAsync();
    Task<List<OptionContract>> GetOptionChainAsync(string symbol, DateTime expirationDate);
    Task<decimal> GetCurrentPriceAsync(string symbol);
    Task<IOrder?> PlaceOrderAsync(TradingSignal signal);
    Task<List<Alpaca.Markets.IPosition>> GetPositionsAsync();
    Task<bool> ClosePositionAsync(string positionId);
    Task<List<IOrder>> GetOrdersAsync();
    Task<List<AlpacaHistoricalBar>> GetHistoricalDataAsync(string symbol, DateTime startDate, DateTime endDate);

    // Phase 2: Enhanced Trading Methods
    Task<IOrder?> PlaceBracketOrderAsync(TradingSignal signal, decimal profitTarget, decimal stopLoss);
    Task<bool> ModifyOrderAsync(Guid orderId, decimal newPrice);
    Task<bool> CancelOrderAsync(Guid orderId);
    Task<List<IOrder>> GetActiveOrdersAsync();
    Task<decimal> GetPositionValueAsync(string symbol);
    Task<Dictionary<string, decimal>> GetPortfolioGreeksAsync();
    Task<bool> CloseAllPositionsAsync();
    Task<List<OptionContract>> GetRealTimeOptionChainAsync(string symbol, DateTime expirationDate);
}

public class AlpacaHistoricalBar
{
    public DateTime Timestamp { get; set; }
    public decimal Open { get; set; }
    public decimal High { get; set; }
    public decimal Low { get; set; }
    public decimal Close { get; set; }
    public long Volume { get; set; }
}

public class AlpacaService : IAlpacaService, IDisposable
{
    private readonly ILogger<AlpacaService> _logger;
    private readonly IConfiguration _configuration;
    private readonly ISecurityService _securityService;
    private readonly IGlobalExceptionHandler _exceptionHandler;
    private readonly IPolygonDataService _polygonDataService;
    private readonly IOptionsChainService? _optionsChainService;
    private readonly IIndexDataService? _indexDataService;
    private IAlpacaTradingClient? _tradingClient;
    private IAlpacaDataClient? _dataClient;
    private bool _isInitialized;

    public AlpacaService(
        ILogger<AlpacaService> logger,
        IConfiguration configuration,
        ISecurityService securityService,
        IGlobalExceptionHandler exceptionHandler,
        IPolygonDataService polygonDataService,
        IOptionsChainService? optionsChainService = null,
        IIndexDataService? indexDataService = null)
    {
        _logger = logger;
        _configuration = configuration;
        _securityService = securityService;
        _exceptionHandler = exceptionHandler;
        _polygonDataService = polygonDataService;
        _optionsChainService = optionsChainService;
        _indexDataService = indexDataService;
    }

    public async Task<bool> InitializeAsync()
    {
        try
        {
            _logger.LogInformation("Initializing Alpaca service with secure credentials");

            // Get secure credentials
            var apiKey = await _securityService.GetSecureApiKeyAsync();
            var secretKey = await _securityService.GetSecureSecretKeyAsync();
            var baseUrl = _configuration["Alpaca:BaseUrl"];
            var dataUrl = _configuration["Alpaca:DataUrl"];

            // Validate credentials format
            if (!await _securityService.ValidateApiKeyFormatAsync(apiKey))
            {
                _logger.LogError("Invalid API key format");
                return false;
            }

            if (!await _securityService.ValidateSecretKeyFormatAsync(secretKey))
            {
                _logger.LogError("Invalid secret key format");
                return false;
            }

            if (string.IsNullOrEmpty(apiKey) || string.IsNullOrEmpty(secretKey))
            {
                _logger.LogError("Alpaca API credentials not configured");
                return false;
            }

            var tradingClientConfiguration = new AlpacaTradingClientConfiguration
            {
                ApiEndpoint = new Uri(baseUrl ?? "https://paper-api.alpaca.markets"),
                SecurityId = new SecretKey(apiKey, secretKey)
            };

            var dataClientConfiguration = new AlpacaDataClientConfiguration
            {
                ApiEndpoint = new Uri(dataUrl ?? "https://data.alpaca.markets"),
                SecurityId = new SecretKey(apiKey, secretKey)
            };

            // Use the configured environment (live or paper) based on BaseUrl
            var isLiveTrading = baseUrl?.Contains("api.alpaca.markets") == true;

            if (isLiveTrading)
            {
                _tradingClient = Environments.Live.GetAlpacaTradingClient(new SecretKey(apiKey, secretKey));
                _dataClient = Environments.Live.GetAlpacaDataClient(new SecretKey(apiKey, secretKey));
                _logger.LogInformation("Connecting to Alpaca LIVE trading environment");
            }
            else
            {
                _tradingClient = Environments.Paper.GetAlpacaTradingClient(new SecretKey(apiKey, secretKey));
                _dataClient = Environments.Paper.GetAlpacaDataClient(new SecretKey(apiKey, secretKey));
                _logger.LogInformation("Connecting to Alpaca PAPER trading environment");
            }

            // Test connection
            var account = await _tradingClient.GetAccountAsync();
            _logger.LogInformation($"Connected to Alpaca. Account: {account.AccountNumber}, Equity: {account.Equity:C}");

            // Log security event
            await _securityService.LogSecurityEventAsync(new SecurityEvent
            {
                EventType = SecurityEventType.DataDecryption,
                Message = "Alpaca service initialized successfully with secure credentials",
                Timestamp = DateTime.UtcNow,
                Severity = SecuritySeverity.Low
            });

            _isInitialized = true;
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize Alpaca service");
            return false;
        }
    }

    public async Task<IAccount?> GetAccountAsync()
    {
        if (!_isInitialized || _tradingClient == null)
            return null;

        try
        {
            return await _tradingClient.GetAccountAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get account information");
            return null;
        }
    }

    public async Task<List<OptionContract>> GetOptionChainAsync(string symbol, DateTime expirationDate)
    {
        if (!_isInitialized || _tradingClient == null)
            return new List<OptionContract>();

        try
        {
            _logger.LogInformation($"Fetching option chain data for {symbol} expiring {expirationDate:yyyy-MM-dd}");

            // Get current underlying price first
            var currentPrice = await GetCurrentPriceAsync(symbol);
            if (currentPrice <= 0)
            {
                _logger.LogWarning($"Could not get current price for {symbol}");
                return new List<OptionContract>();
            }

            // Use the new OptionsChainService if available (with Polygon.io data)
            if (_optionsChainService != null)
            {
                _logger.LogDebug("Using enhanced options chain service with Polygon.io data");
                var optionChain = await _optionsChainService.GetOptionChainAsync(symbol, expirationDate, currentPrice);
                if (optionChain.Options.Any())
                {
                    _logger.LogInformation($"Retrieved {optionChain.Options.Count} option contracts from Polygon.io for {symbol}");
                    return optionChain.Options;
                }
                else
                {
                    _logger.LogWarning($"No options contracts found from Polygon.io for {symbol}, falling back to synthetic generation");
                }
            }

            // Fallback: Generate synthetic option chain using real underlying prices
            _logger.LogDebug("Using fallback synthetic option chain generation");

            var contracts = new List<OptionContract>();

            // Generate option chain around current price with realistic pricing
            for (int i = -10; i <= 10; i++)
            {
                var strike = Math.Round((currentPrice + (i * 5)) / 5) * 5;

                // Calculate more realistic option prices using Black-Scholes approximation
                var timeToExpiry = (decimal)(expirationDate - DateTime.Now).TotalDays / 365.25m;
                var moneyness = currentPrice / strike;

                // Simplified implied volatility estimate (would use real IV in production)
                var impliedVol = 0.20m; // 20% default IV

                // Call option
                var callPrice = CalculateOptionPrice(currentPrice, strike, timeToExpiry, impliedVol, true);
                var callDelta = CalculateDelta(currentPrice, strike, timeToExpiry, impliedVol, true);

                contracts.Add(new OptionContract
                {
                    Symbol = $"{symbol}{expirationDate:yyMMdd}C{strike:00000000}",
                    UnderlyingSymbol = symbol,
                    ExpirationDate = expirationDate,
                    StrikePrice = strike,
                    OptionType = Models.OptionType.Call,
                    Bid = Math.Max(0.01m, callPrice * 0.95m),
                    Ask = Math.Max(0.02m, callPrice * 1.05m),
                    LastPrice = callPrice,
                    Volume = GetRealisticVolume(moneyness),
                    OpenInterest = GetRealisticOpenInterest(moneyness),
                    Delta = callDelta,
                    ImpliedVolatility = impliedVol,
                    LastUpdated = DateTime.UtcNow
                });

                // Put option
                var putPrice = CalculateOptionPrice(currentPrice, strike, timeToExpiry, impliedVol, false);
                var putDelta = CalculateDelta(currentPrice, strike, timeToExpiry, impliedVol, false);

                contracts.Add(new OptionContract
                {
                    Symbol = $"{symbol}{expirationDate:yyMMdd}P{strike:00000000}",
                    UnderlyingSymbol = symbol,
                    ExpirationDate = expirationDate,
                    StrikePrice = strike,
                    OptionType = Models.OptionType.Put,
                    Bid = Math.Max(0.01m, putPrice * 0.95m),
                    Ask = Math.Max(0.02m, putPrice * 1.05m),
                    LastPrice = putPrice,
                    Volume = GetRealisticVolume(moneyness),
                    OpenInterest = GetRealisticOpenInterest(moneyness),
                    Delta = putDelta,
                    ImpliedVolatility = impliedVol,
                    LastUpdated = DateTime.UtcNow
                });
            }

            _logger.LogInformation($"Generated {contracts.Count} synthetic option contracts for {symbol} using real underlying price {currentPrice:C2}");
            return contracts;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to get option chain for {symbol}");
            return new List<OptionContract>();
        }
    }

    public async Task<decimal> GetCurrentPriceAsync(string symbol)
    {
        if (!_isInitialized || _dataClient == null)
            return 0;

        try
        {
            _logger.LogDebug($"Fetching current price for {symbol}");

            // Handle VIX by using VIX proxy ETFs available on Alpaca
            if (symbol == "VIX")
            {
                return await GetVixProxyPriceAsync();
            }

            // For major indices, use IndexDataService with three-tier hierarchy
            if (IsMajorIndex(symbol) && _indexDataService != null)
            {
                try
                {
                    var indexValue = await _indexDataService.GetCurrentIndexValueAsync(symbol);
                    if (indexValue > 0)
                    {
                        _logger.LogDebug($"Current price for {symbol} (from IndexDataService): {indexValue:C2}");
                        return indexValue;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogDebug(ex, $"Failed to get {symbol} data from IndexDataService");
                }
            }

            // Legacy SPX handling for backward compatibility
            if (symbol == "SPX" && _polygonDataService != null)
            {
                try
                {
                    // Note: SPX is an index, so GetCurrentQuoteAsync will automatically use last trade price
                    var polygonQuote = await _polygonDataService.GetCurrentQuoteAsync("I:SPX");
                    if (polygonQuote != null && polygonQuote.Bid > 0)
                    {
                        // For indices, bid and ask are the same (last trade price)
                        var price = polygonQuote.Bid;
                        _logger.LogDebug($"Current price for {symbol} (from Polygon index): {price:C2}");
                        return price;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogDebug(ex, $"Failed to get SPX data from Polygon for {symbol}");
                }
            }

            // Use Alpaca's Algo Trader Plus data for all other symbols
            try
            {
                // Try latest quote first (real-time with Algo Trader Plus)
                var quote = await _dataClient.GetLatestQuoteAsync(new LatestMarketDataRequest(symbol));
                if (quote != null && quote.BidPrice > 0 && quote.AskPrice > 0)
                {
                    // Use mid price (average of bid and ask)
                    var midPrice = (quote.BidPrice + quote.AskPrice) / 2;
                    _logger.LogDebug($"Current price for {symbol} (Alpaca real-time): {midPrice:C2}");
                    return (decimal)midPrice;
                }
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, $"Failed to get quote for {symbol}, trying trade data");
            }

            // Fallback to latest trade if quote not available
            try
            {
                var trade = await _dataClient.GetLatestTradeAsync(new LatestMarketDataRequest(symbol));
                if (trade != null && trade.Price > 0)
                {
                    _logger.LogDebug($"Current price for {symbol} (from trade): {trade.Price:C2}");
                    return (decimal)trade.Price;
                }
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, $"Failed to get trade data for {symbol}");
            }

            // Enhanced fallback with more realistic current market prices
            _logger.LogWarning($"No real-time data available for {symbol}, using enhanced fallback pricing");
            return symbol switch
            {
                "SPY" => 600m,    // S&P 500 ETF
                "SPX" => 6000m,   // S&P 500 Index
                "QQQ" => 520m,    // NASDAQ 100 ETF
                "NDX" => 21000m,  // NASDAQ 100 Index
                "IWM" => 240m,    // Russell 2000 ETF
                "RUT" => 2400m,   // Russell 2000 Index
                "DIA" => 450m,    // Dow Jones ETF
                "DJI" => 45000m,  // Dow Jones Index
                "VIX" => 18.5m,   // Fallback VIX level
                "VXX" => 25m,     // VIX ETF fallback
                "VIXY" => 12m,    // VIX ETF fallback
                "UVXY" => 8m,     // VIX ETF fallback
                _ => 100m
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to get current price for {symbol}");
            return 0;
        }
    }

    /// <summary>
    /// Gets VIX-equivalent data using VIX proxy ETFs available on Alpaca
    /// Uses VXX (primary), VIXY (secondary), UVXY (tertiary) as proxies
    /// </summary>
    private async Task<decimal> GetVixProxyPriceAsync()
    {
        try
        {
            _logger.LogDebug("Calculating VIX proxy using ETF data from Alpaca");

            // Try VXX first (most liquid VIX ETF)
            var vxxPrice = await GetRealTimePrice("VXX");
            if (vxxPrice > 0)
            {
                // Convert VXX price to VIX-equivalent
                // VXX typically trades at 10-15% of VIX level
                var vixEquivalent = vxxPrice * 0.8m; // Approximate conversion factor
                _logger.LogDebug($"VIX proxy from VXX: {vxxPrice:F2} -> VIX equivalent: {vixEquivalent:F2}");
                return Math.Max(vixEquivalent, 10m); // Minimum reasonable VIX level
            }

            // Try VIXY as backup
            var vixyPrice = await GetRealTimePrice("VIXY");
            if (vixyPrice > 0)
            {
                // VIXY typically trades at 50-70% of VIX level
                var vixEquivalent = vixyPrice * 1.5m; // Approximate conversion factor
                _logger.LogDebug($"VIX proxy from VIXY: {vixyPrice:F2} -> VIX equivalent: {vixEquivalent:F2}");
                return Math.Max(vixEquivalent, 10m);
            }

            // Try UVXY as last resort
            var uvxyPrice = await GetRealTimePrice("UVXY");
            if (uvxyPrice > 0)
            {
                // UVXY is 2x leveraged, typically trades at 30-50% of VIX level
                var vixEquivalent = uvxyPrice * 2.5m; // Approximate conversion factor
                _logger.LogDebug($"VIX proxy from UVXY: {uvxyPrice:F2} -> VIX equivalent: {vixEquivalent:F2}");
                return Math.Max(vixEquivalent, 10m);
            }

            _logger.LogWarning("All VIX proxy ETFs unavailable, using fallback VIX value");
            return 20.0m; // Conservative fallback
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating VIX proxy");
            return 20.0m; // Conservative fallback
        }
    }

    /// <summary>
    /// Gets real-time price for a symbol using Alpaca's Algo Trader Plus data
    /// </summary>
    private async Task<decimal> GetRealTimePrice(string symbol)
    {
        try
        {
            if (_dataClient == null) return 0;

            // Get real-time quote
            var quote = await _dataClient.GetLatestQuoteAsync(new LatestMarketDataRequest(symbol));
            if (quote != null && quote.BidPrice > 0 && quote.AskPrice > 0)
            {
                return (decimal)((quote.BidPrice + quote.AskPrice) / 2);
            }

            // Fallback to trade data
            var trade = await _dataClient.GetLatestTradeAsync(new LatestMarketDataRequest(symbol));
            if (trade != null && trade.Price > 0)
            {
                return (decimal)trade.Price;
            }

            return 0;
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, $"Failed to get real-time price for {symbol}");
            return 0;
        }
    }

    public async Task<IOrder?> PlaceOrderAsync(TradingSignal signal)
    {
        if (!_isInitialized || _tradingClient == null)
            return null;

        try
        {
            _logger.LogInformation($"Placing {signal.Strategy} order for {signal.UnderlyingSymbol} with {signal.Legs.Count} legs");

            // Phase 2: Enhanced Multi-Leg Order Execution
            if (signal.Legs.Count == 1)
            {
                return await PlaceSingleLegOrderAsync(signal.Legs.First());
            }
            else
            {
                return await PlaceMultiLegOrderAsync(signal);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to place order for signal {signal.Id}");

            // Send Discord notification for trading error
            await _exceptionHandler.HandleTradingExceptionAsync(ex, $"Order Placement - {signal.Strategy} for {signal.UnderlyingSymbol}");

            return null;
        }
    }

    // Phase 2: Single leg order execution with enhanced validation
    private async Task<IOrder?> PlaceSingleLegOrderAsync(OptionLeg leg)
    {
        try
        {
            // Validate order parameters
            if (leg.Quantity <= 0 || leg.Price <= 0)
            {
                _logger.LogWarning($"Invalid order parameters: Quantity={leg.Quantity}, Price={leg.Price}");
                return null;
            }

            var orderRequest = new NewOrderRequest(
                leg.Symbol,
                leg.Quantity,
                leg.Side == Models.OrderSide.Buy ? Alpaca.Markets.OrderSide.Buy : Alpaca.Markets.OrderSide.Sell,
                OrderType.Limit,
                TimeInForce.Day)
            {
                LimitPrice = leg.Price
            };

            var order = await _tradingClient.PostOrderAsync(orderRequest);
            _logger.LogInformation($"Single leg order placed: {order.OrderId} for {leg.Symbol}");
            return order;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to place single leg order for {leg.Symbol}");
            return null;
        }
    }

    // Phase 2: Multi-leg order execution for spreads
    private async Task<IOrder?> PlaceMultiLegOrderAsync(TradingSignal signal)
    {
        try
        {
            _logger.LogInformation($"Executing multi-leg strategy: {signal.Strategy}");

            // For now, execute legs sequentially with small delays
            // In production, you'd use bracket orders or multi-leg order types
            var orders = new List<IOrder>();

            foreach (var leg in signal.Legs.OrderBy(l => l.Side == Models.OrderSide.Sell ? 0 : 1)) // Sell legs first
            {
                var order = await PlaceSingleLegOrderAsync(leg);
                if (order != null)
                {
                    orders.Add(order);
                    _logger.LogInformation($"Leg executed: {leg.Symbol} {leg.Side} {leg.Quantity} @ {leg.Price}");

                    // Small delay between legs to avoid rejection
                    await Task.Delay(100);
                }
                else
                {
                    _logger.LogError($"Failed to execute leg: {leg.Symbol}");

                    // Cancel previous legs if this one fails
                    await CancelOrdersAsync(orders);
                    return null;
                }
            }

            // Return the first order as representative (in real implementation, return spread order ID)
            return orders.FirstOrDefault();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to place multi-leg order for {signal.Strategy}");
            return null;
        }
    }

    // Phase 2: Enhanced order cancellation
    private async Task CancelOrdersAsync(List<IOrder> orders)
    {
        foreach (var order in orders)
        {
            try
            {
                await _tradingClient.CancelOrderAsync(order.OrderId);
                _logger.LogInformation($"Cancelled order: {order.OrderId}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to cancel order: {order.OrderId}");
            }
        }
    }

    public async Task<List<Alpaca.Markets.IPosition>> GetPositionsAsync()
    {
        if (!_isInitialized || _tradingClient == null)
            return new List<Alpaca.Markets.IPosition>();

        try
        {
            var positions = await _tradingClient.ListPositionsAsync();
            return positions.ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get positions");
            return new List<Alpaca.Markets.IPosition>();
        }
    }

    public async Task<bool> ClosePositionAsync(string positionId)
    {
        if (!_isInitialized || _tradingClient == null)
            return false;

        try
        {
            await _tradingClient.DeletePositionAsync(new DeletePositionRequest(positionId));
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to close position {positionId}");
            return false;
        }
    }

    public async Task<List<IOrder>> GetOrdersAsync()
    {
        if (!_isInitialized || _tradingClient == null)
            return new List<IOrder>();

        try
        {
            var orders = await _tradingClient.ListOrdersAsync(new ListOrdersRequest());
            return orders.ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get orders");
            return new List<IOrder>();
        }
    }

    public async Task<List<AlpacaHistoricalBar>> GetHistoricalDataAsync(string symbol, DateTime startDate, DateTime endDate)
    {
        if (!_isInitialized || _dataClient == null)
            return new List<AlpacaHistoricalBar>();

        try
        {
            _logger.LogDebug($"Fetching historical data for {symbol} from {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}");

            var request = new HistoricalBarsRequest(symbol, BarTimeFrame.Day, new Interval<DateTime>(startDate, endDate));

            var response = await _dataClient.GetHistoricalBarsAsync(request);

            var historicalBars = new List<AlpacaHistoricalBar>();

            foreach (var symbolData in response.Items)
            {
                foreach (var bar in symbolData.Value)
                {
                    historicalBars.Add(new AlpacaHistoricalBar
                    {
                        Timestamp = bar.TimeUtc,
                        Open = (decimal)bar.Open,
                        High = (decimal)bar.High,
                        Low = (decimal)bar.Low,
                        Close = (decimal)bar.Close,
                        Volume = (long)bar.Volume
                    });
                }
            }

            _logger.LogDebug($"Retrieved {historicalBars.Count} historical bars for {symbol}");
            return historicalBars.OrderBy(b => b.Timestamp).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to get historical data for {symbol}");

            // Fallback: Generate synthetic historical data for testing
            _logger.LogWarning($"Using synthetic historical data for {symbol}");
            return GenerateSyntheticHistoricalData(symbol, startDate, endDate);
        }
    }

    private List<AlpacaHistoricalBar> GenerateSyntheticHistoricalData(string symbol, DateTime startDate, DateTime endDate)
    {
        var bars = new List<AlpacaHistoricalBar>();
        var currentPrice = symbol switch
        {
            "SPY" => 600m,    // S&P 500 ETF
            "SPX" => 6000m,   // S&P 500 Index
            "QQQ" => 520m,    // NASDAQ 100 ETF
            "NDX" => 21000m,  // NASDAQ 100 Index
            "IWM" => 240m,    // Russell 2000 ETF
            "RUT" => 2400m,   // Russell 2000 Index
            "DIA" => 450m,    // Dow Jones ETF
            "DJI" => 45000m,  // Dow Jones Index
            _ => 100m
        };

        // Ensure we have enough data by extending the start date if needed
        var extendedStartDate = startDate;
        var requestedDays = (endDate - startDate).Days;
        var minimumDays = 100; // Ensure at least 100 days of data

        if (requestedDays < minimumDays)
        {
            extendedStartDate = endDate.AddDays(-minimumDays);
            _logger.LogDebug($"Extended start date to {extendedStartDate:yyyy-MM-dd} to ensure sufficient data for {symbol}");
        }

        var random = new Random(symbol.GetHashCode()); // Deterministic for consistency
        var currentDate = extendedStartDate;
        var price = currentPrice;

        // Add market regime simulation for more realistic patterns
        var volatilityBase = symbol switch
        {
            "SPY" or "SPX" => 0.015m,
            "QQQ" or "NDX" => 0.020m,
            "IWM" or "RUT" => 0.025m,
            "DIA" or "DJI" => 0.018m,
            _ => 0.020m
        };

        while (currentDate <= endDate)
        {
            // Skip weekends
            if (currentDate.DayOfWeek != DayOfWeek.Saturday && currentDate.DayOfWeek != DayOfWeek.Sunday)
            {
                // Generate realistic daily price movement with varying volatility
                var volatilityMultiplier = GetVolatilityMultiplierForDate(currentDate);
                var adjustedVolatility = volatilityBase * volatilityMultiplier;
                var dailyChange = (decimal)(NextGaussian(random) * (double)adjustedVolatility);

                var open = price;
                var close = price * (1 + dailyChange);

                // More realistic intraday range
                var intradayRange = (decimal)(random.NextDouble() * 0.015 + 0.005);
                var high = Math.Max(open, close) * (1 + intradayRange);
                var low = Math.Min(open, close) * (1 - intradayRange);

                // Realistic volume with patterns
                var baseVolume = symbol switch
                {
                    "SPY" => 80_000_000,
                    "SPX" => 200_000,
                    "QQQ" => 40_000_000,
                    "NDX" => 150_000,
                    "IWM" => 25_000_000,
                    "RUT" => 180_000,
                    "DIA" => 15_000_000,
                    "DJI" => 100_000,
                    _ => 5_000_000
                };
                var volumeVariation = (decimal)(random.NextDouble() * 0.6 + 0.7); // 70% to 130%
                var volume = (long)(baseVolume * volumeVariation);

                bars.Add(new AlpacaHistoricalBar
                {
                    Timestamp = currentDate,
                    Open = open,
                    High = high,
                    Low = low,
                    Close = close,
                    Volume = volume
                });

                price = close;
            }
            currentDate = currentDate.AddDays(1);
        }

        _logger.LogDebug($"Generated {bars.Count} synthetic historical bars for {symbol} from {extendedStartDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}");
        return bars;
    }

    private decimal GetVolatilityMultiplierForDate(DateTime date)
    {
        // Simulate different market regimes based on date patterns
        var dayOfYear = date.DayOfYear;
        var weekOfYear = dayOfYear / 7;

        // Create some cyclical volatility patterns
        var baseMultiplier = 1.0m;

        // Higher volatility in certain periods (simulating earnings seasons, FOMC meetings, etc.)
        if (weekOfYear % 13 == 0) baseMultiplier *= 1.5m; // Quarterly volatility spikes
        if (date.Month == 3 || date.Month == 9) baseMultiplier *= 1.2m; // March/September volatility
        if (date.DayOfWeek == DayOfWeek.Friday) baseMultiplier *= 1.1m; // Friday effect

        return baseMultiplier;
    }

    private double NextGaussian(Random random, double mean = 0, double stdDev = 1)
    {
        // Box-Muller transform for Gaussian random numbers
        var u1 = 1.0 - random.NextDouble();
        var u2 = 1.0 - random.NextDouble();
        var randStdNormal = Math.Sqrt(-2.0 * Math.Log(u1)) * Math.Sin(2.0 * Math.PI * u2);
        return mean + stdDev * randStdNormal;
    }

    private decimal CalculateOptionPrice(decimal spotPrice, decimal strikePrice, decimal timeToExpiry, decimal volatility, bool isCall)
    {
        // Simplified Black-Scholes approximation for option pricing
        // In production, you'd use a proper options pricing library

        if (timeToExpiry <= 0)
            return Math.Max(0, isCall ? spotPrice - strikePrice : strikePrice - spotPrice);

        var riskFreeRate = 0.05m; // 5% risk-free rate assumption
        var d1 = (decimal)(Math.Log((double)(spotPrice / strikePrice)) + (double)(riskFreeRate + volatility * volatility / 2) * (double)timeToExpiry) / (decimal)((double)volatility * Math.Sqrt((double)timeToExpiry));
        var d2 = d1 - (decimal)((double)volatility * Math.Sqrt((double)timeToExpiry));

        var nd1 = NormalCDF((double)d1);
        var nd2 = NormalCDF((double)d2);
        var nMinusD1 = NormalCDF(-(double)d1);
        var nMinusD2 = NormalCDF(-(double)d2);

        if (isCall)
        {
            return spotPrice * (decimal)nd1 - strikePrice * (decimal)Math.Exp(-(double)riskFreeRate * (double)timeToExpiry) * (decimal)nd2;
        }
        else
        {
            return strikePrice * (decimal)Math.Exp(-(double)riskFreeRate * (double)timeToExpiry) * (decimal)nMinusD2 - spotPrice * (decimal)nMinusD1;
        }
    }

    private decimal CalculateDelta(decimal spotPrice, decimal strikePrice, decimal timeToExpiry, decimal volatility, bool isCall)
    {
        if (timeToExpiry <= 0)
            return isCall ? (spotPrice > strikePrice ? 1 : 0) : (spotPrice < strikePrice ? -1 : 0);

        var riskFreeRate = 0.05m;
        var d1 = (decimal)(Math.Log((double)(spotPrice / strikePrice)) + (double)(riskFreeRate + volatility * volatility / 2) * (double)timeToExpiry) / (decimal)((double)volatility * Math.Sqrt((double)timeToExpiry));

        return isCall ? (decimal)NormalCDF((double)d1) : (decimal)NormalCDF((double)d1) - 1;
    }

    private static double NormalCDF(double x)
    {
        // Approximation of the cumulative distribution function for standard normal distribution
        return 0.5 * (1 + Math.Sign(x) * Math.Sqrt(1 - Math.Exp(-2 * x * x / Math.PI)));
    }

    private decimal GetRealisticVolume(decimal moneyness)
    {
        // Generate realistic volume based on moneyness (how close to ATM)
        var atmDistance = Math.Abs(1 - moneyness);
        var baseVolume = 100m;

        if (atmDistance < 0.02m) return baseVolume * 5; // High volume near ATM
        if (atmDistance < 0.05m) return baseVolume * 3;
        if (atmDistance < 0.10m) return baseVolume * 2;
        return baseVolume;
    }

    private decimal GetRealisticOpenInterest(decimal moneyness)
    {
        // Generate realistic open interest based on moneyness
        var atmDistance = Math.Abs(1 - moneyness);
        var baseOI = 500m;

        if (atmDistance < 0.02m) return baseOI * 4; // High OI near ATM
        if (atmDistance < 0.05m) return baseOI * 2.5m;
        if (atmDistance < 0.10m) return baseOI * 1.5m;
        return baseOI;
    }

    // Phase 2: Enhanced Trading Methods Implementation
    public async Task<IOrder?> PlaceBracketOrderAsync(TradingSignal signal, decimal profitTarget, decimal stopLoss)
    {
        if (!_isInitialized || _tradingClient == null)
            return null;

        try
        {
            _logger.LogInformation($"Placing bracket order for {signal.Strategy} with profit target {profitTarget:P1} and stop loss {stopLoss:P1}");

            // For now, place the main order and set up monitoring for profit/stop levels
            var mainOrder = await PlaceOrderAsync(signal);
            if (mainOrder != null)
            {
                // In a real implementation, you'd place OCO (One-Cancels-Other) orders
                // For now, we'll track these levels for manual management
                _logger.LogInformation($"Main order placed: {mainOrder.OrderId}. Profit target: {profitTarget:C}, Stop loss: {stopLoss:C}");
            }

            return mainOrder;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to place bracket order");
            return null;
        }
    }

    public async Task<bool> ModifyOrderAsync(Guid orderId, decimal newPrice)
    {
        if (!_isInitialized || _tradingClient == null)
            return false;

        try
        {
            var changeRequest = new ChangeOrderRequest(orderId)
            {
                LimitPrice = newPrice
            };

            await _tradingClient.PatchOrderAsync(changeRequest);
            _logger.LogInformation($"Modified order {orderId} to new price: {newPrice:C}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to modify order {orderId}");
            return false;
        }
    }

    public async Task<bool> CancelOrderAsync(Guid orderId)
    {
        if (!_isInitialized || _tradingClient == null)
            return false;

        try
        {
            await _tradingClient.CancelOrderAsync(orderId);
            _logger.LogInformation($"Cancelled order: {orderId}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to cancel order {orderId}");
            return false;
        }
    }

    public async Task<List<IOrder>> GetActiveOrdersAsync()
    {
        if (!_isInitialized || _tradingClient == null)
            return new List<IOrder>();

        try
        {
            var orders = await _tradingClient.ListOrdersAsync(new ListOrdersRequest
            {
                OrderStatusFilter = OrderStatusFilter.Open
            });

            return orders.ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get active orders");
            return new List<IOrder>();
        }
    }

    public async Task<decimal> GetPositionValueAsync(string symbol)
    {
        if (!_isInitialized || _tradingClient == null)
            return 0;

        try
        {
            var positions = await GetPositionsAsync();
            var position = positions.FirstOrDefault(p => p.Symbol == symbol);

            if (position != null)
            {
                var currentPrice = await GetCurrentPriceAsync(symbol);
                return position.Quantity * currentPrice;
            }

            return 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to get position value for {symbol}");
            return 0;
        }
    }

    public async Task<Dictionary<string, decimal>> GetPortfolioGreeksAsync()
    {
        var greeks = new Dictionary<string, decimal>
        {
            ["Delta"] = 0,
            ["Gamma"] = 0,
            ["Theta"] = 0,
            ["Vega"] = 0
        };

        try
        {
            var positions = await GetPositionsAsync();

            foreach (var position in positions)
            {
                // For options positions, calculate and sum Greeks
                if (IsOptionSymbol(position.Symbol))
                {
                    var optionGreeks = await CalculatePositionGreeksAsync(position);
                    greeks["Delta"] += optionGreeks["Delta"];
                    greeks["Gamma"] += optionGreeks["Gamma"];
                    greeks["Theta"] += optionGreeks["Theta"];
                    greeks["Vega"] += optionGreeks["Vega"];
                }
            }

            _logger.LogInformation($"Portfolio Greeks - Delta: {greeks["Delta"]:F2}, Gamma: {greeks["Gamma"]:F2}, Theta: {greeks["Theta"]:F2}, Vega: {greeks["Vega"]:F2}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to calculate portfolio Greeks");
        }

        return greeks;
    }

    public async Task<bool> CloseAllPositionsAsync()
    {
        if (!_isInitialized || _tradingClient == null)
            return false;

        try
        {
            await _tradingClient.DeleteAllPositionsAsync();
            _logger.LogInformation("Closed all positions");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to close all positions");
            return false;
        }
    }

    public async Task<List<OptionContract>> GetRealTimeOptionChainAsync(string symbol, DateTime expirationDate)
    {
        // For now, return the same as GetOptionChainAsync
        // In production, this would use real-time options data feeds
        _logger.LogInformation($"Getting real-time option chain for {symbol} expiring {expirationDate:yyyy-MM-dd}");
        return await GetOptionChainAsync(symbol, expirationDate);
    }

    // Helper methods for Phase 2
    private bool IsOptionSymbol(string symbol)
    {
        // Simple check for option symbol format
        return symbol.Length > 10 && (symbol.Contains('C') || symbol.Contains('P'));
    }

    private async Task<Dictionary<string, decimal>> CalculatePositionGreeksAsync(Alpaca.Markets.IPosition position)
    {
        var greeks = new Dictionary<string, decimal>
        {
            ["Delta"] = 0,
            ["Gamma"] = 0,
            ["Theta"] = 0,
            ["Vega"] = 0
        };

        try
        {
            // Simplified Greeks calculation for demonstration
            // In production, you'd use real-time option data and proper Greeks calculation
            var quantity = position.Quantity;

            // Estimate Greeks based on position type and size
            if (position.Symbol.Contains('P')) // Put option
            {
                greeks["Delta"] = quantity * -0.3m; // Approximate delta for puts
                greeks["Theta"] = quantity * -0.05m; // Time decay
            }
            else if (position.Symbol.Contains('C')) // Call option
            {
                greeks["Delta"] = quantity * 0.3m; // Approximate delta for calls
                greeks["Theta"] = quantity * -0.05m; // Time decay
            }

            greeks["Gamma"] = Math.Abs(quantity) * 0.02m; // Gamma is always positive
            greeks["Vega"] = Math.Abs(quantity) * 0.1m; // Vega sensitivity
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to calculate Greeks for position {position.Symbol}");
        }

        return greeks;
    }

    /// <summary>
    /// Checks if a symbol is a major index that should use the IndexDataService
    /// </summary>
    private static bool IsMajorIndex(string symbol)
    {
        return symbol switch
        {
            "SPX" or "NDX" or "RUT" or "DJI" => true,
            _ => false
        };
    }

    public void Dispose()
    {
        _tradingClient?.Dispose();
        _dataClient?.Dispose();
    }
}
