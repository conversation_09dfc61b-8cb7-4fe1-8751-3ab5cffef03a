# ZeroDateStrat Project Memory

## System Overview
ZeroDateStrat is a sophisticated C# application for automated 0 Days to Expiration (0 DTE) options trading using the Alpaca Markets API. The system has evolved through three major phases and now includes advanced AI/ML capabilities, comprehensive risk management, and production-ready infrastructure with Discord integration and ChatGPT bot assistance.

## Current Status (December 2024)
- **Status**: LIVE TRADING ACTIVE
- **Account**: ********* ($12,035.00)
- **Environment**: Alpaca Live Trading
- **Security Score**: 100% - SECURE

## Account Information
- **Account Number**: *********
- **Current Balance**: $12,035.00 (verified)
- **Account Status**: Active
- **Buying Power**: $24,070.00
- **Previous Assumption**: $2,035 (OUTDATED - corrected to actual $12,035)

## Risk Parameters (Live Trading - Enhanced Safety)
- **Max Position Size**: $1,000 (reduced from $1,500 for live trading)
- **Max Daily Loss**: $150 (reduced from $180 for live trading)
- **Risk Per Trade**: 1% ($120.35 max risk per position)
- **Max Positions Per Day**: 2 (reduced from 3 for live trading)
- **Min Days to Expiration**: 0 (0-DTE focus)
- **Max Days to Expiration**: 0 (0-DTE focus)

## Trading Schedule
- **Entry Window**: 9:45 AM - 10:30 AM EST
- **Management Time**: 2:00 PM EST
- **Force Close Time**: 3:45 PM EST
- **Trading End Time**: 4:00 PM EST

## Enabled Strategies
1. **Put Credit Spreads** (Priority 1)
   - Min Delta: 0.05, Max Delta: 0.15
   - Min Premium: $0.10
   - Max Spread Width: 10 points
   - Profit Target: 50%
   - Stop Loss: 200%

2. **Iron Butterfly** (Priority 2)
   - ATM Range: 2%
   - Wing Width: 25 points
   - Min Premium: $0.15
   - Profit Target: 50%
   - Stop Loss: 200%

3. **Call Credit Spreads** (Priority 3)
   - Min Delta: 0.05, Max Delta: 0.15
   - Min Premium: $0.10
   - Max Spread Width: 10 points
   - Profit Target: 50%
   - Stop Loss: 200%

4. **Iron Condor** (Priority 2)
   - Min Delta: 0.05, Max Delta: 0.10
   - Min Premium: $0.30
   - Wing Width: 15 points
   - Profit Target: 50%
   - Stop Loss: 200%

5. **Broken Wing Butterfly** (Priority 4)
   - Min Premium: $0.50
   - Profit Target: 60%
   - Stop Loss: 200%

## Market Regime Configuration
- **VIX Low Threshold**: 25
- **VIX High Threshold**: 55
- **Allow High Volatility Trading**: true
- **High Volatility Max Positions**: 1
- **High Volatility Risk Reduction**: 50%

## Synthetic VIX Configuration
Uses composite index of:
- **VXX**: 50% weight
- **UVXY**: 30% weight
- **SVXY**: -20% weight (inverse)
- **Normalization**: Z-score with 20-period window

## Enhanced Risk Controls
- **Max Drawdown**: 5%
- **VaR Limit**: 2%
- **Max Concentration**: 50%
- **Portfolio Heat Limit**: 60%
- **Max Open Positions**: 3
- **Stress Test Multiplier**: 2.0x
- **Max Positions Per Symbol**: 2

## Monitoring & Alerts
- **Update Interval**: 2 seconds (enhanced for live trading)
- **Alert Check Interval**: 5 seconds (enhanced for live trading)
- **Health Check Interval**: 15 seconds (enhanced for live trading)
- **Discord Notifications**: ACTIVE
- **Console Logging**: Real-time
- **File Logging**: All trades and events logged

## Discord Integration
- **Bot Token**: Configured and active
- **Channel ID**: 1382148371103350799
- **Username**: AugmentBot
- **Alerts**: Risk alerts, trade notifications, system status
- **Commands**: Emergency stop, status checks available

## ChatGPT Bot Integration
- **Status**: Active
- **Trigger Keywords**: !askchatgpt, !chatgpt, !gpt
- **Mention Trigger**: Enabled
- **Response Prefix**: 🤖 **ChatGPT Response:**
- **Max Message Length**: 2000 characters

## Security Features
- **Credential Encryption**: Active
- **Security Audit Score**: 100%
- **Data Decryption Events**: Logged
- **Security Environment**: SECURE

## Circuit Breakers
- **Alpaca API**: 5 failures, 5-minute timeout
- **Options Data**: 3 failures, 3-minute timeout
- **Market Data**: 3 failures, 2-minute timeout
- **Risk Management**: 2 failures, 1-minute timeout
- **Order Execution**: 2 failures, 1-minute timeout

## Emergency Procedures
- **Emergency Stop File**: Create `EMERGENCY_STOP.txt` in project directory
- **Manual Override**: Available through Discord commands
- **Circuit Breakers**: Automatic system protection
- **Force Close Time**: 3:45 PM EST (all positions closed)

## API Configurations
- **Alpaca**: Live trading environment (https://api.alpaca.markets)
- **Polygon**: Enhanced subscription (Indices Starter + Options Starter)
  - Real-time SPX/VIX data via indices endpoints
  - Complete options chain data access
  - 15-minute delayed data (appropriate for subscription tier)
  - Calendar-aware 0 DTE detection
- **OpenAI**: ChatGPT integration for guidance requests

## Machine Learning Configuration
- **Model Update Interval**: 24 hours
- **Min Training Data Points**: 100
- **Confidence Threshold**: 70%
- **Signal Quality Weights**: ML 40%, Technical 30%, Market Condition 20%, Liquidity 10%

## Performance Targets
- **Min Win Rate**: 60%
- **Min Sharpe Ratio**: 1.0
- **Max Drawdown**: 10%
- **Portfolio Optimization**: Enabled
- **Adaptive Parameters**: Enabled

## System Architecture Summary
- **Phase 3 Development**: Advanced Intelligence & Production Optimization
- **Service Layer**: 40+ specialized services for trading, monitoring, and communication
- **AI/ML Integration**: Machine learning for signal quality and strategy optimization
- **Real-time Monitoring**: Comprehensive system health and performance tracking
- **Discord Integration**: Real-time alerts, commands, and ChatGPT bot assistance
- **Production Infrastructure**: Enterprise-grade reliability and safety mechanisms

## Complete Service Inventory
**Core Trading**: AlpacaService, ZeroDteStrategy, OptionsScanner, RiskManager, PositionManager
**Market Data**: PolygonDataService, MarketRegimeAnalyzer, SyntheticVixService, TradingCalendarService
**AI/ML**: MachineLearningService, AdvancedStrategyOptimizer, BacktestingEngine, PerformanceAnalytics
**Monitoring**: RealTimeMonitoringService, ProductionInfrastructureService, TradingSystemHealthCheck
**Communication**: DiscordService, ChatGPTDiscordBot, TradingNotificationService, GuidanceRequestService
**Security**: SecurityService, ConfigurationValidator, GlobalExceptionHandler

## Recent Deployment History
- **December 2024**: Went live with production trading
- **System Reindex**: Complete codebase review and documentation update
- **Safety Validation Score**: 98% (Excellent) → 100% (Secure)
- **Account Balance Correction**: Updated from $2,035 to actual $12,035
- **Risk Parameters**: Optimized for larger account size with enhanced safety
- **Polygon Subscription Upgrade**: Enhanced to Indices Starter + Options Starter
  - Real-time SPX/VIX data access
  - Complete options chain data
  - Calendar-aware 0 DTE detection
  - Comprehensive testing and documentation
- **Production Readiness**: Confirmed ready and actively trading live

## Key Success Factors
1. **Conservative Risk Management**: 1% risk per trade, strict position limits
2. **Comprehensive Monitoring**: Real-time alerts and logging with Discord integration
3. **Multiple Safety Layers**: Circuit breakers, emergency stops, force close mechanisms
4. **Account Size Appropriate**: Risk parameters scaled correctly for $12,035 account
5. **Strategy Diversification**: Multiple 0-DTE strategies with AI/ML optimization
6. **Real-time Communication**: Discord integration with ChatGPT bot assistance
7. **Production Infrastructure**: Enterprise-grade reliability and monitoring

## Next Steps
- **Monitor Live Performance**: Track daily P&L and strategy effectiveness in live trading
- **AI/ML Optimization**: Leverage machine learning for continuous strategy improvement
- **Scale Gradually**: Increase position sizes based on live trading success
- **System Enhancement**: Regular updates based on performance data and market conditions
- **Documentation Maintenance**: Keep AugmentMemories system updated with trading results
