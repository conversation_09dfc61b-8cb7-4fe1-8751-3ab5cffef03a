using Discord;
using Discord.Net;
using Discord.WebSocket;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using ZeroDateStrat.Models;

namespace ZeroDateStrat.Services;

/// <summary>
/// Separate ChatGPT Discord bot that listens for mentions and responds with OpenAI API
/// </summary>
public interface IChatGPTDiscordBot
{
    Task StartAsync(CancellationToken cancellationToken = default);
    Task StopAsync(CancellationToken cancellationToken = default);
    Task<bool> IsConnectedAsync();
}

public class ChatGPTDiscordBot : IChatGPTDiscordBot, IHostedService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<ChatGPTDiscordBot> _logger;
    private readonly IOpenAIService _openAIService;
    private DiscordSocketClient? _client;
    private bool _isRunning = false;
    private ulong _channelId;
    private ChatGPTBotConfiguration _config;
    private OpenAIConfiguration _openAIConfig;

    public ChatGPTDiscordBot(
        IConfiguration configuration, 
        ILogger<ChatGPTDiscordBot> logger,
        IOpenAIService openAIService)
    {
        _configuration = configuration;
        _logger = logger;
        _openAIService = openAIService;
        
        // Load configuration
        _config = new ChatGPTBotConfiguration();
        configuration.GetSection("ChatGPTBot").Bind(_config);
        
        _openAIConfig = new OpenAIConfiguration();
        configuration.GetSection("OpenAI").Bind(_openAIConfig);
    }

    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            if (!_config.Enabled)
            {
                _logger.LogInformation("ChatGPT Discord bot is disabled");
                return;
            }

            var botToken = Environment.GetEnvironmentVariable("CHATGPT_BOT_TOKEN") ?? _config.BotToken;
            _channelId = _config.ChannelId;

            if (string.IsNullOrEmpty(botToken) || _channelId == 0)
            {
                _logger.LogWarning("ChatGPT bot token or channel ID not configured");
                return;
            }

            _client = new DiscordSocketClient();
            
            _client.Log += LogAsync;
            _client.Ready += ReadyAsync;
            _client.MessageReceived += MessageReceivedAsync;

            await _client.LoginAsync(TokenType.Bot, botToken);
            await _client.StartAsync();

            _isRunning = true;
            _logger.LogInformation("ChatGPT Discord bot started successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start ChatGPT Discord bot");
        }
    }

    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            if (_client != null)
            {
                await _client.StopAsync();
                await _client.DisposeAsync();
                _client = null;
            }
            _isRunning = false;
            _logger.LogInformation("ChatGPT Discord bot stopped");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping ChatGPT Discord bot");
        }
    }

    public async Task<bool> IsConnectedAsync()
    {
        return _client?.ConnectionState == ConnectionState.Connected;
    }

    private Task LogAsync(LogMessage log)
    {
        var logLevel = log.Severity switch
        {
            LogSeverity.Critical => LogLevel.Critical,
            LogSeverity.Error => LogLevel.Error,
            LogSeverity.Warning => LogLevel.Warning,
            LogSeverity.Info => LogLevel.Information,
            LogSeverity.Verbose => LogLevel.Debug,
            LogSeverity.Debug => LogLevel.Trace,
            _ => LogLevel.Information
        };

        _logger.Log(logLevel, log.Exception, "[ChatGPTBot] {Message}", log.Message);
        return Task.CompletedTask;
    }

    private Task ReadyAsync()
    {
        _logger.LogInformation("ChatGPT Discord bot is connected and ready!");
        return Task.CompletedTask;
    }

    private async Task MessageReceivedAsync(SocketMessage message)
    {
        // Skip messages from bots (including ourselves)
        if (message.Author.IsBot)
            return;

        // Only process messages in the configured channel
        if (message.Channel.Id != _channelId)
            return;

        try
        {
            // Check if this message should trigger ChatGPT response
            if (ShouldTriggerChatGPT(message))
            {
                await HandleChatGPTRequestAsync(message);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing message in ChatGPT bot");
            try
            {
                await message.Channel.SendMessageAsync(_config.ErrorMessage);
            }
            catch (Exception sendEx)
            {
                _logger.LogError(sendEx, "Failed to send error message");
            }
        }
    }

    private bool ShouldTriggerChatGPT(SocketMessage message)
    {
        if (string.IsNullOrWhiteSpace(message.Content))
            return false;

        var content = message.Content.Trim();

        // Check for mention triggers
        if (_config.EnableMentionTrigger)
        {
            if (content.Contains("@ChatGptBot", StringComparison.OrdinalIgnoreCase) ||
                content.Contains("@ChatGPT", StringComparison.OrdinalIgnoreCase) ||
                message.MentionedUsers?.Any(u => u.Id == _client?.CurrentUser?.Id) == true)
            {
                return true;
            }
        }

        // Check for keyword triggers
        if (_config.EnableKeywordTrigger)
        {
            foreach (var keyword in _config.TriggerKeywords)
            {
                if (content.StartsWith(keyword, StringComparison.OrdinalIgnoreCase))
                {
                    return true;
                }
            }
        }

        return false;
    }

    private async Task HandleChatGPTRequestAsync(SocketMessage message)
    {
        var prompt = ExtractPrompt(message.Content, message.MentionedUsers?.FirstOrDefault()?.Id);
        
        if (string.IsNullOrWhiteSpace(prompt))
        {
            await message.Channel.SendMessageAsync(_config.EmptyRequestMessage);
            return;
        }

        _logger.LogInformation($"Processing ChatGPT request from {message.Author.Username}: {prompt.Substring(0, Math.Min(100, prompt.Length))}...");

        // Send typing indicator
        using var typing = message.Channel.EnterTypingState();

        // Add priority tagging if enabled
        if (_config.EnablePriorityTagging)
        {
            prompt = AddPriorityTags(prompt);
        }

        var response = await _openAIService.GetChatCompletionAsync(prompt);

        if (response?.Success == true && !string.IsNullOrWhiteSpace(response.Content))
        {
            var formattedResponse = FormatResponse(response.Content);
            
            // Handle response pagination if needed
            if (_config.EnableResponsePagination && formattedResponse.Length > _config.MaxMessageLength)
            {
                await SendPaginatedResponseAsync(message.Channel, formattedResponse);
            }
            else
            {
                await message.Channel.SendMessageAsync(formattedResponse);
            }
            
            _logger.LogInformation($"ChatGPT response sent to {message.Author.Username}, tokens used: {response.TokensUsed}");
        }
        else
        {
            _logger.LogWarning("OpenAI API call failed: {ErrorMessage}", response?.ErrorMessage);
            await message.Channel.SendMessageAsync(_config.ErrorMessage);
        }
    }

    private string ExtractPrompt(string messageContent, ulong? botUserId = null)
    {
        var content = messageContent.Trim();

        // Remove mention triggers
        if (_config.EnableMentionTrigger)
        {
            content = content.Replace("@ChatGptBot", "", StringComparison.OrdinalIgnoreCase);
            content = content.Replace("@ChatGPT", "", StringComparison.OrdinalIgnoreCase);
            
            // Remove bot user mention if present
            if (botUserId.HasValue)
            {
                content = content.Replace($"<@{botUserId}>", "");
                content = content.Replace($"<@!{botUserId}>", "");
            }
        }

        // Remove keyword triggers
        if (_config.EnableKeywordTrigger)
        {
            foreach (var keyword in _config.TriggerKeywords)
            {
                if (content.StartsWith(keyword, StringComparison.OrdinalIgnoreCase))
                {
                    content = content.Substring(keyword.Length);
                    break;
                }
            }
        }

        return content.Trim();
    }

    private string AddPriorityTags(string prompt)
    {
        // Add priority tags based on content analysis
        var lowerPrompt = prompt.ToLower();
        
        if (lowerPrompt.Contains("urgent") || lowerPrompt.Contains("critical") || lowerPrompt.Contains("emergency"))
        {
            return $"[urgent] {prompt}";
        }
        
        if (lowerPrompt.Contains("code") || lowerPrompt.Contains("implement") || lowerPrompt.Contains("function") || lowerPrompt.Contains("class"))
        {
            return $"[code] {prompt}";
        }
        
        return prompt;
    }

    private string FormatResponse(string response)
    {
        var formatted = $"{_config.ResponsePrefix}{response}";
        
        if (formatted.Length > _config.MaxMessageLength)
        {
            var truncateLength = _config.MaxMessageLength - 50; // Leave room for truncation notice
            formatted = formatted.Substring(0, truncateLength) + "\n\n... (response truncated)";
        }
        
        return formatted;
    }

    private async Task SendPaginatedResponseAsync(IMessageChannel channel, string response)
    {
        const int maxChunkSize = 1900; // Leave room for formatting
        var chunks = new List<string>();
        
        // Split response into chunks
        var remaining = response;
        var chunkIndex = 1;
        
        while (remaining.Length > 0)
        {
            var chunkSize = Math.Min(maxChunkSize, remaining.Length);
            var chunk = remaining.Substring(0, chunkSize);
            
            // Try to break at a natural point (newline or space)
            if (chunkSize < remaining.Length)
            {
                var lastNewline = chunk.LastIndexOf('\n');
                var lastSpace = chunk.LastIndexOf(' ');
                var breakPoint = Math.Max(lastNewline, lastSpace);
                
                if (breakPoint > chunkSize * 0.8) // Only break if it's not too far back
                {
                    chunk = chunk.Substring(0, breakPoint);
                    chunkSize = breakPoint;
                }
            }
            
            chunks.Add($"**Part {chunkIndex}:**\n{chunk}");
            remaining = remaining.Substring(chunkSize).TrimStart();
            chunkIndex++;
        }
        
        // Send chunks with small delays
        foreach (var chunk in chunks)
        {
            await channel.SendMessageAsync(chunk);
            if (chunks.Count > 1)
            {
                await Task.Delay(500); // Small delay between chunks
            }
        }
    }
}
