using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Serilog;
using ZeroDateStrat.Models;
using ZeroDateStrat.Services;

namespace ZeroDateStrat.Tests;

public static class DiscordLiveTest
{
    public static async Task RunDiscordLiveTest()
    {
        Console.WriteLine("🚀 Starting Discord Live Message Test...");
        Console.WriteLine(new string('=', 50));

        try
        {
            // Setup configuration
            var configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json", optional: false)
                .Build();

            // Setup logging
            var logger = new LoggerConfiguration()
                .WriteTo.Console()
                .CreateLogger();

            // Setup services
            var services = new ServiceCollection();
            services.AddLogging(builder => builder.AddSerilog(logger));
            services.AddSingleton<IConfiguration>(configuration);
            services.AddSingleton<INotificationService, NotificationService>();

            var serviceProvider = services.BuildServiceProvider();
            var notificationService = serviceProvider.GetRequiredService<INotificationService>();

            Console.WriteLine("✅ Services initialized successfully");

            // Check environment variable
            var envToken = Environment.GetEnvironmentVariable("DISCORD_BOT_TOKEN");
            if (!string.IsNullOrEmpty(envToken))
            {
                var maskedToken = envToken.Substring(0, Math.Min(10, envToken.Length)) + "...";
                Console.WriteLine($"🔑 Environment variable found: {maskedToken}");
            }
            else
            {
                Console.WriteLine("⚠️ No environment variable found, checking configuration...");
            }

            // Test 1: Send a simple live test message
            Console.WriteLine("\n📨 Test 1: Sending Live Test Alert");
            var liveAlert = new RiskAlert
            {
                Id = $"LIVE_TEST_{DateTime.Now:yyyyMMdd_HHmmss}",
                Type = "LiveDiscordTest",
                Severity = RiskLevel.Medium,
                Message = $"🧪 Live Discord test message sent at {DateTime.Now:yyyy-MM-dd HH:mm:ss} UTC from Zero DTE Trading System",
                Timestamp = DateTime.UtcNow,
                Value = 2500.75m,
                Threshold = 2000.00m,
                CreatedTime = DateTime.UtcNow,
                ExpiryTime = DateTime.UtcNow.AddHours(1)
            };

            var result1 = await notificationService.SendDiscordAlertAsync(liveAlert);
            Console.WriteLine($"Live alert result: {(result1 ? "✅ SUCCESS" : "❌ FAILED")}");

            if (result1)
            {
                Console.WriteLine("   📋 Alert Details:");
                Console.WriteLine($"   - ID: {liveAlert.Id}");
                Console.WriteLine($"   - Type: {liveAlert.Type}");
                Console.WriteLine($"   - Severity: {liveAlert.Severity}");
                Console.WriteLine($"   - Value: ${liveAlert.Value:F2}");
                Console.WriteLine($"   - Threshold: ${liveAlert.Threshold:F2}");
            }

            // Test 2: Send a critical alert to test formatting
            Console.WriteLine("\n🚨 Test 2: Sending Critical Alert Test");
            var criticalAlert = new RiskAlert
            {
                Id = $"CRITICAL_TEST_{DateTime.Now:yyyyMMdd_HHmmss}",
                Type = "CriticalRiskAlert",
                Severity = RiskLevel.Critical,
                Message = "🚨 CRITICAL: This is a test critical alert to verify Discord formatting and color coding",
                Timestamp = DateTime.UtcNow,
                Value = 5000.00m,
                Threshold = 3000.00m,
                CreatedTime = DateTime.UtcNow,
                ExpiryTime = DateTime.UtcNow.AddMinutes(30)
            };

            var result2 = await notificationService.SendDiscordAlertAsync(criticalAlert);
            Console.WriteLine($"Critical alert result: {(result2 ? "✅ SUCCESS" : "❌ FAILED")}");

            // Test 3: Send a low priority alert
            Console.WriteLine("\n🟢 Test 3: Sending Low Priority Alert Test");
            var lowAlert = new RiskAlert
            {
                Id = $"LOW_TEST_{DateTime.Now:yyyyMMdd_HHmmss}",
                Type = "LowRiskAlert",
                Severity = RiskLevel.Low,
                Message = "✅ Low priority test alert - System operating normally",
                Timestamp = DateTime.UtcNow,
                Value = 500.25m,
                Threshold = 1000.00m,
                CreatedTime = DateTime.UtcNow,
                ExpiryTime = DateTime.UtcNow.AddHours(2)
            };

            var result3 = await notificationService.SendDiscordAlertAsync(lowAlert);
            Console.WriteLine($"Low priority alert result: {(result3 ? "✅ SUCCESS" : "❌ FAILED")}");

            // Test 4: Send a trading simulation alert
            Console.WriteLine("\n📈 Test 4: Sending Trading Simulation Alert");
            var tradingAlert = new RiskAlert
            {
                Id = $"TRADING_SIM_{DateTime.Now:yyyyMMdd_HHmmss}",
                Type = "TradingSimulation",
                Severity = RiskLevel.Medium,
                Message = "📊 Simulated trading alert: SPY 0DTE position opened - Testing Discord integration with realistic trading scenario",
                Timestamp = DateTime.UtcNow,
                Value = 1250.50m,
                Threshold = 1500.00m,
                CreatedTime = DateTime.UtcNow,
                ExpiryTime = DateTime.UtcNow.AddMinutes(45)
            };

            var result4 = await notificationService.SendDiscordAlertAsync(tradingAlert);
            Console.WriteLine($"Trading simulation alert result: {(result4 ? "✅ SUCCESS" : "❌ FAILED")}");

            // Summary
            Console.WriteLine("\n" + new string('=', 50));
            Console.WriteLine("📊 Live Test Summary:");
            
            var successCount = new[] { result1, result2, result3, result4 }.Count(r => r);
            var totalTests = 4;
            
            Console.WriteLine($"   ✅ Successful: {successCount}/{totalTests}");
            Console.WriteLine($"   ❌ Failed: {totalTests - successCount}/{totalTests}");
            Console.WriteLine($"   📈 Success Rate: {(successCount * 100.0 / totalTests):F1}%");

            if (successCount == totalTests)
            {
                Console.WriteLine("\n🎉 All Discord tests passed! Check your Discord channel for the messages.");
                Console.WriteLine("💡 You should see 4 different alert messages with different colors and formatting.");
            }
            else if (successCount > 0)
            {
                Console.WriteLine($"\n⚠️ Partial success: {successCount} out of {totalTests} tests passed.");
                Console.WriteLine("💡 Check your Discord channel for any messages that were sent.");
            }
            else
            {
                Console.WriteLine("\n❌ All tests failed. Please check:");
                Console.WriteLine("   • Discord bot token is set correctly");
                Console.WriteLine("   • Channel ID is correct");
                Console.WriteLine("   • Bot has permissions in the channel");
                Console.WriteLine("   • Bot is invited to your Discord server");
            }

            Console.WriteLine("\n🔧 Troubleshooting:");
            Console.WriteLine("   • Verify bot token: Use a new terminal session");
            Console.WriteLine("   • Check bot permissions: Send Messages, Embed Links");
            Console.WriteLine("   • Verify channel ID: Right-click channel → Copy ID");
            Console.WriteLine("   • Test bot invite: Make sure bot is in your server");

        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Discord Live Test failed with exception: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
    }
}
