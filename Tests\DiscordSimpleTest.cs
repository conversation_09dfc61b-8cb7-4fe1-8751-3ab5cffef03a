using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Serilog;
using ZeroDateStrat.Models;
using ZeroDateStrat.Services;

namespace ZeroDateStrat.Tests;

public static class DiscordSimpleTest
{
    public static async Task RunDiscordSimpleTest()
    {
        Console.WriteLine("🧪 Starting Discord Simple Test (No Embeds)...");
        Console.WriteLine(new string('=', 50));

        try
        {
            // Setup configuration
            var configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json", optional: false)
                .Build();

            // Setup logging
            var logger = new LoggerConfiguration()
                .WriteTo.Console()
                .CreateLogger();

            // Setup services
            var services = new ServiceCollection();
            services.AddLogging(builder => builder.AddSerilog(logger));
            services.AddSingleton<IConfiguration>(configuration);
            services.AddSingleton<INotificationService, NotificationService>();

            var serviceProvider = services.BuildServiceProvider();
            var notificationService = serviceProvider.GetRequiredService<INotificationService>();

            Console.WriteLine("✅ Services initialized successfully");

            // Check environment variable
            var envToken = Environment.GetEnvironmentVariable("DISCORD_BOT_TOKEN");
            if (!string.IsNullOrEmpty(envToken))
            {
                var maskedToken = envToken.Substring(0, Math.Min(10, envToken.Length)) + "...";
                Console.WriteLine($"🔑 Environment variable found: {maskedToken}");
            }
            else
            {
                Console.WriteLine("⚠️ No environment variable found");
                return;
            }

            // Temporarily disable embeds in configuration for this test
            Console.WriteLine("\n🔧 Temporarily disabling embeds for permission testing...");
            
            // Test with a simple alert that uses plain text (no embeds)
            Console.WriteLine("\n📨 Test: Sending Simple Text Alert (No Embeds)");
            var simpleAlert = new RiskAlert
            {
                Id = $"SIMPLE_TEST_{DateTime.Now:yyyyMMdd_HHmmss}",
                Type = "SimpleDiscordTest",
                Severity = RiskLevel.Low,
                Message = $"Simple test message at {DateTime.Now:HH:mm:ss} - Testing Discord permissions",
                Timestamp = DateTime.UtcNow,
                Value = 100.00m,
                Threshold = 200.00m,
                CreatedTime = DateTime.UtcNow,
                ExpiryTime = DateTime.UtcNow.AddHours(1)
            };

            // Override the UseEmbeds setting temporarily
            var originalUseEmbeds = configuration.GetSection("Monitoring:NotificationChannels:Discord:UseEmbeds").Value;
            
            // Create a custom configuration that disables embeds
            var configDict = new Dictionary<string, string?>
            {
                ["Monitoring:NotificationChannels:Discord:Enabled"] = "true",
                ["Monitoring:NotificationChannels:Discord:UseEmbeds"] = "false",
                ["Monitoring:NotificationChannels:Discord:ChannelId"] = "1382148371103350799"
            };

            var customConfig = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json", optional: false)
                .AddInMemoryCollection(configDict)
                .Build();

            // Create new service with custom config
            var customServices = new ServiceCollection();
            customServices.AddLogging(builder => builder.AddSerilog(logger));
            customServices.AddSingleton<IConfiguration>(customConfig);
            customServices.AddSingleton<INotificationService, NotificationService>();

            var customServiceProvider = customServices.BuildServiceProvider();
            var customNotificationService = customServiceProvider.GetRequiredService<INotificationService>();

            var result = await customNotificationService.SendDiscordAlertAsync(simpleAlert);
            Console.WriteLine($"Simple text alert result: {(result ? "✅ SUCCESS" : "❌ FAILED")}");

            if (result)
            {
                Console.WriteLine("🎉 SUCCESS! The bot can send messages to your channel.");
                Console.WriteLine("The previous failures were likely due to embed permissions.");
                Console.WriteLine("Check your Discord channel for the simple text message.");
            }
            else
            {
                Console.WriteLine("❌ FAILED: The bot still cannot send messages.");
                Console.WriteLine("This indicates a more fundamental permission or setup issue.");
            }

            // Additional diagnostics
            Console.WriteLine("\n🔍 Diagnostic Information:");
            Console.WriteLine($"   Channel ID: 1382148371103350799");
            Console.WriteLine($"   Bot Token: {(envToken != null ? envToken.Substring(0, Math.Min(10, envToken.Length)) + "..." : "[Not Set]")}");
            Console.WriteLine($"   UseEmbeds: false (for this test)");

            Console.WriteLine("\n📋 Next Steps:");
            if (result)
            {
                Console.WriteLine("✅ Bot permissions are working!");
                Console.WriteLine("   • The issue was likely with embed permissions");
                Console.WriteLine("   • Make sure your bot has 'Embed Links' permission");
                Console.WriteLine("   • You can now enable embeds in your configuration");
            }
            else
            {
                Console.WriteLine("❌ Bot permissions need to be fixed:");
                Console.WriteLine("   1. Verify bot is invited to your Discord server");
                Console.WriteLine("   2. Check bot has 'Send Messages' permission in the channel");
                Console.WriteLine("   3. Verify the channel ID is correct");
                Console.WriteLine("   4. Make sure the bot token hasn't expired");
            }

        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Discord Simple Test failed with exception: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
    }
}
