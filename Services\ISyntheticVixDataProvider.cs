using ZeroDateStrat.Models;

namespace ZeroDateStrat.Services;

/// <summary>
/// Data provider interface to break circular dependency between SyntheticVixService and SyntheticVixAnalyticsService
/// This interface provides read-only access to synthetic VIX data without creating circular dependencies
/// </summary>
public interface ISyntheticVixDataProvider
{
    /// <summary>
    /// Get the current synthetic VIX value
    /// </summary>
    Task<decimal> GetCurrentValueAsync();
    
    /// <summary>
    /// Get synthetic VIX change over a specified period
    /// </summary>
    Task<decimal> GetChangeAsync(TimeSpan period);
    
    /// <summary>
    /// Get historical synthetic VIX data
    /// </summary>
    Task<List<SyntheticVixHistoricalData>> GetHistoricalDataAsync(int days);
    
    /// <summary>
    /// Test if the data provider is available
    /// </summary>
    Task<bool> TestConnectionAsync();
    
    /// <summary>
    /// Get component breakdown for the current calculation
    /// </summary>
    Task<List<SyntheticVixComponent>> GetCurrentComponentBreakdownAsync();
}
