using System.Text.Json.Serialization;

namespace ZeroDateStrat.Models;

/// <summary>
/// Represents a Treasury rate data point
/// </summary>
public class TreasuryRate
{
    public string Maturity { get; set; } = string.Empty;
    public decimal Rate { get; set; }
    public DateTime Date { get; set; }
    public string Source { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Represents the complete yield curve at a point in time
/// </summary>
public class YieldCurve
{
    public DateTime Date { get; set; }
    public Dictionary<string, decimal> Rates { get; set; } = new();
    public string Source { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// Interpolates a rate for a given time to maturity
    /// </summary>
    public decimal InterpolateRate(decimal timeToMaturityYears)
    {
        if (Rates.Count == 0) return 0.0525m; // Default 5.25%
        
        // Convert maturity strings to years for interpolation
        var maturities = new Dictionary<decimal, decimal>();
        
        foreach (var kvp in Rates)
        {
            var years = ConvertMaturityToYears(kvp.Key);
            if (years > 0) maturities[years] = kvp.Value;
        }
        
        if (maturities.Count == 0) return 0.0525m;
        
        // Find the closest maturities
        var sortedMaturities = maturities.Keys.OrderBy(x => x).ToList();
        
        // If exact match
        if (maturities.ContainsKey(timeToMaturityYears))
            return maturities[timeToMaturityYears];
        
        // If shorter than shortest maturity, use shortest
        if (timeToMaturityYears <= sortedMaturities.First())
            return maturities[sortedMaturities.First()];
        
        // If longer than longest maturity, use longest
        if (timeToMaturityYears >= sortedMaturities.Last())
            return maturities[sortedMaturities.Last()];
        
        // Linear interpolation between two closest points
        var lowerMaturity = sortedMaturities.Where(x => x <= timeToMaturityYears).Max();
        var upperMaturity = sortedMaturities.Where(x => x >= timeToMaturityYears).Min();
        
        var lowerRate = maturities[lowerMaturity];
        var upperRate = maturities[upperMaturity];
        
        var weight = (timeToMaturityYears - lowerMaturity) / (upperMaturity - lowerMaturity);
        return lowerRate + weight * (upperRate - lowerRate);
    }
    
    private decimal ConvertMaturityToYears(string maturity)
    {
        return maturity.ToUpper() switch
        {
            "1MONTH" or "DGS1MO" => 1m / 12m,
            "3MONTH" or "DGS3MO" => 3m / 12m,
            "6MONTH" or "DGS6MO" => 6m / 12m,
            "1YEAR" or "DGS1" => 1m,
            "2YEAR" or "DGS2" => 2m,
            "3YEAR" or "DGS3" => 3m,
            "5YEAR" or "DGS5" => 5m,
            "7YEAR" or "DGS7" => 7m,
            "10YEAR" or "DGS10" => 10m,
            "20YEAR" or "DGS20" => 20m,
            "30YEAR" or "DGS30" => 30m,
            _ => 0m
        };
    }
}

/// <summary>
/// FRED API response models
/// </summary>
public class FredApiResponse<T>
{
    [JsonPropertyName("realtime_start")]
    public string RealtimeStart { get; set; } = string.Empty;
    
    [JsonPropertyName("realtime_end")]
    public string RealtimeEnd { get; set; } = string.Empty;
    
    [JsonPropertyName("observation_start")]
    public string ObservationStart { get; set; } = string.Empty;
    
    [JsonPropertyName("observation_end")]
    public string ObservationEnd { get; set; } = string.Empty;
    
    [JsonPropertyName("units")]
    public string Units { get; set; } = string.Empty;
    
    [JsonPropertyName("output_type")]
    public int OutputType { get; set; }
    
    [JsonPropertyName("file_type")]
    public string FileType { get; set; } = string.Empty;
    
    [JsonPropertyName("order_by")]
    public string OrderBy { get; set; } = string.Empty;
    
    [JsonPropertyName("sort_order")]
    public string SortOrder { get; set; } = string.Empty;
    
    [JsonPropertyName("count")]
    public int Count { get; set; }
    
    [JsonPropertyName("offset")]
    public int Offset { get; set; }
    
    [JsonPropertyName("limit")]
    public int Limit { get; set; }
    
    [JsonPropertyName("observations")]
    public List<T> Observations { get; set; } = new();
}

public class FredObservation
{
    [JsonPropertyName("realtime_start")]
    public string RealtimeStart { get; set; } = string.Empty;
    
    [JsonPropertyName("realtime_end")]
    public string RealtimeEnd { get; set; } = string.Empty;
    
    [JsonPropertyName("date")]
    public string Date { get; set; } = string.Empty;
    
    [JsonPropertyName("value")]
    public string Value { get; set; } = string.Empty;
}

/// <summary>
/// Treasury rate service status and health information
/// </summary>
public class TreasuryRateServiceStatus
{
    public bool IsHealthy { get; set; }
    public DateTime LastUpdate { get; set; }
    public string PrimarySource { get; set; } = string.Empty;
    public string SecondarySource { get; set; } = string.Empty;
    public int CachedRatesCount { get; set; }
    public List<string> Issues { get; set; } = new();
    public Dictionary<string, decimal> CurrentRates { get; set; } = new();
    public TimeSpan CacheAge { get; set; }
}

/// <summary>
/// Historical Treasury rate data point
/// </summary>
public class HistoricalTreasuryRate
{
    public DateTime Date { get; set; }
    public string Maturity { get; set; } = string.Empty;
    public decimal Rate { get; set; }
    public string Source { get; set; } = string.Empty;
}

/// <summary>
/// Treasury rate change information
/// </summary>
public class TreasuryRateChange
{
    public string Maturity { get; set; } = string.Empty;
    public decimal CurrentRate { get; set; }
    public decimal PreviousRate { get; set; }
    public decimal Change { get; set; }
    public decimal ChangePercent { get; set; }
    public TimeSpan Period { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}
