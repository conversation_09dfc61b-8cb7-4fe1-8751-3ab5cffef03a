# Discord Error Messaging Implementation

## Overview

This document describes the implementation of Discord error messaging functionality for the ZeroDateStrat trading system. When trading errors occur, the system will automatically send detailed error notifications to the configured Discord channel.

## Features Implemented

### 1. Discord Error Notification Service
- **New Method**: `SendTradingErrorAsync()` in `IDiscordService`
- **Functionality**: Sends formatted error messages to <PERSON>rd with severity levels and context
- **Supports**: Both embed and plain text message formats

### 2. Enhanced Global Exception Handler
- **New Method**: `HandleTradingExceptionAsync()` in `IGlobalExceptionHandler`
- **Smart Context Detection**: Automatically detects trading-related errors based on context keywords
- **Discord Integration**: Sends Discord notifications for trading errors only

### 3. Trading Error Context Detection
The system automatically identifies trading-related errors by checking for these keywords in the error context:
- trading, order, signal, position, execution, alpaca
- strategy, portfolio, risk, market, option, trade
- buy, sell, close, open, place, cancel, fill

### 4. Error Severity Levels
- **Low**: Network timeouts, recoverable connection issues
- **Medium**: General trading errors, signal generation failures
- **High**: Authentication failures, critical system errors
- **Critical**: System-wide failures

## Implementation Details

### Files Modified

1. **Services/DiscordService.cs**
   - Added `SendTradingErrorAsync()` method to interface and implementation
   - Added `BuildTradingErrorEmbed()` and `BuildTradingErrorMessage()` helper methods
   - Supports both embed and plain text formats

2. **Services/GlobalExceptionHandler.cs**
   - Added Discord service dependency injection
   - Added `HandleTradingExceptionAsync()` method
   - Added `IsTradingRelatedContext()` helper method for smart context detection

3. **Strategies/ZeroDteStrategy.cs**
   - Updated constructor to accept `IGlobalExceptionHandler`
   - Modified exception handling in `ExecuteSignalAsync()`, `GenerateSignalsAsync()`, and `ManagePositionsAsync()`
   - Now uses `HandleTradingExceptionAsync()` for trading-specific error handling

4. **Services/AlpacaService.cs**
   - Updated `PlaceOrderAsync()` to use trading exception handler
   - Provides detailed context for order placement errors

5. **Program.cs**
   - Updated service registration to inject Discord service into GlobalExceptionHandler
   - Added command-line option for testing Discord error notifications

### Error Message Format

#### Discord Embed Format
- **Title**: 🚨 Trading System Error
- **Description**: Context of where the error occurred
- **Fields**:
  - 🔍 Error Type: Exception type name
  - ⚠️ Severity: Error severity level
  - 📍 Context: Detailed context information
  - 📝 Error Message: Full error message (truncated if too long)
  - 🔗 Inner Exception: Inner exception details (if present)
  - 🕒 Occurred At: UTC timestamp
  - 🖥️ System: System identifier

#### Plain Text Format
- Severity emoji (🟡🔴🚨💀) based on severity level
- Context and error type information
- Formatted error details in code blocks
- Inner exception information
- Timestamp and system information

## Usage Examples

### 1. Automatic Error Handling
The system automatically handles trading errors when they occur:

```csharp
// In trading operations, errors are automatically caught and sent to Discord
try
{
    var order = await _alpacaService.PlaceOrderAsync(signal);
}
catch (Exception ex)
{
    // This automatically sends Discord notification for trading errors
    await _exceptionHandler.HandleTradingExceptionAsync(ex, $"Order Placement - {signal.Strategy} for {signal.UnderlyingSymbol}");
}
```

### 2. Manual Error Notification
You can also manually send error notifications:

```csharp
var exception = new TimeoutException("Order placement timeout");
await _discordService.SendTradingErrorAsync(exception, "Manual Error Test", "High");
```

### 3. Testing the Implementation
Run the Discord error notification test:

```bash
dotnet run discord-error-test
```

This will:
- Connect to Discord
- Send various test error notifications
- Verify different severity levels
- Test both trading and non-trading contexts
- Display exception statistics

## Configuration

### Discord Configuration
Ensure your `appsettings.json` has Discord properly configured:

```json
{
  "Monitoring": {
    "NotificationChannels": {
      "Discord": {
        "Enabled": true,
        "EnableSlashCommands": true,
        "ChannelId": "your-channel-id",
        "UseEmbeds": true
      }
    }
  }
}
```

### Environment Variables
Set the Discord bot token as an environment variable:
```bash
DISCORD_BOT_TOKEN=your-bot-token-here
```

## Error Scenarios Covered

### 1. Signal Generation Errors
- Market data retrieval failures
- Options scanning errors
- Signal validation failures

### 2. Order Placement Errors
- Alpaca API authentication failures
- Network timeouts during order placement
- Invalid order parameters
- Insufficient buying power

### 3. Position Management Errors
- Position update failures
- Exit decision calculation errors
- Position closing failures

### 4. System-Level Errors
- Configuration validation errors
- Service initialization failures
- Database connection issues

## Benefits

1. **Real-Time Alerts**: Immediate notification of trading system issues
2. **Detailed Context**: Rich error information including context, severity, and timestamps
3. **Smart Filtering**: Only trading-related errors trigger Discord notifications
4. **Multiple Formats**: Support for both rich embeds and plain text messages
5. **Error Tracking**: Integration with existing exception statistics and logging
6. **Easy Testing**: Built-in test functionality to verify the implementation

## Future Enhancements

1. **Error Categorization**: Group similar errors and provide summary notifications
2. **Rate Limiting**: Prevent spam by limiting notification frequency for similar errors
3. **Error Resolution**: Add buttons or commands to acknowledge or resolve errors
4. **Performance Metrics**: Include system performance data in error notifications
5. **Multi-Channel Support**: Route different error types to different Discord channels

## Troubleshooting

### Discord Not Connected
- Verify bot token is correctly set
- Check Discord channel permissions
- Ensure bot has necessary permissions in the server

### No Error Notifications
- Verify error context contains trading-related keywords
- Check Discord service is running and connected
- Review logs for Discord service errors

### Test Failures
- Ensure Discord bot token is valid
- Verify channel ID is correct
- Check network connectivity to Discord
