using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using Moq.Protected;
using System.Net;
using System.Text;
using System.Text.Json;
using Xunit;
using ZeroDateStrat.Models;
using ZeroDateStrat.Services;

namespace ZeroDateStrat.Tests;

public class TreasuryRateServiceTests
{
    private readonly Mock<ILogger<TreasuryRateService>> _mockLogger;
    private readonly Mock<IConfiguration> _mockConfiguration;
    private readonly Mock<HttpMessageHandler> _mockHttpMessageHandler;
    private readonly HttpClient _httpClient;
    private readonly TreasuryRateService _treasuryRateService;

    public TreasuryRateServiceTests()
    {
        _mockLogger = new Mock<ILogger<TreasuryRateService>>();
        _mockConfiguration = new Mock<IConfiguration>();
        _mockHttpMessageHandler = new Mock<HttpMessageHandler>();
        _httpClient = new HttpClient(_mockHttpMessageHandler.Object);

        // Setup configuration
        SetupConfiguration();

        _treasuryRateService = new TreasuryRateService(
            _mockLogger.Object,
            _mockConfiguration.Object,
            _httpClient);
    }

    private void SetupConfiguration()
    {
        _mockConfiguration.Setup(c => c["FRED:ApiKey"]).Returns("test_api_key");
        _mockConfiguration.Setup(c => c["FRED:BaseUrl"]).Returns("https://api.stlouisfed.org/fred");
        _mockConfiguration.Setup(c => c.GetValue<int>("FRED:CacheExpiryMinutes", 60)).Returns(60);
        _mockConfiguration.Setup(c => c.GetValue<decimal>("FRED:DefaultRiskFreeRate", 0.0525m)).Returns(0.0525m);

        // Setup Treasury rate series
        var treasurySection = new Mock<IConfigurationSection>();
        var treasuryChildren = new List<IConfigurationSection>
        {
            CreateConfigSection("3Month", "DGS3MO"),
            CreateConfigSection("1Year", "DGS1"),
            CreateConfigSection("10Year", "DGS10")
        };
        treasurySection.Setup(s => s.GetChildren()).Returns(treasuryChildren);
        _mockConfiguration.Setup(c => c.GetSection("FRED:TreasuryRateSeries")).Returns(treasurySection.Object);

        // Setup fallback rates
        var fallbackSection = new Mock<IConfigurationSection>();
        var fallbackChildren = new List<IConfigurationSection>
        {
            CreateConfigSection("3Month", "0.0525"),
            CreateConfigSection("1Year", "0.0535"),
            CreateConfigSection("10Year", "0.0550")
        };
        fallbackSection.Setup(s => s.GetChildren()).Returns(fallbackChildren);
        _mockConfiguration.Setup(c => c.GetSection("FRED:FallbackRates")).Returns(fallbackSection.Object);
    }

    private IConfigurationSection CreateConfigSection(string key, string value)
    {
        var section = new Mock<IConfigurationSection>();
        section.Setup(s => s.Key).Returns(key);
        section.Setup(s => s.Value).Returns(value);
        return section.Object;
    }

    [Fact]
    public async Task GetRiskFreeRateAsync_WithValidTimeToMaturity_ReturnsInterpolatedRate()
    {
        // Arrange
        var timeToMaturity = 0.25m; // 3 months
        SetupSuccessfulFredResponse();

        // Act
        var result = await _treasuryRateService.GetRiskFreeRateAsync(timeToMaturity);

        // Assert
        Assert.True(result > 0);
        Assert.True(result < 1); // Should be in decimal format, not percentage
    }

    [Fact]
    public async Task GetCurrentYieldCurveAsync_WithSuccessfulFredResponse_ReturnsYieldCurve()
    {
        // Arrange
        SetupSuccessfulFredResponse();

        // Act
        var result = await _treasuryRateService.GetCurrentYieldCurveAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal("FRED", result.Source);
        Assert.True(result.Rates.Count > 0);
        Assert.Equal(DateTime.UtcNow.Date, result.Date);
    }

    [Fact]
    public async Task GetCurrentYieldCurveAsync_WithFailedFredResponse_ReturnsFallbackRates()
    {
        // Arrange
        SetupFailedFredResponse();

        // Act
        var result = await _treasuryRateService.GetCurrentYieldCurveAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Fallback", result.Source);
        Assert.True(result.Rates.Count > 0);
        Assert.Contains("3Month", result.Rates.Keys);
    }

    [Fact]
    public async Task GetTreasuryRateAsync_WithValidMaturity_ReturnsTreasuryRate()
    {
        // Arrange
        SetupSuccessfulFredResponse();

        // Act
        var result = await _treasuryRateService.GetTreasuryRateAsync("3Month");

        // Assert
        Assert.NotNull(result);
        Assert.Equal("DGS3MO", result.Maturity);
        Assert.True(result.Rate > 0);
        Assert.Equal("FRED", result.Source);
    }

    [Fact]
    public async Task GetTreasuryRateAsync_WithInvalidMaturity_ReturnsFallbackRate()
    {
        // Act
        var result = await _treasuryRateService.GetTreasuryRateAsync("InvalidMaturity");

        // Assert
        Assert.NotNull(result);
        Assert.Equal("InvalidMaturity", result.Maturity);
        Assert.Equal(0.0525m, result.Rate); // Default fallback rate
        Assert.Equal("Fallback", result.Source);
    }

    [Fact]
    public async Task TestConnectionAsync_WithValidApiKey_ReturnsTrue()
    {
        // Arrange
        SetupSuccessfulConnectionTest();

        // Act
        var result = await _treasuryRateService.TestConnectionAsync();

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task TestConnectionAsync_WithInvalidApiKey_ReturnsFalse()
    {
        // Arrange
        _mockConfiguration.Setup(c => c["FRED:ApiKey"]).Returns("YOUR_FRED_API_KEY_HERE");

        // Act
        var result = await _treasuryRateService.TestConnectionAsync();

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task GetServiceStatusAsync_ReturnsValidStatus()
    {
        // Arrange
        SetupSuccessfulFredResponse();
        await _treasuryRateService.GetCurrentYieldCurveAsync(); // Populate cache

        // Act
        var result = await _treasuryRateService.GetServiceStatusAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal("FRED", result.PrimarySource);
        Assert.Equal("Fallback", result.SecondarySource);
        Assert.True(result.CachedRatesCount >= 0);
    }

    [Fact]
    public void YieldCurve_InterpolateRate_ReturnsCorrectInterpolation()
    {
        // Arrange
        var yieldCurve = new YieldCurve
        {
            Rates = new Dictionary<string, decimal>
            {
                { "3Month", 0.05m },
                { "1Year", 0.055m },
                { "10Year", 0.06m }
            }
        };

        // Act - Test interpolation between 3 months and 1 year
        var result = yieldCurve.InterpolateRate(0.5m); // 6 months

        // Assert
        Assert.True(result > 0.05m && result < 0.055m);
    }

    [Fact]
    public void YieldCurve_InterpolateRate_WithExactMatch_ReturnsExactRate()
    {
        // Arrange
        var yieldCurve = new YieldCurve
        {
            Rates = new Dictionary<string, decimal>
            {
                { "1Year", 0.055m }
            }
        };

        // Act
        var result = yieldCurve.InterpolateRate(1.0m);

        // Assert
        Assert.Equal(0.055m, result);
    }

    private void SetupSuccessfulFredResponse()
    {
        var fredResponse = new FredApiResponse<FredObservation>
        {
            Observations = new List<FredObservation>
            {
                new FredObservation
                {
                    Date = DateTime.UtcNow.ToString("yyyy-MM-dd"),
                    Value = "5.25"
                }
            }
        };

        var jsonResponse = JsonSerializer.Serialize(fredResponse);
        var httpResponse = new HttpResponseMessage(HttpStatusCode.OK)
        {
            Content = new StringContent(jsonResponse, Encoding.UTF8, "application/json")
        };

        _mockHttpMessageHandler
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(httpResponse);
    }

    private void SetupFailedFredResponse()
    {
        var httpResponse = new HttpResponseMessage(HttpStatusCode.BadRequest);

        _mockHttpMessageHandler
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(httpResponse);
    }

    private void SetupSuccessfulConnectionTest()
    {
        var httpResponse = new HttpResponseMessage(HttpStatusCode.OK)
        {
            Content = new StringContent("{}", Encoding.UTF8, "application/json")
        };

        _mockHttpMessageHandler
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.Is<HttpRequestMessage>(req => req.RequestUri!.ToString().Contains("series?series_id=DGS3MO")),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(httpResponse);
    }
}
