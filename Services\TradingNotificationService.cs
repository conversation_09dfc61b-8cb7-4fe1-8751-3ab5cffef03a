using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using ZeroDateStrat.Models;
using Alpaca.Markets;

namespace ZeroDateStrat.Services;

public interface ITradingNotificationService
{
    Task SendMorningReportAsync();
    Task SendTradeExecutionNotificationAsync(TradingSignal signal, IOrder? order, bool success);
    Task SendEndOfDayReportAsync();
    Task SendPositionUpdateAsync(Models.ManagedPosition position, string updateType);
    Task SendMarketStatusUpdateAsync(string status, decimal vix, string regime);
    Task SendSystemStartupNotificationAsync();
    Task SendSystemShutdownNotificationAsync();
}

public class TradingNotificationService : ITradingNotificationService
{
    private readonly IDiscordService _discordService;
    private readonly IAlpacaService _alpacaService;
    private readonly IPositionManager _positionManager;
    private readonly IAdvancedRiskManager _riskManager;
    private readonly ILogger<TradingNotificationService> _logger;
    private readonly IConfiguration _configuration;

    public TradingNotificationService(
        IDiscordService discordService,
        IAlpacaService alpacaService,
        IPositionManager positionManager,
        IAdvancedRiskManager riskManager,
        ILogger<TradingNotificationService> logger,
        IConfiguration configuration)
    {
        _discordService = discordService;
        _alpacaService = alpacaService;
        _positionManager = positionManager;
        _riskManager = riskManager;
        _logger = logger;
        _configuration = configuration;
    }

    public async Task SendMorningReportAsync()
    {
        try
        {
            _logger.LogInformation("Generating morning portfolio report");

            var account = await _alpacaService.GetAccountAsync();
            if (account == null)
            {
                _logger.LogWarning("Unable to retrieve account information for morning report");
                return;
            }

            var portfolio = await _positionManager.GetPortfolioSnapshotAsync();
            
            await _discordService.SendMorningPortfolioSummaryAsync(portfolio, account);
            
            _logger.LogInformation("Morning portfolio report sent successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send morning portfolio report");
        }
    }

    public async Task SendTradeExecutionNotificationAsync(TradingSignal signal, IOrder? order, bool success)
    {
        try
        {
            _logger.LogInformation($"Sending trade execution notification: {signal.Strategy} - {(success ? "Success" : "Failed")}");
            
            await _discordService.SendTradeNotificationAsync(signal, order, success);
            
            _logger.LogInformation("Trade execution notification sent successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send trade execution notification");
        }
    }

    public async Task SendEndOfDayReportAsync()
    {
        try
        {
            _logger.LogInformation("Generating end of day trading report");

            var report = await GenerateDailyTradingReportAsync();
            
            await _discordService.SendEndOfDayReportAsync(report);
            
            _logger.LogInformation("End of day report sent successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send end of day report");
        }
    }

    public async Task SendPositionUpdateAsync(Models.ManagedPosition position, string updateType)
    {
        try
        {
            _logger.LogInformation($"Sending position update notification: {position.UnderlyingSymbol} - {updateType}");
            
            await _discordService.SendPositionUpdateAsync(position, updateType);
            
            _logger.LogInformation("Position update notification sent successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send position update notification");
        }
    }

    public async Task SendMarketStatusUpdateAsync(string status, decimal vix, string regime)
    {
        try
        {
            _logger.LogInformation($"Sending market status update: {status} - VIX: {vix:F2}");
            
            await _discordService.SendMarketStatusUpdateAsync(status, vix, regime);
            
            _logger.LogInformation("Market status update sent successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send market status update");
        }
    }

    public async Task SendSystemStartupNotificationAsync()
    {
        try
        {
            var message = "🚀 **Zero DTE Trading System Started**\n\n" +
                         $"**Time:** {DateTime.Now:yyyy-MM-dd HH:mm:ss ET}\n" +
                         $"**Environment:** {(_configuration.GetValue<string>("Alpaca:BaseUrl")?.Contains("paper") == true ? "Paper Trading" : "Live Trading")}\n" +
                         $"**Status:** System initialized and ready for trading\n\n" +
                         "📊 Monitoring market conditions and scanning for opportunities...";

            await _discordService.SendMessageAsync(message);
            
            _logger.LogInformation("System startup notification sent successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send system startup notification");
        }
    }

    public async Task SendSystemShutdownNotificationAsync()
    {
        try
        {
            var message = "🛑 **Zero DTE Trading System Shutdown**\n\n" +
                         $"**Time:** {DateTime.Now:yyyy-MM-dd HH:mm:ss ET}\n" +
                         $"**Status:** System shutdown initiated\n\n" +
                         "💤 Trading operations suspended until next startup.";

            await _discordService.SendMessageAsync(message);
            
            _logger.LogInformation("System shutdown notification sent successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send system shutdown notification");
        }
    }

    private async Task<DailyTradingReport> GenerateDailyTradingReportAsync()
    {
        var report = new DailyTradingReport
        {
            Date = DateTime.Today
        };

        try
        {
            // Get account information
            var account = await _alpacaService.GetAccountAsync();
            if (account != null)
            {
                report.EndingAccountValue = (decimal)account.Equity;
                report.DayPnL = (decimal)(account.Equity - account.LastEquity);
                report.AccountValueChange = report.DayPnL;
            }

            // Get portfolio snapshot
            var portfolio = await _positionManager.GetPortfolioSnapshotAsync();
            
            // Get risk metrics
            var riskMetrics = await _riskManager.GetRealTimeRiskMetricsAsync();
            if (riskMetrics != null)
            {
                report.MaxDrawdown = riskMetrics.MaxDrawdown;
                report.TotalRiskTaken = riskMetrics.LargestPositionSize; // Use largest position as proxy for risk taken
                report.RiskUtilization = riskMetrics.PortfolioValue > 0 ?
                    riskMetrics.LargestPositionSize / riskMetrics.PortfolioValue : 0; // Calculate utilization
            }

            // Calculate trading statistics (simplified for now)
            var activePositions = await _positionManager.GetActivePositionsAsync();
            report.TotalTrades = activePositions.Count;
            
            if (activePositions.Any())
            {
                var winningTrades = activePositions.Count(p => p.UnrealizedPnL > 0);
                report.WinRate = (decimal)winningTrades / activePositions.Count;
                
                // Strategy performance breakdown
                var strategyGroups = activePositions.GroupBy(p => p.Strategy);
                foreach (var group in strategyGroups)
                {
                    var strategyPnL = group.Sum(p => p.UnrealizedPnL);
                    report.StrategyPerformance[group.Key] = strategyPnL;
                }
            }

            // Add market conditions (placeholder)
            report.MarketConditions = "Normal trading conditions";
            report.VixLevel = 25.0m; // This should come from actual VIX data
            report.MarketRegime = "Moderate Volatility";

            return report;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating daily trading report");
            return report; // Return partial report
        }
    }
}
