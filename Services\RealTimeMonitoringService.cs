using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using ZeroDateStrat.Models;
using System.Text.Json;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace ZeroDateStrat.Services;

// Phase 3: Real-time Monitoring & Alerting Service
public interface IRealTimeMonitoringService
{
    Task<LiveDashboardData> GetLiveDashboardDataAsync();
    Task StartMonitoringAsync();
    Task StopMonitoringAsync();
    Task<bool> AddAlertConfigurationAsync(AlertConfiguration config);
    Task<bool> RemoveAlertConfigurationAsync(string alertType);
    Task<List<AlertConfiguration>> GetAlertConfigurationsAsync();
    Task<List<RiskAlert>> GetActiveAlertsAsync();
    Task<bool> AcknowledgeAlertAsync(string alertId);
    Task<SystemMetrics> GetSystemMetricsAsync();
    Task<List<HealthCheckResult>> GetHealthCheckResultsAsync();
    event EventHandler<LiveDashboardData>? DashboardDataUpdated;
    event EventHandler<RiskAlert>? AlertTriggered;
}

public class RealTimeMonitoringService : IRealTimeMonitoringService
{
    private readonly ILogger<RealTimeMonitoringService> _logger;
    private readonly IConfiguration _configuration;
    private readonly IAlpacaService _alpacaService;
    private readonly IAdvancedRiskManager _riskManager;
    private readonly IPositionManager _positionManager;
    private readonly IPerformanceAnalytics _performanceAnalytics;
    private readonly IServiceProvider? _serviceProvider;
    
    private readonly ConcurrentDictionary<string, AlertConfiguration> _alertConfigurations = new();
    private readonly ConcurrentDictionary<string, RiskAlert> _activeAlerts = new();
    private readonly ConcurrentQueue<SystemMetrics> _systemMetricsHistory = new();
    
    private Timer? _monitoringTimer;
    private Timer? _alertCheckTimer;
    private Timer? _healthCheckTimer;
    private bool _isMonitoring = false;
    
    public event EventHandler<LiveDashboardData>? DashboardDataUpdated;
    public event EventHandler<RiskAlert>? AlertTriggered;

    public RealTimeMonitoringService(
        ILogger<RealTimeMonitoringService> logger,
        IConfiguration configuration,
        IAlpacaService alpacaService,
        IAdvancedRiskManager riskManager,
        IPositionManager positionManager,
        IPerformanceAnalytics performanceAnalytics,
        IServiceProvider? serviceProvider = null)
    {
        _logger = logger;
        _configuration = configuration;
        _alpacaService = alpacaService;
        _riskManager = riskManager;
        _positionManager = positionManager;
        _performanceAnalytics = performanceAnalytics;
        _serviceProvider = serviceProvider;
        
        InitializeDefaultAlerts();
    }

    private void InitializeDefaultAlerts()
    {
        try
        {
            // Default alert configurations
            var defaultAlerts = new List<AlertConfiguration>
            {
                new AlertConfiguration
                {
                    AlertType = "DailyLossLimit",
                    Threshold = _configuration.GetValue<decimal>("Trading:MaxDailyLoss", 500),
                    IsEnabled = true,
                    NotificationChannels = new List<string> { "Console", "Email" },
                    CooldownPeriod = TimeSpan.FromMinutes(30)
                },
                new AlertConfiguration
                {
                    AlertType = "PortfolioDrawdown",
                    Threshold = 0.05m, // 5% drawdown
                    IsEnabled = true,
                    NotificationChannels = new List<string> { "Console", "Email" },
                    CooldownPeriod = TimeSpan.FromMinutes(15)
                },
                new AlertConfiguration
                {
                    AlertType = "HighRiskPosition",
                    Threshold = 0.1m, // 10% of portfolio
                    IsEnabled = true,
                    NotificationChannels = new List<string> { "Console" },
                    CooldownPeriod = TimeSpan.FromMinutes(5)
                },
                new AlertConfiguration
                {
                    AlertType = "SystemHealth",
                    Threshold = 0.8m, // 80% CPU/Memory
                    IsEnabled = true,
                    NotificationChannels = new List<string> { "Console" },
                    CooldownPeriod = TimeSpan.FromMinutes(10)
                }
            };

            foreach (var alert in defaultAlerts)
            {
                _alertConfigurations.TryAdd(alert.AlertType, alert);
            }
            
            _logger.LogInformation($"Initialized {defaultAlerts.Count} default alert configurations");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error initializing default alerts");
        }
    }

    public async Task StartMonitoringAsync()
    {
        try
        {
            if (_isMonitoring)
            {
                _logger.LogWarning("Monitoring is already running");
                return;
            }

            _logger.LogInformation("Starting real-time monitoring service");
            
            // Start monitoring timers
            var updateInterval = _configuration.GetValue<int>("Monitoring:UpdateIntervalMs", 5000);
            var alertCheckInterval = _configuration.GetValue<int>("Monitoring:AlertCheckIntervalMs", 10000);
            var healthCheckInterval = _configuration.GetValue<int>("Monitoring:HealthCheckIntervalMs", 30000);
            
            _monitoringTimer = new Timer(UpdateDashboardData, null, TimeSpan.Zero, TimeSpan.FromMilliseconds(updateInterval));
            _alertCheckTimer = new Timer(CheckAlerts, null, TimeSpan.Zero, TimeSpan.FromMilliseconds(alertCheckInterval));
            _healthCheckTimer = new Timer(PerformHealthChecks, null, TimeSpan.Zero, TimeSpan.FromMilliseconds(healthCheckInterval));
            
            _isMonitoring = true;
            _logger.LogInformation("Real-time monitoring started successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting real-time monitoring");
            throw;
        }
    }

    public async Task StopMonitoringAsync()
    {
        try
        {
            if (!_isMonitoring)
            {
                _logger.LogWarning("Monitoring is not running");
                return;
            }

            _logger.LogInformation("Stopping real-time monitoring service");
            
            _monitoringTimer?.Dispose();
            _alertCheckTimer?.Dispose();
            _healthCheckTimer?.Dispose();
            
            _isMonitoring = false;
            _logger.LogInformation("Real-time monitoring stopped");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping real-time monitoring");
        }
    }

    public async Task<LiveDashboardData> GetLiveDashboardDataAsync()
    {
        try
        {
            var account = await _alpacaService.GetAccountAsync();
            var positions = await _positionManager.GetActivePositionsAsync();
            var riskMetrics = await _riskManager.GetRealTimeRiskMetricsAsync();
            
            var dashboardData = new LiveDashboardData
            {
                Timestamp = DateTime.UtcNow,
                AccountValue = account?.Equity ?? 0,
                DayPnL = riskMetrics?.DayPnL ?? 0,
                UnrealizedPnL = positions.Sum(p => p.UnrealizedPnL),
                ActivePositions = positions.Count,
                Positions = await ConvertToLivePositions(positions),
                ActiveAlerts = _activeAlerts.Values.ToList(),
                CurrentMarket = await GetCurrentMarketConditions(),
                PendingSignals = new List<TradingSignal>() // Would be populated from strategy service
            };

            return dashboardData;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting live dashboard data");
            return new LiveDashboardData { Timestamp = DateTime.UtcNow };
        }
    }

    public async Task<bool> AddAlertConfigurationAsync(AlertConfiguration config)
    {
        try
        {
            _alertConfigurations.AddOrUpdate(config.AlertType, config, (key, oldValue) => config);
            _logger.LogInformation($"Added/updated alert configuration: {config.AlertType}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error adding alert configuration: {config.AlertType}");
            return false;
        }
    }

    public async Task<bool> RemoveAlertConfigurationAsync(string alertType)
    {
        try
        {
            var removed = _alertConfigurations.TryRemove(alertType, out _);
            if (removed)
            {
                _logger.LogInformation($"Removed alert configuration: {alertType}");
            }
            return removed;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error removing alert configuration: {alertType}");
            return false;
        }
    }

    public async Task<List<AlertConfiguration>> GetAlertConfigurationsAsync()
    {
        await Task.CompletedTask;
        return _alertConfigurations.Values.ToList();
    }

    public async Task<List<RiskAlert>> GetActiveAlertsAsync()
    {
        await Task.CompletedTask;
        return _activeAlerts.Values.ToList();
    }

    public async Task<bool> AcknowledgeAlertAsync(string alertId)
    {
        try
        {
            if (_activeAlerts.TryGetValue(alertId, out var alert))
            {
                alert.IsAcknowledged = true;
                alert.AcknowledgedAt = DateTime.UtcNow;
                _logger.LogInformation($"Alert acknowledged: {alertId}");
                return true;
            }
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error acknowledging alert: {alertId}");
            return false;
        }
    }

    public async Task<SystemMetrics> GetSystemMetricsAsync()
    {
        try
        {
            var metrics = new SystemMetrics
            {
                Timestamp = DateTime.UtcNow,
                CpuUsage = await GetCpuUsageAsync(),
                MemoryUsage = await GetMemoryUsageAsync(),
                NetworkBytesReceived = await GetNetworkBytesReceivedAsync(),
                NetworkBytesSent = await GetNetworkBytesSentAsync(),
                ActiveConnections = await GetActiveConnectionsAsync(),
                Uptime = DateTime.UtcNow - Process.GetCurrentProcess().StartTime,
                CustomMetrics = new Dictionary<string, decimal>()
            };

            // Keep history for trending
            _systemMetricsHistory.Enqueue(metrics);
            while (_systemMetricsHistory.Count > 1000) // Keep last 1000 metrics
            {
                _systemMetricsHistory.TryDequeue(out _);
            }

            return metrics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting system metrics");
            return new SystemMetrics { Timestamp = DateTime.UtcNow };
        }
    }

    public async Task<List<HealthCheckResult>> GetHealthCheckResultsAsync()
    {
        try
        {
            var healthChecks = new List<HealthCheckResult>();

            // Check Alpaca API connection
            healthChecks.Add(await CheckAlpacaHealthAsync());
            
            // Check database connection (if implemented)
            healthChecks.Add(await CheckDatabaseHealthAsync());
            
            // Check system resources
            healthChecks.Add(await CheckSystemResourcesAsync());
            
            // Check trading services
            healthChecks.Add(await CheckTradingServicesAsync());

            return healthChecks;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing health checks");
            return new List<HealthCheckResult>();
        }
    }

    // Private helper methods
    private async void UpdateDashboardData(object? state)
    {
        try
        {
            var dashboardData = await GetLiveDashboardDataAsync();
            DashboardDataUpdated?.Invoke(this, dashboardData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating dashboard data");
        }
    }

    private async void CheckAlerts(object? state)
    {
        try
        {
            foreach (var config in _alertConfigurations.Values.Where(c => c.IsEnabled))
            {
                await CheckSpecificAlert(config);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking alerts");
        }
    }

    private async void PerformHealthChecks(object? state)
    {
        try
        {
            var healthResults = await GetHealthCheckResultsAsync();
            
            // Check for unhealthy services and create alerts
            foreach (var result in healthResults.Where(r => !r.IsHealthy))
            {
                await CreateHealthAlert(result);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing health checks");
        }
    }

    private async Task CheckSpecificAlert(AlertConfiguration config)
    {
        try
        {
            // Check cooldown period
            if (DateTime.UtcNow - config.LastTriggered < config.CooldownPeriod)
                return;

            bool shouldTrigger = false;
            string message = "";
            decimal currentValue = 0;

            switch (config.AlertType)
            {
                case "DailyLossLimit":
                    var dayPnL = await _riskManager.GetDailyPnLAsync();
                    currentValue = Math.Abs(dayPnL);
                    shouldTrigger = currentValue >= config.Threshold;
                    message = $"Daily loss limit reached: {dayPnL:C} (Limit: {config.Threshold:C})";
                    break;

                case "PortfolioDrawdown":
                    var riskMetrics = await _riskManager.GetRealTimeRiskMetricsAsync();
                    currentValue = riskMetrics?.MaxDrawdown ?? 0;
                    shouldTrigger = currentValue >= config.Threshold;
                    message = $"Portfolio drawdown alert: {currentValue:P2} (Threshold: {config.Threshold:P2})";
                    break;

                case "HighRiskPosition":
                    var positions = await _positionManager.GetActivePositionsAsync();
                    var account = await _alpacaService.GetAccountAsync();
                    if (account != null && positions.Any())
                    {
                        var maxPositionRisk = positions.Max(p => Math.Abs(p.UnrealizedPnL) / (decimal)(account.Equity ?? 1));
                        currentValue = maxPositionRisk;
                        shouldTrigger = maxPositionRisk >= config.Threshold;
                        message = $"High risk position detected: {maxPositionRisk:P2} of portfolio";
                    }
                    break;

                case "SystemHealth":
                    var metrics = await GetSystemMetricsAsync();
                    currentValue = (decimal)Math.Max(metrics.CpuUsage, metrics.MemoryUsage);
                    shouldTrigger = currentValue >= config.Threshold;
                    message = $"System resource usage high: CPU {metrics.CpuUsage:P1}, Memory {metrics.MemoryUsage:P1}";
                    break;
            }

            if (shouldTrigger)
            {
                await TriggerAlert(config, message, currentValue);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error checking alert: {config.AlertType}");
        }
    }

    private async Task TriggerAlert(AlertConfiguration config, string message, decimal currentValue)
    {
        try
        {
            var alertSeverity = DetermineSeverity(config.AlertType, currentValue, config.Threshold);
            var riskLevel = ConvertAlertSeverityToRiskLevel(alertSeverity);

            var alert = new RiskAlert
            {
                Id = Guid.NewGuid().ToString(),
                Type = config.AlertType,
                Severity = riskLevel,
                Message = message,
                Timestamp = DateTime.UtcNow,
                IsAcknowledged = false,
                Value = currentValue,
                Threshold = config.Threshold
            };

            _activeAlerts.TryAdd(alert.Id, alert);
            config.LastTriggered = DateTime.UtcNow;

            // Send notifications
            await SendNotifications(alert, config.NotificationChannels);

            // Trigger event
            AlertTriggered?.Invoke(this, alert);

            _logger.LogWarning($"Alert triggered: {alert.Type} - {alert.Message}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error triggering alert");
        }
    }

    private AlertSeverity DetermineSeverity(string alertType, decimal currentValue, decimal threshold)
    {
        var ratio = currentValue / threshold;

        return alertType switch
        {
            "DailyLossLimit" => ratio >= 1.5m ? AlertSeverity.Critical : AlertSeverity.High,
            "PortfolioDrawdown" => ratio >= 2.0m ? AlertSeverity.Critical : AlertSeverity.High,
            "HighRiskPosition" => ratio >= 1.5m ? AlertSeverity.High : AlertSeverity.Medium,
            "SystemHealth" => ratio >= 1.2m ? AlertSeverity.High : AlertSeverity.Medium,
            _ => AlertSeverity.Medium
        };
    }

    private RiskLevel ConvertAlertSeverityToRiskLevel(AlertSeverity severity)
    {
        return severity switch
        {
            AlertSeverity.Low => RiskLevel.Low,
            AlertSeverity.Medium => RiskLevel.Medium,
            AlertSeverity.High => RiskLevel.High,
            AlertSeverity.Critical => RiskLevel.Critical,
            _ => RiskLevel.Medium
        };
    }

    private async Task SendNotifications(RiskAlert alert, List<string> channels)
    {
        foreach (var channel in channels)
        {
            try
            {
                switch (channel.ToLower())
                {
                    case "console":
                        Console.WriteLine($"[ALERT] {alert.Severity}: {alert.Message}");
                        break;
                    case "email":
                        await SendEmailNotification(alert);
                        break;
                    case "sms":
                        await SendSmsNotification(alert);
                        break;
                    case "discord":
                        await SendDiscordNotification(alert);
                        break;
                    // Add more notification channels as needed
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error sending notification via {channel}");
            }
        }
    }

    // Placeholder methods for system metrics and health checks
    private async Task<double> GetCpuUsageAsync() => await Task.FromResult(new Random().NextDouble() * 100);
    private async Task<double> GetMemoryUsageAsync() => await Task.FromResult(new Random().NextDouble() * 100);
    private async Task<long> GetNetworkBytesReceivedAsync() => await Task.FromResult(0L);
    private async Task<long> GetNetworkBytesSentAsync() => await Task.FromResult(0L);
    private async Task<int> GetActiveConnectionsAsync() => await Task.FromResult(1);
    private async Task<Dictionary<string, double>> GetCustomMetricsAsync() => await Task.FromResult(new Dictionary<string, double>());
    
    private async Task<List<LivePosition>> ConvertToLivePositions(List<ManagedPosition> positions)
    {
        var livePositions = new List<LivePosition>();

        foreach (var position in positions)
        {
            // Calculate aggregated Greeks from legs
            var totalDelta = position.Legs.Sum(l => l.Delta * l.Quantity);
            var totalGamma = position.Legs.Sum(l => l.Gamma * l.Quantity);
            var totalTheta = position.Legs.Sum(l => l.Theta * l.Quantity);
            var totalVega = position.Legs.Sum(l => l.Vega * l.Quantity);

            livePositions.Add(new LivePosition
            {
                PositionId = position.PositionId,
                Symbol = position.UnderlyingSymbol,
                Strategy = position.Strategy,
                CurrentValue = position.CurrentValue,
                UnrealizedPnL = position.UnrealizedPnL,
                PnLPercent = position.CurrentValue != 0 ? (position.UnrealizedPnL / position.CurrentValue) * 100 : 0,
                TimeInPosition = DateTime.UtcNow - position.OpenTime,
                Delta = totalDelta,
                Gamma = totalGamma,
                Theta = totalTheta,
                Vega = totalVega,
                LastUpdate = DateTime.UtcNow
            });
        }

        return livePositions;
    }

    private async Task<MarketConditions> GetCurrentMarketConditions()
    {
        try
        {
            // Get current market data (placeholder implementation)
            // var spyQuote = await _alpacaService.GetLatestQuoteAsync("SPY");
            // var vixQuote = await _alpacaService.GetLatestQuoteAsync("VIX");

            return new MarketConditions
            {
                VixLevel = 20.0m, // Placeholder
                SpxTrend = 0m, // Placeholder
                VolumeProfile = 1.0m, // Placeholder
                ImpliedVolatilityRank = 0.5m, // Placeholder
                MarketSession = GetCurrentMarketSession(),
                Timestamp = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting current market conditions");
            return new MarketConditions { Timestamp = DateTime.UtcNow };
        }
    }

    private async Task<HealthCheckResult> CheckAlpacaHealthAsync()
    {
        try
        {
            var account = await _alpacaService.GetAccountAsync();
            var isHealthy = account != null;

            return new HealthCheckResult
            {
                ServiceName = "Alpaca API",
                IsHealthy = isHealthy,
                Status = isHealthy ? "Connected" : "Disconnected",
                Timestamp = DateTime.UtcNow,
                ResponseTime = TimeSpan.FromMilliseconds(100), // Placeholder
                Details = isHealthy ? $"Account: {account?.AccountId}" : "Failed to connect to Alpaca API"
            };
        }
        catch (Exception ex)
        {
            return new HealthCheckResult
            {
                ServiceName = "Alpaca API",
                IsHealthy = false,
                Status = "Error",
                Timestamp = DateTime.UtcNow,
                Details = ex.Message
            };
        }
    }

    private async Task<HealthCheckResult> CheckDatabaseHealthAsync()
    {
        // Placeholder for database health check
        // In a real implementation, you would check database connectivity
        await Task.Delay(10);

        return new HealthCheckResult
        {
            ServiceName = "Database",
            IsHealthy = true,
            Status = "Not Implemented",
            Timestamp = DateTime.UtcNow,
            Details = "Database health check not implemented"
        };
    }

    private async Task<HealthCheckResult> CheckSystemResourcesAsync()
    {
        try
        {
            var metrics = await GetSystemMetricsAsync();
            var isHealthy = metrics.CpuUsage < 0.9 && metrics.MemoryUsage < 0.9;

            return new HealthCheckResult
            {
                ServiceName = "System Resources",
                IsHealthy = isHealthy,
                Status = isHealthy ? "Normal" : "High Usage",
                Timestamp = DateTime.UtcNow,
                Details = $"CPU: {metrics.CpuUsage:P1}, Memory: {metrics.MemoryUsage:P1}, Uptime: {metrics.Uptime}"
            };
        }
        catch (Exception ex)
        {
            return new HealthCheckResult
            {
                ServiceName = "System Resources",
                IsHealthy = false,
                Status = "Error",
                Timestamp = DateTime.UtcNow,
                Details = ex.Message
            };
        }
    }

    private async Task<HealthCheckResult> CheckTradingServicesAsync()
    {
        try
        {
            // Check if trading services are responsive
            var riskMetrics = await _riskManager.GetRealTimeRiskMetricsAsync();
            var positions = await _positionManager.GetActivePositionsAsync();

            var isHealthy = riskMetrics != null;

            return new HealthCheckResult
            {
                ServiceName = "Trading Services",
                IsHealthy = isHealthy,
                Status = isHealthy ? "Operational" : "Degraded",
                Timestamp = DateTime.UtcNow,
                Details = $"Active Positions: {positions.Count}, Risk Service: {(riskMetrics != null ? "OK" : "Error")}"
            };
        }
        catch (Exception ex)
        {
            return new HealthCheckResult
            {
                ServiceName = "Trading Services",
                IsHealthy = false,
                Status = "Error",
                Timestamp = DateTime.UtcNow,
                Details = ex.Message
            };
        }
    }

    private async Task CreateHealthAlert(HealthCheckResult result)
    {
        try
        {
            if (!result.IsHealthy)
            {
                var alert = new RiskAlert
                {
                    Id = Guid.NewGuid().ToString(),
                    Type = "HealthCheck",
                    Severity = RiskLevel.High,
                    Message = $"Health check failed for {result.ServiceName}: {result.Details}",
                    Timestamp = DateTime.UtcNow,
                    IsAcknowledged = false,
                    Value = 0,
                    Threshold = 0
                };

                _activeAlerts.TryAdd(alert.Id, alert);

                // Send notifications for health alerts
                await SendNotifications(alert, new List<string> { "Console", "Email" });

                AlertTriggered?.Invoke(this, alert);

                _logger.LogError($"Health alert created: {result.ServiceName} - {result.Details}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error creating health alert for {result.ServiceName}");
        }
    }

    private string GetCurrentMarketSession()
    {
        var now = DateTime.Now.TimeOfDay;

        if (now >= TimeSpan.FromHours(4) && now < TimeSpan.FromHours(9.5))
            return "Pre-Market";
        else if (now >= TimeSpan.FromHours(9.5) && now < TimeSpan.FromHours(16))
            return "Regular";
        else if (now >= TimeSpan.FromHours(16) && now < TimeSpan.FromHours(20))
            return "After-Hours";
        else
            return "Closed";
    }
    private async Task SendEmailNotification(RiskAlert alert)
    {
        try
        {
            var emailConfig = _configuration.GetSection("Monitoring:NotificationChannels:Email");
            if (!emailConfig.GetValue<bool>("Enabled", false))
            {
                _logger.LogDebug("Email notifications are disabled");
                return;
            }

            var notificationService = _serviceProvider?.GetService(typeof(INotificationService)) as INotificationService;
            if (notificationService != null)
            {
                await notificationService.SendEmailAlertAsync(alert);
                _logger.LogInformation($"Email notification sent for alert: {alert.Type}");
            }
            else
            {
                _logger.LogWarning("NotificationService not available for email alerts");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to send email notification for alert: {alert.Type}");
        }
    }

    private async Task SendSmsNotification(RiskAlert alert)
    {
        try
        {
            var smsConfig = _configuration.GetSection("Monitoring:NotificationChannels:SMS");
            if (!smsConfig.GetValue<bool>("Enabled", false))
            {
                _logger.LogDebug("SMS notifications are disabled");
                return;
            }

            var notificationService = _serviceProvider?.GetService(typeof(INotificationService)) as INotificationService;
            if (notificationService != null)
            {
                await notificationService.SendSmsAlertAsync(alert);
                _logger.LogInformation($"SMS notification sent for alert: {alert.Type}");
            }
            else
            {
                _logger.LogWarning("NotificationService not available for SMS alerts");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to send SMS notification for alert: {alert.Type}");
        }
    }

    private async Task SendDiscordNotification(RiskAlert alert)
    {
        try
        {
            var discordConfig = _configuration.GetSection("Monitoring:NotificationChannels:Discord");
            if (!discordConfig.GetValue<bool>("Enabled", false))
            {
                _logger.LogDebug("Discord notifications are disabled");
                return;
            }

            var notificationService = _serviceProvider?.GetService(typeof(INotificationService)) as INotificationService;
            if (notificationService != null)
            {
                await notificationService.SendDiscordAlertAsync(alert);
                _logger.LogInformation($"Discord notification sent for alert: {alert.Type}");
            }
            else
            {
                _logger.LogWarning("NotificationService not available for Discord alerts");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to send Discord notification for alert: {alert.Type}");
        }
    }
}
