using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Threading.Tasks;
using ZeroDateStrat.Models;

namespace ZeroDateStrat
{
    public class SafetyValidator
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<SafetyValidator> _logger;

        public SafetyValidator(IConfiguration configuration, ILogger<SafetyValidator> logger)
        {
            _configuration = configuration;
            _logger = logger;
        }

        public async Task<SafetyValidationResult> ValidateSystemSafetyAsync(decimal accountEquity)
        {
            var result = new SafetyValidationResult();
            
            _logger.LogInformation("🔍 Starting comprehensive safety validation...");
            
            // 1. Validate Risk Parameters
            await ValidateRiskParametersAsync(result, accountEquity);
            
            // 2. Validate Security Configuration
            await ValidateSecurityConfigurationAsync(result);
            
            // 3. Validate Trading Environment
            await ValidateTradingEnvironmentAsync(result);
            
            // 4. Validate Monitoring Setup
            await ValidateMonitoringSetupAsync(result);
            
            // 5. Calculate Overall Safety Score
            result.CalculateOverallScore();
            
            // 6. Generate Recommendations
            GenerateRecommendations(result, accountEquity);
            
            LogValidationResults(result);
            
            return result;
        }

        private async Task ValidateRiskParametersAsync(SafetyValidationResult result, decimal accountEquity)
        {
            _logger.LogInformation("📊 Validating risk parameters...");
            
            var maxPositionSize = _configuration.GetValue<decimal>("Trading:MaxPositionSize");
            var maxDailyLoss = _configuration.GetValue<decimal>("Trading:MaxDailyLoss");
            var riskPerTrade = _configuration.GetValue<decimal>("Trading:RiskPerTrade");
            
            // Position Size Validation
            var positionSizeRatio = maxPositionSize / accountEquity;
            if (positionSizeRatio <= 0.25m)
            {
                result.AddPass("Position Size", $"Safe: {positionSizeRatio:P1} of equity");
            }
            else if (positionSizeRatio <= 0.5m)
            {
                result.AddWarning("Position Size", $"Moderate: {positionSizeRatio:P1} of equity");
            }
            else
            {
                result.AddFail("Position Size", $"DANGEROUS: {positionSizeRatio:P1} of equity");
            }
            
            // Daily Loss Validation
            var dailyLossRatio = maxDailyLoss / accountEquity;
            if (dailyLossRatio <= 0.025m)
            {
                result.AddPass("Daily Loss Limit", $"Safe: {dailyLossRatio:P1} of equity");
            }
            else if (dailyLossRatio <= 0.05m)
            {
                result.AddWarning("Daily Loss Limit", $"Moderate: {dailyLossRatio:P1} of equity");
            }
            else
            {
                result.AddFail("Daily Loss Limit", $"DANGEROUS: {dailyLossRatio:P1} of equity");
            }
            
            // Risk Per Trade Validation
            if (riskPerTrade <= 0.01m)
            {
                result.AddPass("Risk Per Trade", $"Conservative: {riskPerTrade:P1}");
            }
            else if (riskPerTrade <= 0.02m)
            {
                result.AddWarning("Risk Per Trade", $"Moderate: {riskPerTrade:P1}");
            }
            else
            {
                result.AddFail("Risk Per Trade", $"Aggressive: {riskPerTrade:P1}");
            }
        }

        private async Task ValidateSecurityConfigurationAsync(SafetyValidationResult result)
        {
            _logger.LogInformation("🔐 Validating security configuration...");
            
            var apiKey = _configuration.GetValue<string>("Alpaca:ApiKey");
            var secretKey = _configuration.GetValue<string>("Alpaca:SecretKey");
            var polygonKey = _configuration.GetValue<string>("Polygon:ApiKey");
            
            // Check for encryption
            if (apiKey?.StartsWith("ENC:") == true)
            {
                result.AddPass("API Key Encryption", "Credentials are encrypted");
            }
            else
            {
                result.AddFail("API Key Encryption", "Credentials are in plain text");
            }
            
            if (secretKey?.StartsWith("ENC:") == true)
            {
                result.AddPass("Secret Key Encryption", "Credentials are encrypted");
            }
            else
            {
                result.AddFail("Secret Key Encryption", "Credentials are in plain text");
            }
            
            if (polygonKey?.StartsWith("ENC:") == true)
            {
                result.AddPass("Polygon Key Encryption", "Credentials are encrypted");
            }
            else
            {
                result.AddWarning("Polygon Key Encryption", "Polygon key not encrypted");
            }
        }

        private async Task ValidateTradingEnvironmentAsync(SafetyValidationResult result)
        {
            _logger.LogInformation("🏭 Validating trading environment...");
            
            var baseUrl = _configuration.GetValue<string>("Alpaca:BaseUrl");
            
            if (baseUrl?.Contains("paper-api") == true)
            {
                result.AddPass("Trading Environment", "Using paper trading (SAFE)");
            }
            else if (baseUrl?.Contains("api.alpaca.markets") == true)
            {
                result.AddWarning("Trading Environment", "Using LIVE trading - ensure validation complete");
            }
            else
            {
                result.AddFail("Trading Environment", "Unknown trading environment");
            }
        }

        private async Task ValidateMonitoringSetupAsync(SafetyValidationResult result)
        {
            _logger.LogInformation("📱 Validating monitoring setup...");
            
            var consoleEnabled = _configuration.GetValue<bool>("Monitoring:NotificationChannels:Console:Enabled");
            var emailEnabled = _configuration.GetValue<bool>("Monitoring:NotificationChannels:Email:Enabled");
            var smsEnabled = _configuration.GetValue<bool>("Monitoring:NotificationChannels:SMS:Enabled");
            
            if (consoleEnabled)
            {
                result.AddPass("Console Monitoring", "Console alerts enabled");
            }
            else
            {
                result.AddWarning("Console Monitoring", "Console alerts disabled");
            }
            
            if (emailEnabled)
            {
                result.AddPass("Email Monitoring", "Email alerts enabled");
            }
            else
            {
                result.AddWarning("Email Monitoring", "Email alerts not configured");
            }
            
            if (smsEnabled)
            {
                result.AddPass("SMS Monitoring", "SMS alerts enabled");
            }
            else
            {
                result.AddInfo("SMS Monitoring", "SMS alerts not configured (optional)");
            }
        }

        private void GenerateRecommendations(SafetyValidationResult result, decimal accountEquity)
        {
            _logger.LogInformation("💡 Generating safety recommendations...");
            
            if (result.FailureCount > 0)
            {
                result.Recommendations.Add("🚨 CRITICAL: Address all failed validations before trading");
            }
            
            if (result.WarningCount > 3)
            {
                result.Recommendations.Add("⚠️ Consider addressing warnings for optimal safety");
            }
            
            // Account-specific recommendations
            if (accountEquity < 5000)
            {
                result.Recommendations.Add($"💰 Account size (${accountEquity:N0}) is small - consider very conservative settings");
            }
            
            if (result.OverallScore < 80)
            {
                result.Recommendations.Add("📈 Overall safety score is low - review all configurations");
            }
            
            result.Recommendations.Add("🧪 Always test in paper trading before live deployment");
            result.Recommendations.Add("📊 Monitor positions closely during first week of trading");
            result.Recommendations.Add("🔄 Review and adjust parameters based on performance");
        }

        private void LogValidationResults(SafetyValidationResult result)
        {
            _logger.LogInformation("📋 Safety Validation Results:");
            _logger.LogInformation($"   Overall Score: {result.OverallScore:F1}%");
            _logger.LogInformation($"   Passed: {result.PassCount}");
            _logger.LogInformation($"   Warnings: {result.WarningCount}");
            _logger.LogInformation($"   Failed: {result.FailureCount}");
            _logger.LogInformation($"   Status: {result.GetOverallStatus()}");
            
            if (result.Recommendations.Any())
            {
                _logger.LogInformation("📝 Recommendations:");
                foreach (var recommendation in result.Recommendations)
                {
                    _logger.LogInformation($"   {recommendation}");
                }
            }
        }
    }
}
