using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using ZeroDateStrat.Models;

namespace ZeroDateStrat.Services;

/// <summary>
/// Service for fetching and managing Treasury rate data from multiple sources
/// Primary: FRED API, Secondary: Polygon, Tertiary: Fallback rates
/// </summary>
public interface ITreasuryRateService
{
    Task<decimal> GetRiskFreeRateAsync(decimal timeToMaturityYears);
    Task<YieldCurve> GetCurrentYieldCurveAsync();
    Task<TreasuryRate> GetTreasuryRateAsync(string maturity);
    Task<List<HistoricalTreasuryRate>> GetHistoricalRatesAsync(string maturity, DateTime startDate, DateTime endDate);
    Task<TreasuryRateChange> GetRateChangeAsync(string maturity, TimeSpan period);
    Task<TreasuryRateServiceStatus> GetServiceStatusAsync();
    Task<bool> TestConnectionAsync();
    Task RefreshCacheAsync();
}

public class TreasuryRateService : ITreasuryRateService
{
    private readonly ILogger<TreasuryRateService> _logger;
    private readonly IConfiguration _configuration;
    private readonly HttpClient _httpClient;
    private readonly IPolygonDataService? _polygonDataService;
    
    // Caching
    private readonly Dictionary<string, TreasuryRate> _cachedRates = new();
    private readonly Dictionary<string, List<HistoricalTreasuryRate>> _cachedHistoricalRates = new();
    private DateTime _lastCacheUpdate = DateTime.MinValue;
    private readonly TimeSpan _cacheExpiry;
    
    // FRED API configuration
    private readonly string _fredApiKey;
    private readonly string _fredBaseUrl;
    private readonly Dictionary<string, string> _treasuryRateSeries;
    private readonly Dictionary<string, decimal> _fallbackRates;
    private readonly decimal _defaultRiskFreeRate;

    public TreasuryRateService(
        ILogger<TreasuryRateService> logger,
        IConfiguration configuration,
        HttpClient httpClient,
        IPolygonDataService? polygonDataService = null)
    {
        _logger = logger;
        _configuration = configuration;
        _httpClient = httpClient;
        _polygonDataService = polygonDataService;
        
        // Load configuration
        _fredApiKey = _configuration["FRED:ApiKey"] ?? "YOUR_FRED_API_KEY_HERE";
        _fredBaseUrl = _configuration["FRED:BaseUrl"] ?? "https://api.stlouisfed.org/fred";
        _cacheExpiry = TimeSpan.FromMinutes(_configuration.GetValue<int>("FRED:CacheExpiryMinutes", 60));
        _defaultRiskFreeRate = _configuration.GetValue<decimal>("FRED:DefaultRiskFreeRate", 0.0525m);
        
        // Load Treasury rate series mappings
        _treasuryRateSeries = new Dictionary<string, string>();
        var seriesSection = _configuration.GetSection("FRED:TreasuryRateSeries");
        foreach (var item in seriesSection.GetChildren())
        {
            _treasuryRateSeries[item.Key] = item.Value ?? "";
        }
        
        // Load fallback rates
        _fallbackRates = new Dictionary<string, decimal>();
        var fallbackSection = _configuration.GetSection("FRED:FallbackRates");
        foreach (var item in fallbackSection.GetChildren())
        {
            if (decimal.TryParse(item.Value, out var rate))
            {
                _fallbackRates[item.Key] = rate;
            }
        }
        
        _logger.LogInformation($"TreasuryRateService initialized with {_treasuryRateSeries.Count} rate series");
    }

    public async Task<decimal> GetRiskFreeRateAsync(decimal timeToMaturityYears)
    {
        try
        {
            _logger.LogDebug($"Getting risk-free rate for {timeToMaturityYears:F4} years to maturity");
            
            var yieldCurve = await GetCurrentYieldCurveAsync();
            var rate = yieldCurve.InterpolateRate(timeToMaturityYears);
            
            // Convert from percentage to decimal if needed
            if (rate > 1) rate = rate / 100m;
            
            _logger.LogDebug($"Risk-free rate for {timeToMaturityYears:F4} years: {rate:F4} ({rate * 100:F2}%)");
            return rate;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error getting risk-free rate for {timeToMaturityYears:F4} years");
            return _defaultRiskFreeRate;
        }
    }

    public async Task<YieldCurve> GetCurrentYieldCurveAsync()
    {
        try
        {
            // Check cache first
            if (DateTime.UtcNow - _lastCacheUpdate < _cacheExpiry && _cachedRates.Count > 0)
            {
                _logger.LogDebug("Returning cached yield curve");
                return BuildYieldCurveFromCache();
            }

            _logger.LogDebug("Fetching current yield curve from FRED API");
            
            var yieldCurve = new YieldCurve
            {
                Date = DateTime.UtcNow.Date,
                Source = "FRED",
                Timestamp = DateTime.UtcNow
            };

            // Fetch all Treasury rates in parallel
            var tasks = _treasuryRateSeries.Select(async kvp =>
            {
                try
                {
                    var rate = await GetTreasuryRateFromFredAsync(kvp.Value);
                    if (rate != null)
                    {
                        yieldCurve.Rates[kvp.Key] = rate.Rate;
                        _cachedRates[kvp.Key] = rate;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, $"Failed to fetch {kvp.Key} rate from FRED");
                    
                    // Try fallback rate
                    if (_fallbackRates.ContainsKey(kvp.Key))
                    {
                        yieldCurve.Rates[kvp.Key] = _fallbackRates[kvp.Key];
                        _logger.LogDebug($"Using fallback rate for {kvp.Key}: {_fallbackRates[kvp.Key]:F4}");
                    }
                }
            });

            await Task.WhenAll(tasks);
            
            // If we got some rates, update cache timestamp
            if (yieldCurve.Rates.Count > 0)
            {
                _lastCacheUpdate = DateTime.UtcNow;
                _logger.LogInformation($"Updated yield curve with {yieldCurve.Rates.Count} rates from FRED");
            }
            else
            {
                // Use all fallback rates
                _logger.LogWarning("No rates fetched from FRED, using all fallback rates");
                yieldCurve.Rates = new Dictionary<string, decimal>(_fallbackRates);
                yieldCurve.Source = "Fallback";
            }

            return yieldCurve;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting current yield curve");
            return GetFallbackYieldCurve();
        }
    }

    public async Task<TreasuryRate> GetTreasuryRateAsync(string maturity)
    {
        try
        {
            // Check cache first
            if (_cachedRates.ContainsKey(maturity) && 
                DateTime.UtcNow - _lastCacheUpdate < _cacheExpiry)
            {
                return _cachedRates[maturity];
            }

            if (!_treasuryRateSeries.ContainsKey(maturity))
            {
                _logger.LogWarning($"No FRED series found for maturity: {maturity}");
                return GetFallbackRate(maturity);
            }

            var seriesId = _treasuryRateSeries[maturity];
            var rate = await GetTreasuryRateFromFredAsync(seriesId);
            
            if (rate != null)
            {
                _cachedRates[maturity] = rate;
                return rate;
            }

            return GetFallbackRate(maturity);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error getting Treasury rate for {maturity}");
            return GetFallbackRate(maturity);
        }
    }

    private async Task<TreasuryRate?> GetTreasuryRateFromFredAsync(string seriesId)
    {
        try
        {
            if (string.IsNullOrEmpty(_fredApiKey) || _fredApiKey == "YOUR_FRED_API_KEY_HERE")
            {
                _logger.LogWarning("FRED API key not configured, using fallback rates");
                return null;
            }

            var url = $"{_fredBaseUrl}/series/observations?series_id={seriesId}&api_key={_fredApiKey}&file_type=json&limit=1&sort_order=desc";
            
            var response = await _httpClient.GetAsync(url);
            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning($"FRED API returned {response.StatusCode} for series {seriesId}");
                return null;
            }

            var content = await response.Content.ReadAsStringAsync();
            var fredResponse = JsonSerializer.Deserialize<FredApiResponse<FredObservation>>(content);
            
            if (fredResponse?.Observations?.Count > 0)
            {
                var observation = fredResponse.Observations.First();
                if (decimal.TryParse(observation.Value, out var rateValue) && 
                    DateTime.TryParse(observation.Date, out var rateDate))
                {
                    // Convert percentage to decimal
                    var rate = rateValue > 1 ? rateValue / 100m : rateValue;
                    
                    return new TreasuryRate
                    {
                        Maturity = seriesId,
                        Rate = rate,
                        Date = rateDate,
                        Source = "FRED",
                        Timestamp = DateTime.UtcNow
                    };
                }
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error fetching Treasury rate from FRED for series {seriesId}");
            return null;
        }
    }

    private YieldCurve BuildYieldCurveFromCache()
    {
        var yieldCurve = new YieldCurve
        {
            Date = DateTime.UtcNow.Date,
            Source = "Cache",
            Timestamp = DateTime.UtcNow
        };

        foreach (var kvp in _cachedRates)
        {
            yieldCurve.Rates[kvp.Key] = kvp.Value.Rate;
        }

        return yieldCurve;
    }

    private YieldCurve GetFallbackYieldCurve()
    {
        return new YieldCurve
        {
            Date = DateTime.UtcNow.Date,
            Source = "Fallback",
            Timestamp = DateTime.UtcNow,
            Rates = new Dictionary<string, decimal>(_fallbackRates)
        };
    }

    private TreasuryRate GetFallbackRate(string maturity)
    {
        var rate = _fallbackRates.ContainsKey(maturity) ? _fallbackRates[maturity] : _defaultRiskFreeRate;

        return new TreasuryRate
        {
            Maturity = maturity,
            Rate = rate,
            Date = DateTime.UtcNow.Date,
            Source = "Fallback",
            Timestamp = DateTime.UtcNow
        };
    }

    public async Task<List<HistoricalTreasuryRate>> GetHistoricalRatesAsync(string maturity, DateTime startDate, DateTime endDate)
    {
        try
        {
            // Check cache first
            var cacheKey = $"{maturity}_{startDate:yyyyMMdd}_{endDate:yyyyMMdd}";
            if (_cachedHistoricalRates.ContainsKey(cacheKey))
            {
                _logger.LogDebug($"Returning cached historical rates for {maturity}");
                return _cachedHistoricalRates[cacheKey];
            }

            if (!_treasuryRateSeries.ContainsKey(maturity))
            {
                _logger.LogWarning($"No FRED series found for maturity: {maturity}");
                return new List<HistoricalTreasuryRate>();
            }

            var seriesId = _treasuryRateSeries[maturity];
            var historicalRates = await GetHistoricalRatesFromFredAsync(seriesId, startDate, endDate);

            // Cache the results
            _cachedHistoricalRates[cacheKey] = historicalRates;

            return historicalRates;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error getting historical Treasury rates for {maturity}");
            return new List<HistoricalTreasuryRate>();
        }
    }

    private async Task<List<HistoricalTreasuryRate>> GetHistoricalRatesFromFredAsync(string seriesId, DateTime startDate, DateTime endDate)
    {
        try
        {
            if (string.IsNullOrEmpty(_fredApiKey) || _fredApiKey == "YOUR_FRED_API_KEY_HERE")
            {
                _logger.LogWarning("FRED API key not configured for historical data");
                return new List<HistoricalTreasuryRate>();
            }

            var startDateStr = startDate.ToString("yyyy-MM-dd");
            var endDateStr = endDate.ToString("yyyy-MM-dd");
            var url = $"{_fredBaseUrl}/series/observations?series_id={seriesId}&api_key={_fredApiKey}&file_type=json&observation_start={startDateStr}&observation_end={endDateStr}&sort_order=desc&limit=10000";

            var response = await _httpClient.GetAsync(url);
            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning($"FRED API returned {response.StatusCode} for historical series {seriesId}");
                return new List<HistoricalTreasuryRate>();
            }

            var content = await response.Content.ReadAsStringAsync();
            var fredResponse = JsonSerializer.Deserialize<FredApiResponse<FredObservation>>(content);

            var historicalRates = new List<HistoricalTreasuryRate>();

            if (fredResponse?.Observations != null)
            {
                foreach (var observation in fredResponse.Observations)
                {
                    if (decimal.TryParse(observation.Value, out var rateValue) &&
                        DateTime.TryParse(observation.Date, out var rateDate) &&
                        observation.Value != ".")  // FRED uses "." for missing values
                    {
                        // Convert percentage to decimal
                        var rate = rateValue > 1 ? rateValue / 100m : rateValue;

                        historicalRates.Add(new HistoricalTreasuryRate
                        {
                            Date = rateDate,
                            Maturity = seriesId,
                            Rate = rate,
                            Source = "FRED"
                        });
                    }
                }
            }

            _logger.LogInformation($"Retrieved {historicalRates.Count} historical rates for {seriesId}");
            return historicalRates.OrderBy(r => r.Date).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error fetching historical Treasury rates from FRED for series {seriesId}");
            return new List<HistoricalTreasuryRate>();
        }
    }

    public async Task<TreasuryRateChange> GetRateChangeAsync(string maturity, TimeSpan period)
    {
        try
        {
            var endDate = DateTime.UtcNow.Date;
            var startDate = endDate.Subtract(period);

            var historicalRates = await GetHistoricalRatesAsync(maturity, startDate, endDate);

            if (historicalRates.Count < 2)
            {
                _logger.LogWarning($"Insufficient historical data for rate change calculation: {maturity}");
                return new TreasuryRateChange
                {
                    Maturity = maturity,
                    Period = period
                };
            }

            var currentRate = historicalRates.Last();
            var previousRate = historicalRates.First();

            var change = currentRate.Rate - previousRate.Rate;
            var changePercent = previousRate.Rate != 0 ? (change / previousRate.Rate) * 100 : 0;

            return new TreasuryRateChange
            {
                Maturity = maturity,
                CurrentRate = currentRate.Rate,
                PreviousRate = previousRate.Rate,
                Change = change,
                ChangePercent = changePercent,
                Period = period,
                Timestamp = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error calculating rate change for {maturity}");
            return new TreasuryRateChange
            {
                Maturity = maturity,
                Period = period
            };
        }
    }

    public async Task<TreasuryRateServiceStatus> GetServiceStatusAsync()
    {
        try
        {
            var status = new TreasuryRateServiceStatus
            {
                LastUpdate = _lastCacheUpdate,
                PrimarySource = "FRED",
                SecondarySource = "Fallback",
                CachedRatesCount = _cachedRates.Count,
                CacheAge = DateTime.UtcNow - _lastCacheUpdate
            };

            // Test FRED connection
            var connectionTest = await TestConnectionAsync();
            status.IsHealthy = connectionTest;

            if (!connectionTest)
            {
                status.Issues.Add("FRED API connection failed");
            }

            if (_cachedRates.Count == 0)
            {
                status.Issues.Add("No cached rates available");
            }

            if (status.CacheAge > _cacheExpiry)
            {
                status.Issues.Add($"Cache expired ({status.CacheAge.TotalMinutes:F1} minutes old)");
            }

            // Add current rates to status
            foreach (var kvp in _cachedRates)
            {
                status.CurrentRates[kvp.Key] = kvp.Value.Rate;
            }

            return status;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting Treasury rate service status");
            return new TreasuryRateServiceStatus
            {
                IsHealthy = false,
                Issues = new List<string> { "Service status check failed" }
            };
        }
    }

    public async Task<bool> TestConnectionAsync()
    {
        try
        {
            if (string.IsNullOrEmpty(_fredApiKey) || _fredApiKey == "YOUR_FRED_API_KEY_HERE")
            {
                _logger.LogWarning("FRED API key not configured");
                return false;
            }

            // Test with a simple series request
            var url = $"{_fredBaseUrl}/series?series_id=DGS3MO&api_key={_fredApiKey}&file_type=json&limit=1";
            var response = await _httpClient.GetAsync(url);

            var success = response.IsSuccessStatusCode;
            _logger.LogInformation($"FRED API connection test: {(success ? "Success" : "Failed")}");

            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "FRED API connection test failed");
            return false;
        }
    }

    public async Task RefreshCacheAsync()
    {
        try
        {
            _logger.LogInformation("Refreshing Treasury rate cache");

            // Clear existing cache
            _cachedRates.Clear();
            _cachedHistoricalRates.Clear();
            _lastCacheUpdate = DateTime.MinValue;

            // Fetch fresh data
            await GetCurrentYieldCurveAsync();

            _logger.LogInformation($"Treasury rate cache refreshed with {_cachedRates.Count} rates");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error refreshing Treasury rate cache");
        }
    }
}
