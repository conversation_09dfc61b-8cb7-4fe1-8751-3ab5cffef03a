using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using ZeroDateStrat.Services;
using ZeroDateStrat.Models;

namespace ZeroDateStrat.Tests;

public static class DiscordErrorNotificationTest
{
    public static async Task RunTestAsync()
    {
        Console.WriteLine("🧪 Starting Discord Error Notification Test...");

        try
        {
            // Build configuration
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false)
                .AddJsonFile("appsettings.Development.json", optional: true)
                .AddEnvironmentVariables()
                .Build();

            // Setup services
            var services = new ServiceCollection();
            services.AddSingleton<IConfiguration>(configuration);
            services.AddLogging(builder =>
            {
                builder.AddConsole();
                builder.SetMinimumLevel(LogLevel.Debug);
            });

            // Add Discord service
            services.AddSingleton<IDiscordService, DiscordService>();

            // Add GlobalExceptionHandler with Discord integration
            services.AddSingleton<IGlobalExceptionHandler>(provider =>
            {
                var logger = provider.GetRequiredService<ILogger<GlobalExceptionHandler>>();
                var discordService = provider.GetService<IDiscordService>();
                return new GlobalExceptionHandler(logger, discordService);
            });

            var serviceProvider = services.BuildServiceProvider();
            var loggerFactory = serviceProvider.GetRequiredService<ILoggerFactory>();
            var logger = loggerFactory.CreateLogger("DiscordErrorNotificationTest");
            var discordService = serviceProvider.GetRequiredService<IDiscordService>();
            var exceptionHandler = serviceProvider.GetRequiredService<IGlobalExceptionHandler>();

            // Start Discord service
            logger.LogInformation("🚀 Starting Discord service...");
            await discordService.StartAsync();

            // Wait for connection
            await Task.Delay(3000);

            // Check if Discord is connected
            var isConnected = await discordService.IsConnectedAsync();
            if (!isConnected)
            {
                logger.LogWarning("❌ Discord not connected, skipping error notification tests");
                return;
            }

            logger.LogInformation("✅ Discord connected successfully!");

            // Test 1: Direct Discord error notification
            logger.LogInformation("📧 Test 1: Testing direct Discord error notification...");
            var testException = new InvalidOperationException("Test trading error for Discord notification");
            await discordService.SendTradingErrorAsync(testException, "Test Trading Operation", "Medium");
            await Task.Delay(2000);

            // Test 2: Exception handler with trading context
            logger.LogInformation("📧 Test 2: Testing exception handler with trading context...");
            var tradingException = new TimeoutException("Order placement timeout during signal execution");
            await exceptionHandler.HandleTradingExceptionAsync(tradingException, "Signal Execution - Iron Condor for SPY");
            await Task.Delay(2000);

            // Test 3: Exception handler with non-trading context (should not send Discord notification)
            logger.LogInformation("📧 Test 3: Testing exception handler with non-trading context...");
            var nonTradingException = new ArgumentException("Configuration validation error");
            await exceptionHandler.HandleTradingExceptionAsync(nonTradingException, "Configuration Validation");
            await Task.Delay(2000);

            // Test 4: High severity trading error
            logger.LogInformation("📧 Test 4: Testing high severity trading error...");
            var criticalException = new UnauthorizedAccessException("Alpaca API authentication failed during order placement");
            await exceptionHandler.HandleTradingExceptionAsync(criticalException, "Order Placement - Credit Spread for QQQ");
            await Task.Delay(2000);

            // Test 5: Exception with inner exception
            logger.LogInformation("📧 Test 5: Testing exception with inner exception...");
            var innerException = new HttpRequestException("Network timeout");
            var outerException = new InvalidOperationException("Failed to retrieve market data", innerException);
            await exceptionHandler.HandleTradingExceptionAsync(outerException, "Market Data Retrieval for Position Management");
            await Task.Delay(2000);

            // Test 6: Test exception statistics
            logger.LogInformation("📊 Test 6: Checking exception statistics...");
            var stats = await exceptionHandler.GetExceptionStatisticsAsync();
            logger.LogInformation($"Exception Statistics: Total: {stats.TotalExceptions}, Last Hour: {stats.ExceptionsLastHour}");

            // Send test completion notification
            await discordService.SendMessageAsync("✅ **Discord Error Notification Test Completed Successfully!**\n\n" +
                                                 "All error notification tests have been executed. Check the messages above to verify that:\n" +
                                                 "• Direct error notifications work\n" +
                                                 "• Trading context errors trigger Discord alerts\n" +
                                                 "• Non-trading errors are handled appropriately\n" +
                                                 "• Different severity levels are displayed correctly\n" +
                                                 "• Inner exceptions are included in notifications");

            logger.LogInformation("✅ All Discord error notification tests completed successfully!");

            // Stop Discord service
            await discordService.StopAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Discord error notification test failed: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
    }
}
