# ZeroDateStrat - API Reference & Integration Guide

## Core Service APIs

### IAlpacaService

Primary interface for Alpaca Markets integration.

#### Methods

##### `Task<bool> InitializeAsync()`
Initializes connection to Alpaca Markets API.

**Returns**: `bool` - Success status
**Throws**: `Exception` - Connection or authentication failures

```csharp
var alpacaService = serviceProvider.GetService<IAlpacaService>();
var success = await alpacaService.InitializeAsync();
```

##### `Task<IAccount> GetAccountAsync()`
Retrieves current account information.

**Returns**: `IAccount` - Account details including equity, buying power
**Requires**: Initialized connection

```csharp
var account = await alpacaService.GetAccountAsync();
Console.WriteLine($"Equity: {account.Equity:C}");
```

##### `Task<bool> PlaceOrderAsync(OrderRequest request)`
Places a trading order.

**Parameters**:
- `OrderRequest request` - Order details (symbol, quantity, side, type)

**Returns**: `bool` - Order placement success
**Validation**: Risk checks performed before execution

```csharp
var orderRequest = new OrderRequest
{
    Symbol = "SPY",
    Quantity = 1,
    Side = OrderSide.Buy,
    Type = OrderType.Market
};
var success = await alpacaService.PlaceOrderAsync(orderRequest);
```

##### `Task<IReadOnlyList<IPosition>> GetPositionsAsync()`
Retrieves all current positions.

**Returns**: `IReadOnlyList<IPosition>` - List of open positions
**Includes**: Unrealized P&L, market value, quantity

```csharp
var positions = await alpacaService.GetPositionsAsync();
foreach (var position in positions)
{
    Console.WriteLine($"{position.Symbol}: {position.UnrealizedPnl:C}");
}
```

### IRiskManager

Risk management and validation service.

#### Methods

##### `Task<RiskValidationResult> ValidateTradeAsync(TradingSignal signal)`
Validates a trading signal against risk parameters.

**Parameters**:
- `TradingSignal signal` - Signal to validate

**Returns**: `RiskValidationResult` - Validation result with approval status
**Checks**: Position limits, daily loss, concentration, buying power

```csharp
var validation = await riskManager.ValidateTradeAsync(signal);
if (validation.IsApproved)
{
    // Execute trade
}
else
{
    Console.WriteLine($"Trade rejected: {validation.RejectionReason}");
}
```

##### `Task<PortfolioRisk> CalculatePortfolioRiskAsync()`
Calculates current portfolio risk metrics.

**Returns**: `PortfolioRisk` - Risk metrics including VaR, concentration
**Updates**: Real-time risk calculations

```csharp
var portfolioRisk = await riskManager.CalculatePortfolioRiskAsync();
Console.WriteLine($"Portfolio VaR: {portfolioRisk.ValueAtRisk:P2}");
```

### IZeroDteStrategy

Main trading strategy interface.

#### Methods

##### `Task<bool> ShouldTrade()`
Determines if trading should occur based on market conditions.

**Returns**: `bool` - Trading approval status
**Checks**: Market hours, volatility, account status

```csharp
var shouldTrade = await strategy.ShouldTrade();
if (shouldTrade)
{
    var signals = await strategy.GenerateSignalsAsync();
}
```

##### `Task<List<TradingSignal>> GenerateSignalsAsync()`
Generates trading signals based on current market conditions.

**Returns**: `List<TradingSignal>` - List of trading opportunities
**Strategies**: Put Credit Spreads, Iron Butterflies, Call Credit Spreads

```csharp
var signals = await strategy.GenerateSignalsAsync();
foreach (var signal in signals.Take(3)) // Limit to top 3
{
    await strategy.ExecuteSignalAsync(signal);
}
```

##### `Task<bool> ExecuteSignalAsync(TradingSignal signal)`
Executes a trading signal.

**Parameters**:
- `TradingSignal signal` - Signal to execute

**Returns**: `bool` - Execution success status
**Process**: Risk validation → Order placement → Position tracking

```csharp
var success = await strategy.ExecuteSignalAsync(signal);
if (success)
{
    Console.WriteLine($"Signal {signal.Id} executed successfully");
}
```

### ISecurityService

Security and credential management service.

#### Methods

##### `Task<string> GetSecureApiKeyAsync()`
Retrieves API key with decryption if needed.

**Returns**: `string` - Decrypted API key
**Security**: Supports encrypted storage with ENC: prefix

```csharp
var apiKey = await securityService.GetSecureApiKeyAsync();
// Use for API authentication
```

##### `Task<SecurityAuditResult> PerformSecurityAuditAsync()`
Performs comprehensive security audit.

**Returns**: `SecurityAuditResult` - Security assessment with score
**Checks**: Credential security, environment validation, configuration

```csharp
var audit = await securityService.PerformSecurityAuditAsync();
Console.WriteLine($"Security Score: {audit.OverallSecurityScore:P0}");
```

## Configuration API

### Trading Configuration

```csharp
public class TradingConfiguration
{
    public string PrimarySymbol { get; set; } = "SPX";
    public decimal MaxPositionSize { get; set; } = 10000;
    public decimal MaxDailyLoss { get; set; } = 500;
    public decimal RiskPerTrade { get; set; } = 0.02m;
    public int MaxPositionsPerDay { get; set; } = 5;
    public TimeSpan EntryTimeStart { get; set; }
    public TimeSpan EntryTimeEnd { get; set; }
    public decimal ProfitTargetPercent { get; set; } = 0.5m;
    public decimal StopLossPercent { get; set; } = 2.0m;
}
```

### Risk Configuration

```csharp
public class RiskConfiguration
{
    public decimal MaxDrawdown { get; set; } = 0.08m;
    public decimal VaRLimit { get; set; } = 0.03m;
    public decimal MaxConcentration { get; set; } = 0.6m;
    public int MaxDailyTrades { get; set; } = 8;
    public int MaxOpenPositions { get; set; } = 12;
    public decimal RiskRewardMinimum { get; set; } = 0.15m;
}
```

## Data Models

### TradingSignal

```csharp
public class TradingSignal
{
    public string Id { get; set; }
    public string Strategy { get; set; }
    public string UnderlyingSymbol { get; set; }
    public decimal ExpectedProfit { get; set; }
    public decimal MaxLoss { get; set; }
    public decimal RiskRewardRatio { get; set; }
    public decimal Confidence { get; set; }
    public DateTime ExpirationDate { get; set; }
    public List<OptionLeg> Legs { get; set; }
}
```

### Position

```csharp
public class Position
{
    public string Id { get; set; }
    public string Strategy { get; set; }
    public string UnderlyingSymbol { get; set; }
    public decimal OpenCredit { get; set; }
    public decimal CurrentValue { get; set; }
    public decimal UnrealizedPnL { get; set; }
    public DateTime OpenTime { get; set; }
    public DateTime ExpirationTime { get; set; }
    public List<PositionLeg> Legs { get; set; }
}
```

### RiskAlert

```csharp
public class RiskAlert
{
    public string Id { get; set; }
    public RiskAlertType Type { get; set; }
    public RiskLevel Severity { get; set; }
    public string Message { get; set; }
    public decimal? Value { get; set; }
    public decimal? Threshold { get; set; }
    public DateTime Timestamp { get; set; }
    public Dictionary<string, object> Metadata { get; set; }
}
```

## Error Handling

### Exception Types

#### AlpacaApiException
Thrown when Alpaca API calls fail.

```csharp
try
{
    await alpacaService.PlaceOrderAsync(order);
}
catch (AlpacaApiException ex)
{
    logger.LogError($"Alpaca API error: {ex.Message}");
    // Handle API-specific errors
}
```

#### RiskValidationException
Thrown when risk validation fails.

```csharp
try
{
    await strategy.ExecuteSignalAsync(signal);
}
catch (RiskValidationException ex)
{
    logger.LogWarning($"Trade rejected: {ex.Message}");
    // Handle risk rejection
}
```

#### SecurityException
Thrown for security-related issues.

```csharp
try
{
    await securityService.GetSecureApiKeyAsync();
}
catch (SecurityException ex)
{
    logger.LogError($"Security error: {ex.Message}");
    // Handle security issues
}
```

### Global Exception Handler

The `IGlobalExceptionHandler` provides centralized exception management:

```csharp
// Automatic retry with exponential backoff
var result = await exceptionHandler.ExecuteWithRetryAsync(async () =>
{
    return await riskyOperation();
}, maxRetries: 3, context: "OperationName");

// Manual exception handling
var handled = await exceptionHandler.HandleExceptionAsync(exception, "Context");
```

## Integration Examples

### Basic Trading Loop

```csharp
while (!cancellationToken.IsCancellationRequested)
{
    try
    {
        if (await strategy.ShouldTrade())
        {
            var signals = await strategy.GenerateSignalsAsync();
            
            foreach (var signal in signals.Take(3))
            {
                var validation = await riskManager.ValidateTradeAsync(signal);
                if (validation.IsApproved)
                {
                    await strategy.ExecuteSignalAsync(signal);
                }
            }
        }
        
        await strategy.ManagePositionsAsync();
        await Task.Delay(30000, cancellationToken); // 30-second cycle
    }
    catch (Exception ex)
    {
        await exceptionHandler.HandleExceptionAsync(ex, "TradingLoop");
        await Task.Delay(5000, cancellationToken); // Error recovery delay
    }
}
```

### Risk Monitoring

```csharp
// Continuous risk monitoring
var portfolioRisk = await riskManager.CalculatePortfolioRiskAsync();

if (portfolioRisk.ValueAtRisk > riskConfig.VaRLimit)
{
    await notificationService.SendAlertAsync(new RiskAlert
    {
        Type = RiskAlertType.VaRExceeded,
        Severity = RiskLevel.High,
        Message = $"Portfolio VaR exceeded: {portfolioRisk.ValueAtRisk:P2}",
        Value = portfolioRisk.ValueAtRisk,
        Threshold = riskConfig.VaRLimit
    });
}
```

### Security Audit

```csharp
// Perform security audit on startup
var securityAudit = await securityService.PerformSecurityAuditAsync();

if (!securityAudit.IsSecure)
{
    logger.LogWarning($"Security issues detected. Score: {securityAudit.OverallSecurityScore:P0}");
    
    foreach (var check in securityAudit.Checks.Where(c => !c.Passed))
    {
        logger.LogWarning($"Security Issue: {check.CheckName} - {check.Message}");
    }
}
```

---

*API Reference Last Updated: December 2024*
*Version: Phase 3*
*Compatibility: .NET 8.0+*
