using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Serilog;
using ZeroDateStrat.Models;
using ZeroDateStrat.Services;
using ILogger = Microsoft.Extensions.Logging.ILogger;

namespace ZeroDateStrat.Tests;

/// <summary>
/// Quick test for SyntheticVIX Analytics Service
/// </summary>
public static class QuickSyntheticVixTest
{
    public static async Task RunQuickTestAsync()
    {
        Console.WriteLine("🔬 Quick SyntheticVIX Analytics Test");
        Console.WriteLine(new string('=', 50));
        Console.WriteLine($"Test started at: {DateTime.Now}");

        try
        {
            // Setup basic services
            var services = new ServiceCollection();
            
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false)
                .AddJsonFile("appsettings.production.json", optional: true)
                .Build();

            services.AddSingleton<IConfiguration>(configuration);
            services.AddLogging(builder => builder.AddConsole());
            
            // Add required services
            services.AddSingleton<IPerformanceMonitoringService, PerformanceMonitoringService>();
            services.AddSingleton<IAlpacaService, AlpacaService>();
            services.AddSingleton<ISyntheticVixAnalyticsService, SyntheticVixAnalyticsService>();
            services.AddSingleton<ISyntheticVixService, SyntheticVixService>();
            services.AddSingleton<IAlpacaVixService, AlpacaVixService>();
            services.AddSingleton<IPolygonDataService, PolygonDataService>();

            var serviceProvider = services.BuildServiceProvider();
            var analyticsService = serviceProvider.GetRequiredService<ISyntheticVixAnalyticsService>();
            var logger = serviceProvider.GetRequiredService<ILoggerFactory>().CreateLogger("QuickTest");

            Console.WriteLine("✅ Services initialized");

            // Test 1: Health Report
            Console.WriteLine("\n📊 Testing Health Report...");
            var healthReport = await analyticsService.GetHealthReportAsync();
            Console.WriteLine($"   Overall Health: {healthReport.OverallHealth}");
            Console.WriteLine($"   Summary: {healthReport.Summary}");
            Console.WriteLine($"   Component Count: {healthReport.ComponentHealth.Count}");

            // Test 2: Performance Metrics
            Console.WriteLine("\n⚡ Testing Performance Metrics...");
            var performanceMetrics = await analyticsService.GetPerformanceMetricsAsync();
            Console.WriteLine($"   Avg Calculation Time: {performanceMetrics.AverageCalculationTime:F1}ms");
            Console.WriteLine($"   Value Stability: {performanceMetrics.ValueStability:F2}");

            // Test 3: Active Alerts
            Console.WriteLine("\n🚨 Testing Alert System...");
            var alerts = await analyticsService.GetActiveAlertsAsync();
            Console.WriteLine($"   Active Alerts: {alerts.Count}");

            // Test 4: Correlation Analysis
            Console.WriteLine("\n🔗 Testing Correlation Analysis...");
            var correlationAnalysis = await analyticsService.GetCorrelationAnalysisAsync();
            Console.WriteLine($"   Overall Correlation: {correlationAnalysis.OverallCorrelation:F3}");

            // Test 5: Component Accuracy
            Console.WriteLine("\n✅ Testing Component Accuracy...");
            var accuracyValidation = await analyticsService.ValidateComponentAccuracyAsync();
            Console.WriteLine($"   Accuracy Validation: {(accuracyValidation ? "PASSED" : "FAILED")}");

            Console.WriteLine("\n✅ Quick SyntheticVIX Analytics test completed successfully!");
            Console.WriteLine($"Test completed at: {DateTime.Now}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"\n❌ Test failed: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }

        Console.WriteLine("\nPress any key to continue...");
        Console.ReadKey();
    }
}
